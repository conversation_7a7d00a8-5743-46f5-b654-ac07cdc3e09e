import { Schema, model, Document } from 'mongoose';

export interface ITradeConfirmation extends Document {
  // Core Information
  confirmationId: string; // Unique confirmation identifier
  tradeId: string; // Reference to the trade
  discordId: string; // User who made the confirmation
  guildId: string; // Discord guild ID
  
  // Confirmation Details
  confirmationType: 'TRADE_ACCEPTANCE' | 'ITEM_RECEIVED' | 'TRADE_COMPLETION' | 'DISPUTE_ACKNOWLEDGMENT';
  confirmed: boolean; // Whether the user confirmed or denied
  
  // Timing
  confirmedAt: Date;
  
  // Additional Information
  notes?: string; // Optional notes from the user
  ipAddress?: string; // For audit purposes (if available)
  userAgent?: string; // For audit purposes (if available)
  
  // Metadata
  previousConfirmationId?: string; // If this is updating a previous confirmation
  isRevoked: boolean; // Whether this confirmation was revoked
  revokedAt?: Date;
  revokeReason?: string;
}

const tradeConfirmationSchema = new Schema<ITradeConfirmation>({
  confirmationId: {
    type: String,
    required: [true, 'Confirmation ID is required'],
    unique: true,
    index: true
  },
  tradeId: {
    type: String,
    required: [true, 'Trade ID is required'],
    validate: {
      validator: function(v: string): boolean {
        return v.trim().length > 0;
      },
      message: 'Trade ID cannot be empty'
    },
    index: true
  },
  discordId: {
    type: String,
    required: [true, 'Discord ID is required'],
    validate: {
      validator: function(v: string): boolean {
        return /^\d{17,20}$/.test(v);
      },
      message: 'Discord ID must be a valid Discord snowflake'
    },
    index: true
  },
  guildId: {
    type: String,
    required: [true, 'Guild ID is required'],
    validate: {
      validator: function(v: string): boolean {
        return /^\d{17,20}$/.test(v);
      },
      message: 'Guild ID must be a valid Discord snowflake'
    },
    index: true
  },
  confirmationType: {
    type: String,
    enum: ['TRADE_ACCEPTANCE', 'ITEM_RECEIVED', 'TRADE_COMPLETION', 'DISPUTE_ACKNOWLEDGMENT'],
    required: [true, 'Confirmation type is required'],
    index: true
  },
  confirmed: {
    type: Boolean,
    required: [true, 'Confirmation status is required'],
    index: true
  },
  confirmedAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  notes: {
    type: String,
    maxlength: [500, 'Notes cannot exceed 500 characters']
  },
  ipAddress: {
    type: String,
    validate: {
      validator: function(v: string): boolean {
        if (!v) return true;
        // Basic IP validation (IPv4 and IPv6)
        const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
        return ipv4Regex.test(v) || ipv6Regex.test(v);
      },
      message: 'Invalid IP address format'
    }
  },
  userAgent: {
    type: String,
    maxlength: [500, 'User agent cannot exceed 500 characters']
  },
  previousConfirmationId: {
    type: String,
    index: true
  },
  isRevoked: {
    type: Boolean,
    default: false,
    index: true
  },
  revokedAt: {
    type: Date
  },
  revokeReason: {
    type: String,
    maxlength: [200, 'Revoke reason cannot exceed 200 characters']
  }
}, {
  timestamps: false // Using custom timestamp fields
});

// Compound indexes for efficient queries
tradeConfirmationSchema.index({ tradeId: 1, confirmationType: 1, confirmed: 1 });
tradeConfirmationSchema.index({ discordId: 1, confirmedAt: -1 });
tradeConfirmationSchema.index({ guildId: 1, confirmedAt: -1 });
tradeConfirmationSchema.index({ confirmedAt: -1 });

// Method to check if confirmation is valid (not revoked)
tradeConfirmationSchema.methods.isValid = function(): boolean {
  return !this.isRevoked;
};

// Method to revoke confirmation
tradeConfirmationSchema.methods.revoke = function(reason?: string): void {
  this.isRevoked = true;
  this.revokedAt = new Date();
  if (reason) {
    this.revokeReason = reason;
  }
};

// Static method to get latest confirmation for a trade and user
tradeConfirmationSchema.statics.getLatestConfirmation = async function(
  tradeId: string, 
  discordId: string, 
  confirmationType: string
) {
  return await this.findOne({
    tradeId,
    discordId,
    confirmationType,
    isRevoked: false
  }).sort({ confirmedAt: -1 });
};

// Static method to check if both parties have confirmed
tradeConfirmationSchema.statics.checkBothPartiesConfirmed = async function(
  tradeId: string,
  sellerId: string,
  buyerId: string,
  confirmationType: string
): Promise<{ sellerConfirmed: boolean; buyerConfirmed: boolean; bothConfirmed: boolean }> {
  const sellerConfirmation = await this.getLatestConfirmation(tradeId, sellerId, confirmationType);
  const buyerConfirmation = await this.getLatestConfirmation(tradeId, buyerId, confirmationType);
  
  const sellerConfirmed = sellerConfirmation?.confirmed || false;
  const buyerConfirmed = buyerConfirmation?.confirmed || false;
  
  return {
    sellerConfirmed,
    buyerConfirmed,
    bothConfirmed: sellerConfirmed && buyerConfirmed
  };
};

// Static method to get confirmation history for a trade
tradeConfirmationSchema.statics.getTradeConfirmationHistory = async function(tradeId: string) {
  return await this.find({ tradeId })
    .sort({ confirmedAt: -1 })
    .lean();
};

export default model<ITradeConfirmation>('TradeConfirmation', tradeConfirmationSchema);
