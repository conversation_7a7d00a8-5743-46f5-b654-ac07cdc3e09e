"use strict";
/**
 * Formatting Utilities
 * Centralized formatting functions for consistent display
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormatUtils = exports.ProgressFormatter = exports.TextFormatter = exports.ListFormatter = exports.UserFormatter = exports.TimeFormatter = exports.CurrencyFormatter = void 0;
const constants_1 = require("../../config/constants");
/**
 * Currency formatting utilities
 */
class CurrencyFormatter {
    /**
     * Format coins with currency symbol and proper formatting
     */
    static formatCoins(amount) {
        if (typeof amount !== 'number' || isNaN(amount)) {
            return `0 ${constants_1.ECONOMY.CURRENCY_SYMBOL}`;
        }
        // Add thousand separators for large amounts
        const formattedAmount = amount.toLocaleString();
        return `${formattedAmount} ${constants_1.ECONOMY.CURRENCY_SYMBOL}`;
    }
    /**
     * Format coins with emoji
     */
    static formatCoinsWithEmoji(amount) {
        return `${constants_1.ECONOMY.CURRENCY_EMOJI} ${this.formatCoins(amount)}`;
    }
    /**
     * Format percentage
     */
    static formatPercentage(value, decimals = 1) {
        return `${value.toFixed(decimals)}%`;
    }
    /**
     * Format large numbers with suffixes (K, M, B)
     */
    static formatLargeNumber(num) {
        if (num < 1000)
            return num.toString();
        if (num < 1000000)
            return `${(num / 1000).toFixed(1)}K`;
        if (num < 1000000000)
            return `${(num / 1000000).toFixed(1)}M`;
        return `${(num / 1000000000).toFixed(1)}B`;
    }
}
exports.CurrencyFormatter = CurrencyFormatter;
/**
 * Time formatting utilities
 */
class TimeFormatter {
    /**
     * Format duration in milliseconds to human readable
     */
    static formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        if (days > 0)
            return `${days}d ${hours % 24}h`;
        if (hours > 0)
            return `${hours}h ${minutes % 60}m`;
        if (minutes > 0)
            return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    }
    /**
     * Format timestamp to relative time
     */
    static formatRelativeTime(date) {
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffSeconds = Math.floor(diffMs / 1000);
        const diffMinutes = Math.floor(diffSeconds / 60);
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);
        if (diffDays > 0)
            return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        if (diffHours > 0)
            return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        if (diffMinutes > 0)
            return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
        return 'Just now';
    }
    /**
     * Format date to Discord timestamp
     */
    static formatDiscordTimestamp(date, style = 'f') {
        const timestamp = Math.floor(date.getTime() / 1000);
        return `<t:${timestamp}:${style}>`;
    }
}
exports.TimeFormatter = TimeFormatter;
/**
 * User formatting utilities
 */
class UserFormatter {
    /**
     * Format user display name without mentions
     */
    static formatDisplayName(user) {
        if ('displayName' in user && typeof user.displayName === 'string') {
            return user.displayName;
        }
        if ('username' in user && typeof user.username === 'string') {
            return user.username;
        }
        return '';
    }
    /**
     * Format user tag (username#discriminator)
     */
    static formatUserTag(user) {
        return user.tag;
    }
    /**
     * Format user mention (for intentional pings)
     */
    static formatUserMention(user) {
        return `<@${user.id}>`;
    }
    /**
     * Format user info for embeds
     */
    static formatUserInfo(user, includeId = false) {
        let info = `**${user.username}**`;
        if (includeId) {
            info += `\nID: \`${user.id}\``;
        }
        return info;
    }
}
exports.UserFormatter = UserFormatter;
/**
 * List formatting utilities
 */
class ListFormatter {
    /**
     * Format array as numbered list
     */
    static formatNumberedList(items, startIndex = 1) {
        return items.map((item, index) => `${startIndex + index}. ${item}`).join('\n');
    }
    /**
     * Format array as bulleted list
     */
    static formatBulletedList(items, bullet = '•') {
        return items.map(item => `${bullet} ${item}`).join('\n');
    }
    /**
     * Format leaderboard entries
     */
    static formatLeaderboard(entries) {
        return entries.map(entry => {
            const medal = this.getRankMedal(entry.rank);
            return `${medal} **${entry.rank}.** ${entry.name} - ${entry.value}`;
        }).join('\n');
    }
    /**
     * Get medal emoji for rank
     */
    static getRankMedal(rank) {
        switch (rank) {
            case 1: return '🥇';
            case 2: return '🥈';
            case 3: return '🥉';
            default: return '🏅';
        }
    }
    /**
     * Format key-value pairs
     */
    static formatKeyValuePairs(pairs, separator = ': ') {
        return Object.entries(pairs)
            .map(([key, value]) => `**${key}**${separator}${value}`)
            .join('\n');
    }
}
exports.ListFormatter = ListFormatter;
/**
 * Text formatting utilities
 */
class TextFormatter {
    /**
     * Truncate text with ellipsis
     */
    static truncate(text, maxLength, suffix = '...') {
        if (text.length <= maxLength)
            return text;
        return text.substring(0, maxLength - suffix.length) + suffix;
    }
    /**
     * Capitalize first letter
     */
    static capitalize(text) {
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
    }
    /**
     * Convert to title case
     */
    static toTitleCase(text) {
        return text.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
    }
    /**
     * Format code block
     */
    static formatCodeBlock(code, language = '') {
        return `\`\`\`${language}\n${code}\n\`\`\``;
    }
    /**
     * Format inline code
     */
    static formatInlineCode(code) {
        return `\`${code}\``;
    }
    /**
     * Format bold text
     */
    static formatBold(text) {
        return `**${text}**`;
    }
    /**
     * Format italic text
     */
    static formatItalic(text) {
        return `*${text}*`;
    }
    /**
     * Format strikethrough text
     */
    static formatStrikethrough(text) {
        return `~~${text}~~`;
    }
    /**
     * Format spoiler text
     */
    static formatSpoiler(text) {
        return `||${text}||`;
    }
}
exports.TextFormatter = TextFormatter;
/**
 * Progress formatting utilities
 */
class ProgressFormatter {
    /**
     * Create progress bar
     */
    static createProgressBar(current, max, length = 10, filledChar = '█', emptyChar = '░') {
        const percentage = Math.max(0, Math.min(1, current / max));
        const filledLength = Math.round(length * percentage);
        const emptyLength = length - filledLength;
        return filledChar.repeat(filledLength) + emptyChar.repeat(emptyLength);
    }
    /**
     * Format progress with percentage
     */
    static formatProgress(current, max) {
        const percentage = Math.max(0, Math.min(100, (current / max) * 100));
        const progressBar = this.createProgressBar(current, max);
        return `${progressBar} ${percentage.toFixed(1)}%`;
    }
}
exports.ProgressFormatter = ProgressFormatter;
/**
 * Comprehensive formatting utility
 */
class FormatUtils {
}
exports.FormatUtils = FormatUtils;
FormatUtils.currency = CurrencyFormatter;
FormatUtils.time = TimeFormatter;
FormatUtils.user = UserFormatter;
FormatUtils.list = ListFormatter;
FormatUtils.text = TextFormatter;
FormatUtils.progress = ProgressFormatter;
/**
 * Format coins (shorthand)
 */
FormatUtils.formatCoins = CurrencyFormatter.formatCoins;
/**
 * Format user display name (shorthand)
 */
FormatUtils.formatDisplayName = UserFormatter.formatDisplayName;
/**
 * Format relative time (shorthand)
 */
FormatUtils.formatRelativeTime = TimeFormatter.formatRelativeTime;
exports.default = FormatUtils;
//# sourceMappingURL=FormatUtils.js.map