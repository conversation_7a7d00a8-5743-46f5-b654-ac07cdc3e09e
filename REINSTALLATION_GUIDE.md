# Clean Reinstallation Guide

This guide provides step-by-step instructions for reinstalling Node.js and rebuilding your Discord bot project after cleanup.

## 🎯 Prerequisites

Before starting:
- ✅ Completed cleanup steps from CLEANUP_INSTRUCTIONS.md
- ✅ Backed up all source code and configuration files
- ✅ Have your environment variables ready (.env file)
- ✅ Have your Discord bot token and client ID available

## 🔧 Option 1: Local Development Setup

### Step 1: Install Node.js

#### Windows
1. Go to https://nodejs.org/
2. Download the LTS version (Long Term Support)
3. Run the installer (.msi file)
4. Follow installation wizard (accept defaults)
5. Restart your computer

#### macOS
1. Go to https://nodejs.org/
2. Download the LTS version
3. Run the installer (.pkg file)
4. Follow installation wizard
5. Restart terminal

#### Linux (Ubuntu/Debian)
```bash
# Update package index
sudo apt update

# Install Node.js LTS
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### Step 2: Verify Installation
```bash
# Check Node.js version (should be 18.x or higher)
node --version

# Check npm version (should be 9.x or higher)
npm --version

# Check npm configuration
npm config list
```

### Step 3: Navigate to Project Directory
```bash
# Navigate to your bot project directory
cd "path/to/your/bot/project"

# Verify you're in the right directory
ls -la
# Should see: src/, package.json, tsconfig.json, etc.
```

### Step 4: Install Dependencies
```bash
# Install all dependencies from package.json
npm install

# This will:
# - Create node_modules/ directory
# - Install all dependencies and devDependencies
# - Create package-lock.json
```

### Step 5: Build the Project
```bash
# Compile TypeScript to JavaScript
npm run build

# Verify build succeeded
ls dist/
# Should see compiled .js files
```

### Step 6: Deploy Commands Manually
```bash
# Deploy slash commands to Discord
npm run deploy-commands

# You should see output like:
# ✅ Loaded legacy command: balance
# ✅ Loaded new command: trade
# ✅ Successfully reloaded X application (/) commands
```

### Step 7: Test the Bot
```bash
# Start the bot in development mode
npm run start:dev

# You should see:
# [Main] Economy Bot started successfully!
# [Main] Bot is ready as YourBotName#1234
```

## 🚀 Option 2: Discloud-Only Deployment

If you prefer not to install Node.js locally:

### Step 1: Prepare Project Files
Ensure your project directory contains:
```
your-bot-project/
├── src/                    # Source code
├── scripts/               # Deployment scripts
├── migrations/            # Database migrations
├── tests/                 # Test files
├── package.json           # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
├── discloud.config        # Discloud configuration
├── jest.config.js         # Test configuration
├── .env                   # Environment variables
└── *.md                   # Documentation files
```

### Step 2: Verify package.json
Your package.json should look like this:
```json
{
  "name": "economy-bot",
  "version": "2.0.0",
  "main": "dist/main.js",
  "scripts": {
    "start": "node dist/main.js",
    "build": "tsc"
  },
  "dependencies": {
    "@discordjs/rest": "^2.5.0",
    "discord-api-types": "^0.38.8",
    "discord.js": "^14.19.3",
    "dotenv": "^16.6.1",
    "mongodb": "^6.17.0",
    "mongoose": "^8.15.0",
    "node-cron": "^4.0.7",
    "winston": "^3.17.0"
  },
  "devDependencies": {
    "@types/node": "^22.15.0",
    "ts-node": "^10.9.2",
    "typescript": "^5.8.3"
  }
}
```

### Step 3: Verify discloud.config
```
NAME=Banker_Of_The_Phalanx
AVATAR=https://i.ibb.co/Df75TTGX/Uma60.png
TYPE=bot
MAIN=dist/main.js
RAM=924
AUTORESTART=false
VERSION=latest
APT=
START=npm start
ID=*************
```

### Step 4: Create Deployment Package
1. **DO NOT INCLUDE**:
   - node_modules/
   - dist/
   - package-lock.json
   - yarn.lock
   - .npm/
   - .cache/

2. **INCLUDE**:
   - All source code (src/)
   - Configuration files
   - Documentation files

### Step 5: Upload to Discloud
1. Create a zip file with the clean project
2. Upload to Discloud
3. Set environment variables in Discloud dashboard:
   - `BOT_TOKEN`
   - `CLIENT_ID`
   - `MONGODB_URI`

## 🔍 Verification Steps

### For Local Development
```bash
# 1. Check installation
node --version && npm --version

# 2. Check dependencies
npm list --depth=0

# 3. Check build
npm run build && ls dist/

# 4. Check commands deployment
npm run deploy-commands

# 5. Test bot startup
npm run start:dev
```

### For Discloud Deployment
1. Check Discloud deployment logs
2. Verify bot shows as online in Discord
3. Test a basic command like `/balance`
4. Check for any error messages in logs

## 🚨 Troubleshooting

### Local Development Issues

#### "node: command not found"
```bash
# Node.js not installed or not in PATH
# Solution: Reinstall Node.js and restart terminal
```

#### "npm install" fails with permission errors
```bash
# On macOS/Linux:
sudo chown -R $(whoami) ~/.npm

# On Windows: Run as Administrator
```

#### TypeScript compilation errors
```bash
# Check TypeScript installation
npm list typescript

# Reinstall if needed
npm install --save-dev typescript

# Check tsconfig.json for syntax errors
```

#### "Cannot find module" errors
```bash
# Clean reinstall
rm -rf node_modules package-lock.json
npm install
```

### Discloud Deployment Issues

#### "Error 504: package.json error"
- Check package.json syntax with a JSON validator
- Ensure no trailing commas
- Verify all dependency versions are valid

#### Bot doesn't start
- Check environment variables are set correctly
- Verify MAIN points to correct file (dist/main.js)
- Check Discloud logs for specific error messages

#### Commands not working
- Verify BOT_TOKEN and CLIENT_ID are correct
- Check bot has proper permissions in Discord server
- Ensure bot has applications.commands scope

## 📋 Post-Installation Checklist

### Local Development
- [ ] Node.js installed and working
- [ ] npm install completed successfully
- [ ] TypeScript compilation works (npm run build)
- [ ] Commands deploy successfully
- [ ] Bot starts and connects to Discord
- [ ] Basic commands work (test /balance)
- [ ] MongoDB connection established

### Discloud Deployment
- [ ] Clean project files uploaded
- [ ] Environment variables set in dashboard
- [ ] Bot deploys without errors
- [ ] Bot appears online in Discord
- [ ] Commands are registered and working
- [ ] Database connection established
- [ ] No error messages in logs

## 🔄 Next Steps

### After Successful Basic Deployment
1. **Test all functionality** thoroughly
2. **Document any remaining issues**
3. **Consider re-enabling auto-deployment** (refer to DEPLOYMENT_CONFIGURATION_BACKUP.md)
4. **Set up monitoring** for production deployment

### Re-enabling Auto-Deployment (Optional)
Once basic deployment works:
1. Uncomment auto-deployment code in src/main.ts
2. Update package.json to include TypeScript in dependencies
3. Update discloud.config to use src/main.ts
4. Test locally before redeploying

## 📞 Support Resources

### Documentation Files Created
- `DEPLOYMENT_CONFIGURATION_BACKUP.md` - All auto-deployment configurations
- `CLEANUP_INSTRUCTIONS.md` - Cleanup procedures
- `SELF_CONTAINED_DEPLOYMENT.md` - Auto-deployment system documentation

### Useful Commands
```bash
# Check versions
node --version && npm --version

# Clean install
rm -rf node_modules package-lock.json && npm install

# Build and test
npm run build && npm run deploy-commands && npm run start:dev

# View logs (local)
tail -f logs/combined.log

# Test specific functionality
npm test
```

### Getting Help
1. Check Discloud documentation
2. Review Discord.js documentation
3. Check MongoDB connection issues
4. Verify environment variables are correct
5. Test with minimal configuration first
