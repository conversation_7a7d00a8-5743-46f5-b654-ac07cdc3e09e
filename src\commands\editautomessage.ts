import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits, ChannelType, Role, StringSelectMenuBuilder, ActionRowBuilder, ComponentType } from 'discord.js';
import { withErrorHandler, ValidationError, PermissionError, DatabaseError } from '../utils/errorHandler';
import { createSuccessEmbed, createErrorEmbed, createAdminEmbed, EMOJIS } from '../utils/embedBuilder';
import { WelcomeTemplate } from '../models/WelcomeTemplate';
import { resolveRole } from '../utils/roleResolver';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('editautomessage')
        .setDescription('Edit an existing automated message (admin only)')
        .addStringOption(option =>
            option.setName('name')
                .setDescription('Name of the message template to edit')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('trigger')
                .setDescription('New event that triggers the message')
                .setRequired(false)
                .addChoices(
                    { name: 'Member Join', value: 'member_join' },
                    { name: 'Role Added', value: 'role_add' },
                    { name: 'Role Removed', value: 'role_remove' }
                )
        )
        .addStringOption(option =>
            option.setName('delivery')
                .setDescription('New delivery method')
                .setRequired(false)
                .addChoices(
                    { name: 'Direct Message', value: 'dm' },
                    { name: 'Channel', value: 'channel' },
                    { name: 'Both DM and Channel', value: 'both' }
                )
        )
        .addRoleOption(option =>
            option.setName('role')
                .setDescription('New specific role for role_add/role_remove triggers')
                .setRequired(false)
        )
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('New channel to send messages to')
                .setRequired(false)
                .addChannelTypes(ChannelType.GuildText)
        )
        .addStringOption(option =>
            option.setName('title')
                .setDescription('New title for the embed message (max 256 characters)')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('description')
                .setDescription('New main content of the message (max 4000 characters)')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('image')
                .setDescription('New URL to an image to display in the embed')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('color')
                .setDescription('New hex color code for the embed (e.g., #dd7d00)')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('buttons')
                .setDescription('New buttons in format: Name1|URL1 Name2|URL2 (max 5 buttons)')
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('embed')
                .setDescription('Send as embed (true) or plain text (false)')
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('enabled')
                .setDescription('Enable or disable the message')
                .setRequired(false)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Check permissions
        if (!interaction.guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError('You need Administrator permissions to use this command.');
        }

        const templateName = interaction.options.getString('name');

        try {
            if (!templateName) {
                // Show selection menu if no name provided
                await showTemplateSelection(interaction);
            } else {
                // Edit the specified template
                await handleEdit(interaction, templateName);
            }
        } catch (error) {
            if (error instanceof ValidationError || error instanceof PermissionError || error instanceof DatabaseError) {
                throw error;
            }
            throw new DatabaseError('An unexpected error occurred while editing the automessage.');
        }
    })
};

/**
 * Shows a selection menu for choosing which template to edit
 */
async function showTemplateSelection(interaction: ChatInputCommandInteraction): Promise<void> {
    const templates = await WelcomeTemplate.find({
        guildId: interaction.guild!.id
    }).sort({ name: 1 });

    if (templates.length === 0) {
        const embed = createAdminEmbed('No Automated Messages')
            .setDescription(`${EMOJIS.ADMIN.INFO} **No Messages Found**\n\nYou haven't created any automated messages yet.`)
            .addFields({
                name: `${EMOJIS.MISC.LIGHTBULB} Getting Started`,
                value:
                    `Use \`/automessage action:create\` to create your first automated message.\n\n` +
                    `**Example:**\n` +
                    `\`/automessage action:create trigger:member_join delivery:channel name:welcome title:Welcome! description:Hello {user}!\``,
                inline: false
            });

        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }

    // Create selection menu
    const options = templates.slice(0, 25).map(template => ({
        label: template.name,
        value: template.name,
        description: `${template.triggerType} • ${template.deliveryType} • ${template.enabled ? 'Enabled' : 'Disabled'}`,
        emoji: getTriggerEmoji(template.triggerType === 'join' ? 'member_join' : template.triggerType)
    }));

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('edit_automessage_select')
        .setPlaceholder('Choose a message template to edit...')
        .addOptions(options);

    const row = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

    const embed = createAdminEmbed('Edit Automated Message')
        .setDescription(`${EMOJIS.ADMIN.SETTINGS} **Select Message to Edit**\n\nChoose which automated message you want to modify from the dropdown below.`)
        .addFields({
            name: `${EMOJIS.ADMIN.INFO} Available Templates`,
            value: `Found ${templates.length} automated message(s) in this server.`,
            inline: false
        });

    await interaction.reply({
        embeds: [embed],
        components: [row],
        ephemeral: true
    });

    // Wait for selection
    try {
        const response = await interaction.followUp({
            content: 'Waiting for your selection...',
            ephemeral: true
        });

        const collector = response.createMessageComponentCollector({
            componentType: ComponentType.StringSelect,
            time: 60000
        });

        collector.on('collect', async (selectInteraction) => {
            if (selectInteraction.user.id !== interaction.user.id) {
                await selectInteraction.reply({
                    content: 'Only the command user can make this selection.',
                    ephemeral: true
                });
                return;
            }

            const selectedName = selectInteraction.values[0];
            await selectInteraction.deferUpdate();
            
            // Show edit form for selected template
            await showEditForm(selectInteraction, selectedName);
        });

        collector.on('end', async (collected) => {
            if (collected.size === 0) {
                await interaction.editReply({
                    content: 'Selection timed out. Please run the command again.',
                    components: []
                });
            }
        });
    } catch (error) {
        throw new DatabaseError('Failed to create template selection interface.');
    }
}

/**
 * Shows the edit form for a specific template
 */
async function showEditForm(interaction: any, templateName: string): Promise<void> {
    const template = await WelcomeTemplate.findOne({
        guildId: interaction.guild!.id,
        name: templateName
    });

    if (!template) {
        throw new ValidationError(`No message template found with the name "${templateName}".`);
    }

    const embed = createAdminEmbed('Edit Automated Message')
        .setDescription(`${EMOJIS.ADMIN.SETTINGS} **Editing: ${template.name}**\n\nTo edit this message, use the \`/editautomessage\` command with the specific parameters you want to change.`)
        .addFields(
            {
                name: `${EMOJIS.ADMIN.INFO} Current Configuration`,
                value:
                    `**Name:** ${template.name}\n` +
                    `**Trigger:** ${getTriggerDisplayName(template.triggerType === 'join' ? 'member_join' : template.triggerType)}\n` +
                    `**Delivery:** ${template.deliveryType}\n` +
                    `**Format:** ${template.useEmbed ? 'Embed' : 'Plain Text'}\n` +
                    `**Status:** ${template.enabled ? 'Enabled' : 'Disabled'}`,
                inline: false
            },
            {
                name: `${EMOJIS.MISC.LIGHTBULB} How to Edit`,
                value:
                    `Use: \`/editautomessage name:${template.name} [parameter]:[new_value]\`\n\n` +
                    `**Available Parameters:**\n` +
                    `• \`trigger\` - Change trigger type\n` +
                    `• \`delivery\` - Change delivery method\n` +
                    `• \`title\` - Change embed title\n` +
                    `• \`description\` - Change message content\n` +
                    `• \`embed\` - Toggle embed/plain text\n` +
                    `• \`enabled\` - Enable/disable message\n` +
                    `• And more...`,
                inline: false
            }
        );

    await interaction.editReply({
        embeds: [embed],
        components: []
    });
}

/**
 * Handles editing a specific template
 */
async function handleEdit(interaction: ChatInputCommandInteraction, templateName: string): Promise<void> {
    const template = await WelcomeTemplate.findOne({
        guildId: interaction.guild!.id,
        name: templateName
    });

    if (!template) {
        throw new ValidationError(`No message template found with the name "${templateName}".`);
    }

    // Get all the new values
    const newTrigger = interaction.options.getString('trigger');
    const newDelivery = interaction.options.getString('delivery');
    const newRole = interaction.options.getRole('role') as Role | null;
    const newChannel = interaction.options.getChannel('channel');
    const newTitle = interaction.options.getString('title');
    const newDescription = interaction.options.getString('description');
    const newImage = interaction.options.getString('image');
    const newColor = interaction.options.getString('color');
    const newButtons = interaction.options.getString('buttons');
    const newUseEmbed = interaction.options.getBoolean('embed');
    const newEnabled = interaction.options.getBoolean('enabled');

    // Build update object
    const update: Record<string, any> = {};
    let hasChanges = false;

    // Validate and apply trigger changes
    if (newTrigger) {
        const triggerTypeMap: { [key: string]: string } = {
            'member_join': 'join',
            'role_add': 'role_add',
            'role_remove': 'role_remove'
        };
        update.triggerType = triggerTypeMap[newTrigger];
        hasChanges = true;

        // Validate role requirement for role triggers
        if ((newTrigger === 'role_add' || newTrigger === 'role_remove') && !newRole && !template.triggerRoleId) {
            throw new ValidationError('A specific role must be selected for role_add and role_remove triggers.');
        }
    }

    // Apply role changes
    if (newRole) {
        update.triggerRoleId = newRole.id;
        hasChanges = true;
    }

    // Validate and apply delivery changes
    if (newDelivery) {
        const deliveryTypeMap: { [key: string]: string } = {
            'dm': 'dm',
            'channel': 'channel',
            'both': 'both'
        };
        update.deliveryType = deliveryTypeMap[newDelivery];
        hasChanges = true;

        // Validate channel requirement for channel delivery
        if ((newDelivery === 'channel' || newDelivery === 'both') && !newChannel && !template.channelId) {
            throw new ValidationError('A channel must be selected for channel or both delivery methods.');
        }
    }

    // Apply channel changes
    if (newChannel) {
        if (newChannel.type !== ChannelType.GuildText) {
            throw new ValidationError('The specified channel must be a text channel.');
        }
        update.channelId = newChannel.id;
        hasChanges = true;
    }

    // Apply embed format changes
    if (newUseEmbed !== null) {
        update.useEmbed = newUseEmbed;
        hasChanges = true;
    }

    // Apply content changes with validation
    if (newTitle !== null) {
        if (newTitle && newTitle.length > 256) {
            throw new ValidationError('Title cannot exceed 256 characters.');
        }
        update.title = newTitle || undefined;
        hasChanges = true;
    }

    if (newDescription !== null) {
        if (newDescription && newDescription.length > 4000) {
            throw new ValidationError('Description cannot exceed 4000 characters.');
        }
        update.description = newDescription || undefined;
        hasChanges = true;
    }

    // Validate content requirements based on format
    const finalUseEmbed = newUseEmbed !== null ? newUseEmbed : template.useEmbed;
    const finalTitle = newTitle !== null ? newTitle : template.title;
    const finalDescription = newDescription !== null ? newDescription : template.description;

    if (finalUseEmbed) {
        if (!finalTitle && !finalDescription) {
            throw new ValidationError('Either title or description must be provided for embed messages.');
        }
    } else {
        if (!finalDescription) {
            throw new ValidationError('Description is required for plain text messages.');
        }
    }

    // Apply color changes
    if (newColor !== null) {
        if (newColor && !/^#[0-9A-Fa-f]{6}$/.test(newColor)) {
            throw new ValidationError('Color must be a valid hex code (e.g., #dd7d00).');
        }
        update.color = newColor || undefined;
        hasChanges = true;
    }

    // Apply image changes
    if (newImage !== null) {
        if (newImage && !isValidUrl(newImage)) {
            throw new ValidationError('Image must be a valid URL.');
        }
        update.imageUrl = newImage || undefined;
        hasChanges = true;
    }

    // Apply button changes
    if (newButtons !== null) {
        const parsedButtons = parseButtons(newButtons);
        update.buttons = parsedButtons;
        hasChanges = true;
    }

    // Apply enabled status changes
    if (newEnabled !== null) {
        update.enabled = newEnabled;
        hasChanges = true;
    }

    // Check if there are any changes
    if (!hasChanges) {
        throw new ValidationError('No changes specified. Please provide at least one field to update.');
    }

    // Update the template
    const updatedTemplate = await WelcomeTemplate.findByIdAndUpdate(
        template._id,
        update,
        { new: true }
    );

    if (!updatedTemplate) {
        throw new DatabaseError('Failed to update the message template.');
    }

    // Create success response
    const embed = createSuccessEmbed('Automated Message Updated')
        .setDescription(`${EMOJIS.SUCCESS.PARTY} **Message Updated!**\n\nYour automated message "${templateName}" has been updated successfully.`)
        .addFields(
            {
                name: `${EMOJIS.ADMIN.SETTINGS} Updated Configuration`,
                value:
                    `**Name:** ${updatedTemplate.name}\n` +
                    `**Trigger:** ${getTriggerDisplayName(updatedTemplate.triggerType === 'join' ? 'member_join' : updatedTemplate.triggerType)}\n` +
                    `**Delivery:** ${updatedTemplate.deliveryType}\n` +
                    `**Format:** ${updatedTemplate.useEmbed ? 'Embed' : 'Plain Text'}\n` +
                    `**Status:** ${updatedTemplate.enabled ? 'Enabled' : 'Disabled'}`,
                inline: false
            },
            {
                name: `${EMOJIS.MISC.LIGHTBULB} Next Steps`,
                value:
                    `• Use \`/automessage action:test name:${templateName}\` to preview the updated message\n` +
                    `• Use \`/automessage action:list\` to see all your messages`,
                inline: false
            }
        );

    await interaction.reply({ embeds: [embed], ephemeral: false });
}

/**
 * Utility function to validate URLs
 */
function isValidUrl(string: string): boolean {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

/**
 * Utility function to parse button strings
 */
function parseButtons(buttonString: string | null): any[] {
    if (!buttonString) return [];

    const buttons: any[] = [];
    const buttonPairs = buttonString.split(' ');

    for (const pair of buttonPairs) {
        if (buttons.length >= 5) {
            throw new ValidationError('Maximum of 5 buttons allowed.');
        }

        const [name, url] = pair.split('|');
        if (!name || !url) {
            throw new ValidationError('Button format must be: Name|URL (e.g., Discord|https://discord.com)');
        }

        if (name.length > 80) {
            throw new ValidationError('Button names cannot exceed 80 characters.');
        }

        if (!isValidUrl(url)) {
            throw new ValidationError(`Invalid URL for button "${name}": ${url}`);
        }

        buttons.push({
            label: name,
            url: url,
            style: 'Link'
        });
    }

    return buttons;
}

/**
 * Utility function to get display name for triggers
 */
function getTriggerDisplayName(trigger: string): string {
    switch (trigger) {
        case 'member_join':
            return 'Member Join';
        case 'role_add':
            return 'Role Added';
        case 'role_remove':
            return 'Role Removed';
        default:
            return trigger;
    }
}

/**
 * Utility function to get emoji for trigger types
 */
function getTriggerEmoji(trigger: string): string {
    switch (trigger) {
        case 'member_join':
            return '👋';
        case 'role_add':
            return '🎭';
        case 'role_remove':
            return '🗑️';
        default:
            return '📝';
    }
}
