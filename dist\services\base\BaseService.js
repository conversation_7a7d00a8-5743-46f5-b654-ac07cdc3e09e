"use strict";
/**
 * Base Service Class
 * Abstract base class for all application services
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceRegistry = exports.ServiceRegistry = exports.BaseService = void 0;
const logger_1 = require("../../core/logger");
/**
 * Abstract base service class
 */
class BaseService {
    constructor(app) {
        this.app = app;
        this.logger = (0, logger_1.createLogger)(`service:${this.constructor.name}`);
    }
    /**
     * Initialize the service
     */
    async initialize() {
        this.logger.info(`[${this.name}] Service initializing...`);
        await this.onInitialize();
        this.logger.info(`[${this.name}] Service initialized`);
    }
    /**
     * Shutdown the service
     */
    async shutdown() {
        this.logger.info(`[${this.name}] Service shutting down...`);
        await this.onShutdown();
        this.logger.info(`[${this.name}] Service shutdown complete`);
    }
    /**
     * Override in subclasses for initialization logic
     */
    async onInitialize() {
        // Default implementation - override in subclasses
    }
    /**
     * Override in subclasses for shutdown logic
     */
    async onShutdown() {
        // Default implementation - override in subclasses
    }
    /**
     * Check if a feature is enabled
     */
    isFeatureEnabled(featureName) {
        // Import here to avoid circular dependencies
        const { isFeatureActive } = require('../../config');
        return isFeatureActive(featureName);
    }
    /**
     * Get another service from the application context
     */
    getService(serviceName) {
        if (!this.app) {
            throw new Error(`Cannot get service ${serviceName}: No application context available`);
        }
        return this.app.getService(serviceName);
    }
    /**
     * Handle service errors with consistent logging
     */
    handleError(error, context) {
        this.logger.error(`[${this.name}] Service error`, {
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
            } : error,
            context,
            serviceName: this.name,
        });
    }
    /**
     * Log service operations
     */
    logOperation(operation, details) {
        this.logger.debug(`[${this.name}] ${operation}`, details);
    }
    /**
     * Validate required dependencies
     */
    validateDependencies(dependencies) {
        if (!this.app) {
            throw new Error(`Cannot validate dependencies: No application context available`);
        }
        for (const dependency of dependencies) {
            try {
                this.app.getService(dependency);
            }
            catch (error) {
                throw new Error(`Required dependency not available: ${dependency}`);
            }
        }
    }
    /**
     * Create a child logger with additional context
     */
    createChildLogger(context) {
        return (0, logger_1.createLogger)(`${this.name}:${context}`);
    }
}
exports.BaseService = BaseService;
/**
 * Service registry for managing service instances
 */
class ServiceRegistry {
    constructor() {
        this.services = new Map();
        this.logger = (0, logger_1.createLogger)('service-registry');
    }
    /**
     * Register a service
     */
    register(service) {
        if (this.services.has(service.name)) {
            this.logger.warn(`[ServiceRegistry] Service already registered: ${service.name}`);
            return;
        }
        this.services.set(service.name, service);
        this.logger.debug(`[ServiceRegistry] Registered service: ${service.name}`);
    }
    /**
     * Get a service by name
     */
    get(name) {
        const service = this.services.get(name);
        if (!service) {
            throw new Error(`Service not found: ${name}`);
        }
        return service;
    }
    /**
     * Check if a service is registered
     */
    has(name) {
        return this.services.has(name);
    }
    /**
     * Get all registered services
     */
    getAll() {
        return Array.from(this.services.values());
    }
    /**
     * Unregister a service
     */
    unregister(name) {
        const removed = this.services.delete(name);
        if (removed) {
            this.logger.debug(`[ServiceRegistry] Unregistered service: ${name}`);
        }
        return removed;
    }
    /**
     * Clear all services
     */
    clear() {
        this.services.clear();
        this.logger.debug('[ServiceRegistry] Cleared all services');
    }
}
exports.ServiceRegistry = ServiceRegistry;
/**
 * Global service registry instance
 */
exports.serviceRegistry = new ServiceRegistry();
//# sourceMappingURL=BaseService.js.map