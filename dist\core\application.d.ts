/**
 * Application Bootstrap and Context
 * Main application class that manages services, client, and lifecycle
 */
import { Client } from 'discord.js';
import { IApplicationContext, IService, ILogger, ServiceOptions } from './interfaces';
/**
 * Main application class
 */
export declare class Application implements IApplicationContext {
    readonly client: Client;
    readonly logger: ILogger;
    readonly services: Map<string, IService>;
    private serviceRegistrations;
    private isShuttingDown;
    constructor();
    /**
     * Create Discord client with proper configuration
     */
    private createDiscordClient;
    /**
     * Initialize the application
     */
    initialize(): Promise<void>;
    /**
     * Register core services
     */
    private registerCoreServices;
    /**
     * Register a service
     */
    registerService(service: IService, options?: ServiceOptions): void;
    /**
     * Get a service by name
     */
    getService<T extends IService>(name: string): T;
    /**
     * Initialize services in dependency order
     */
    private initializeServices;
    /**
     * Initialize a single service
     */
    private initializeService;
    /**
     * Connect to Discord
     */
    private connectToDiscord;
    /**
     * Setup graceful shutdown handlers
     */
    private setupGracefulShutdown;
    /**
     * Shutdown the application
     */
    shutdown(): Promise<void>;
    /**
     * Get application health status
     */
    getHealthStatus(): Promise<any>;
}
/**
 * Get or create global application instance
 */
export declare function getApplication(): Application;
/**
 * Initialize and start the application
 */
export declare function startApplication(): Promise<Application>;
export default Application;
//# sourceMappingURL=application.d.ts.map