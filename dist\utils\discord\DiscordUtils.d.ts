/**
 * Discord Utilities
 * Helper functions for Discord-specific operations
 */
import { Client, Guild, GuildMember, Role, TextChannel, EmbedBuilder, ButtonBuilder, ActionRowBuilder, ButtonStyle, PermissionFlagsBits } from 'discord.js';
/**
 * Guild utilities
 */
export declare class GuildUtils {
    /**
     * Safely fetch guild by ID
     */
    static fetchGuild(client: Client, guildId: string): Promise<Guild | null>;
    /**
     * Check if guild exists and bot has access
     */
    static validateGuildAccess(client: Client, guildId: string): Promise<Guild>;
    /**
     * Get guild member count
     */
    static getMemberCount(guild: Guild): Promise<number>;
    /**
     * Check if guild has specific features
     */
    static hasFeature(guild: Guild, feature: string): boolean;
}
/**
 * Member utilities
 */
export declare class MemberUtils {
    /**
     * Safely fetch member by ID
     */
    static fetchMember(guild: Guild, userId: string): Promise<GuildMember | null>;
    /**
     * Check if member exists in guild
     */
    static validateMemberAccess(guild: Guild, userId: string): Promise<GuildMember>;
    /**
     * Check if member has specific permission
     */
    static hasPermission(member: GuildMember, permission: keyof typeof PermissionFlagsBits): boolean;
    /**
     * Check if member has any of the specified permissions
     */
    static hasAnyPermission(member: GuildMember, permissions: (keyof typeof PermissionFlagsBits)[]): boolean;
    /**
     * Check if member has admin permissions
     */
    static isAdmin(member: GuildMember): boolean;
    /**
     * Get member's highest role
     */
    static getHighestRole(member: GuildMember): Role | null;
    /**
     * Check if member can manage another member
     */
    static canManageMember(manager: GuildMember, target: GuildMember): boolean;
}
/**
 * Role utilities
 */
export declare class RoleUtils {
    /**
     * Safely fetch role by ID
     */
    static fetchRole(guild: Guild, roleId: string): Promise<Role | null>;
    /**
     * Find role by name (case-insensitive)
     */
    static findRoleByName(guild: Guild, roleName: string): Role | null;
    /**
     * Check if role can be managed by bot
     */
    static canManageRole(guild: Guild, role: Role): boolean;
    /**
     * Get manageable roles for bot
     */
    static getManageableRoles(guild: Guild): Role[];
}
/**
 * Channel utilities
 */
export declare class ChannelUtils {
    /**
     * Safely fetch channel by ID
     */
    static fetchChannel(client: Client, channelId: string): Promise<TextChannel | null>;
    /**
     * Check if bot can send messages in channel
     */
    static canSendMessages(channel: TextChannel): boolean;
    /**
     * Check if bot can embed links in channel
     */
    static canEmbedLinks(channel: TextChannel): boolean;
    /**
     * Send message with error handling
     */
    static safeSendMessage(channel: TextChannel, content: any): Promise<boolean>;
}
/**
 * Embed utilities
 */
export declare class EmbedUtils {
    /**
     * Validate embed limits
     */
    static validateEmbed(embed: EmbedBuilder): void;
    /**
     * Truncate embed field value if too long
     */
    static truncateFieldValue(value: string, maxLength?: number): string;
    /**
     * Split long text into multiple embed fields
     */
    static splitIntoFields(text: string, fieldName: string, maxLength?: number): Array<{
        name: string;
        value: string;
    }>;
}
/**
 * Button utilities
 */
export declare class ButtonUtils {
    /**
     * Create action row with buttons
     */
    static createActionRow(buttons: ButtonBuilder[]): ActionRowBuilder<ButtonBuilder>;
    /**
     * Create simple button
     */
    static createButton(customId: string, label: string, style?: ButtonStyle, emoji?: string): ButtonBuilder;
    /**
     * Create link button
     */
    static createLinkButton(url: string, label: string, emoji?: string): ButtonBuilder;
}
/**
 * Comprehensive Discord utility
 */
export declare class DiscordUtils {
    static guild: typeof GuildUtils;
    static member: typeof MemberUtils;
    static role: typeof RoleUtils;
    static channel: typeof ChannelUtils;
    static embed: typeof EmbedUtils;
    static button: typeof ButtonUtils;
    /**
     * Parse Discord ID from mention or return as-is
     */
    static parseId(input: string): string;
    /**
     * Check if string is a Discord ID
     */
    static isDiscordId(input: string): boolean;
    /**
     * Format user mention
     */
    static formatUserMention(userId: string): string;
    /**
     * Format role mention
     */
    static formatRoleMention(roleId: string): string;
    /**
     * Format channel mention
     */
    static formatChannelMention(channelId: string): string;
}
export default DiscordUtils;
//# sourceMappingURL=DiscordUtils.d.ts.map