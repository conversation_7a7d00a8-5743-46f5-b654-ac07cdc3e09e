import { Document } from 'mongoose';
export type TriggerType = 'join' | 'role_add' | 'role_remove';
export type DeliveryType = 'dm' | 'channel' | 'both';
export interface IWelcomeField {
    name: string;
    value: string;
    inline: boolean;
}
export interface IWelcomeButton {
    label: string;
    style: 'Link';
    url: string;
}
export interface IWelcomeTemplate extends Document {
    guildId: string;
    name: string;
    triggerType: TriggerType;
    triggerRoleId?: string;
    deliveryType: DeliveryType;
    channelId?: string;
    useEmbed: boolean;
    title?: string;
    description?: string;
    color?: string;
    thumbnailUrl?: string;
    imageUrl?: string;
    footerText?: string;
    showTimestamp: boolean;
    fields: IWelcomeField[];
    buttons: IWelcomeButton[];
    delaySeconds: number;
    enabled: boolean;
    priority: number;
    conditions?: {
        minAccountAge?: number;
        requireVerification?: boolean;
        excludeRoles?: string[];
        includeRoles?: string[];
    };
    createdAt: Date;
    updatedAt: Date;
}
export declare const WelcomeTemplate: import("mongoose").Model<IWelcomeTemplate, {}, {}, {}, Document<unknown, {}, IWelcomeTemplate, {}> & IWelcomeTemplate & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=WelcomeTemplate.d.ts.map