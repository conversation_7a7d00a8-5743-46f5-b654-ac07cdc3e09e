import { SlashCommand<PERSON>uilder, ChatInputCommandInteraction } from 'discord.js';
import { getTransactionHistory } from '../services/economyService';
import { withErrorHandler, DatabaseError } from '../utils/errorHandler';
import { createEconomyEmbed, addUserInfo, formatCoins, EMOJIS, createNavigationButtons } from '../utils/embedBuilder';
import { ITransaction } from '../models/Transaction';

// Helper functions for transaction display
function getTransactionEmoji(type: string, amount: number): string {
  switch (type) {
    case 'pay':
      return amount > 0 ? EMOJIS.ECONOMY.MONEY : EMOJIS.ACTIONS.LIGHTNING;
    case 'buyrole':
      return EMOJIS.ROLES.MEDAL;
    case 'give':
      return EMOJIS.SUCCESS.STAR;
    case 'fine':
      return EMOJIS.ADMIN.WARNING;
    case 'reaction':
      return EMOJIS.SUCCESS.THUMBS_UP;
    default:
      return EMOJIS.ECONOMY.COINS;
  }
}

function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName('history')
    .setDescription('Show your recent transaction history'),
  execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
    try {
      const discordId = interaction.user.id;
      const transactions = await getTransactionHistory(discordId, 10);

      if (transactions.length === 0) {
        const embed = createEconomyEmbed('Transaction History')
          .setDescription(`${EMOJIS.MISC.SCROLL} No transaction history found.\n\nStart earning coins to see your transactions here!`)
          .setFooter({ text: 'Use /help to see ways to earn coins' });

        addUserInfo(embed, interaction.user);

        await interaction.reply({
          embeds: [embed],
          ephemeral: true
        });
        return;
      }

      // Create rich transaction history
      const transactionFields = transactions.map((tx: ITransaction, index: number) => {
        const sign = tx.amount > 0 ? '+' : '';
        const emoji = getTransactionEmoji(tx.type, tx.amount);
        const timestamp = `<t:${Math.floor(new Date(tx.timestamp).getTime() / 1000)}:R>`;

        return {
          name: `${emoji} ${capitalizeFirst(tx.type)} Transaction`,
          value: `**Amount:** ${sign}${formatCoins(Math.abs(tx.amount))}\n` +
                 `**Details:** ${tx.details || 'No details'}\n` +
                 `**Time:** ${timestamp}`,
          inline: index % 2 === 0 // Alternate inline for better layout
        };
      });

      const embed = createEconomyEmbed('Your Transaction History')
        .setDescription(`${EMOJIS.MISC.BOOK} **Recent Activity** (Last 10 transactions)\n\n${EMOJIS.ECONOMY.CHART} Track your coin earnings and spending below:`)
        .addFields(...transactionFields)
        .setFooter({
          text: `Showing ${transactions.length} most recent transactions • Private to you`
        });

      // Add user's avatar to embed
      addUserInfo(embed, interaction.user);

      await interaction.reply({
        embeds: [embed],
        ephemeral: true // Keep transaction history private
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new DatabaseError(error.message);
      }
      throw new DatabaseError('Failed to fetch transaction history.');
    }
  })
};
