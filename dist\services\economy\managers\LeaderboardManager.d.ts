/**
 * Leaderboard Manager
 * Handles leaderboard operations and rankings
 */
import { LeaderboardEntry, ILogger } from '../../../core/interfaces';
/**
 * Leaderboard management operations
 */
export declare class LeaderboardManager {
    private logger;
    constructor(logger: ILogger);
    /**
     * Get leaderboard
     */
    getLeaderboard(guildId?: string, limit?: number): Promise<LeaderboardEntry[]>;
    /**
     * Get user rank
     */
    getUserRank(discordId: string): Promise<number>;
    /**
     * Get top users by balance
     */
    getTopUsers(limit?: number): Promise<LeaderboardEntry[]>;
    /**
     * Get leaderboard around user
     */
    getLeaderboardAroundUser(discordId: string, range?: number): Promise<LeaderboardEntry[]>;
    /**
     * Get leaderboard statistics
     */
    getLeaderboardStats(): Promise<any>;
    /**
     * Validate leaderboard parameters
     */
    private validateLeaderboardParams;
    /**
     * Log operation
     */
    private logOperation;
    /**
     * Handle errors
     */
    private handleError;
}
//# sourceMappingURL=LeaderboardManager.d.ts.map