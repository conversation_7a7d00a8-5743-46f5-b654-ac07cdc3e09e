export declare class DailyRewardService {
    /**
     * Grants a daily reward to a user if they are eligible.
     * @param userId The user's Discord ID.
     * @returns An object indicating success, the amount granted, and a message.
     */
    grantDailyReward(userId: string): Promise<{
        success: boolean;
        amount: number;
        message: string;
    }>;
}
//# sourceMappingURL=DailyRewardService.d.ts.map