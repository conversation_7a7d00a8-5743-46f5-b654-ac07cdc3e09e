/**
 * Role Service
 * Refactored role assignment service with improved architecture
 */
import { Client, GuildMember } from 'discord.js';
import { BaseService } from '../base/BaseService';
import { IRoleService, IApplicationContext, RoleAssignmentResult, UserRoleInfo } from '../../core/interfaces';
/**
 * Role service implementation
 */
export declare class RoleService extends BaseService implements IRoleService {
    readonly name = "RoleService";
    constructor(app: IApplicationContext);
    /**
     * Initialize the role service
     */
    protected onInitialize(): Promise<void>;
    /**
     * Check and assign roles based on user balance
     */
    checkAndAssignRoles(client: Client, discordId: string, guildId: string, balance: number): Promise<RoleAssignmentResult | null>;
    /**
     * Get user achievement roles information
     */
    getUserAchievementRoles(member: GuildMember): Promise<UserRoleInfo>;
}
/**
 * Send role achievement notifications
 */
export declare function sendRoleAchievementNotifications(result: RoleAssignmentResult, client: Client): Promise<void>;
export { checkAndAssignRoles, getUserAchievementRoles } from '../roleAssignmentService';
//# sourceMappingURL=RoleService.d.ts.map