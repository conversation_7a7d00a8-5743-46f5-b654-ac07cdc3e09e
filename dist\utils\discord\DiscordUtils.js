"use strict";
/**
 * Discord Utilities
 * Helper functions for Discord-specific operations
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiscordUtils = exports.ButtonUtils = exports.EmbedUtils = exports.ChannelUtils = exports.RoleUtils = exports.MemberUtils = exports.GuildUtils = void 0;
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../errorHandler");
const constants_1 = require("../../config/constants");
/**
 * Guild utilities
 */
class GuildUtils {
    /**
     * Safely fetch guild by ID
     */
    static async fetchGuild(client, guildId) {
        try {
            return await client.guilds.fetch(guildId);
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Check if guild exists and bot has access
     */
    static async validateGuildAccess(client, guildId) {
        const guild = await this.fetchGuild(client, guildId);
        if (!guild) {
            throw new errorHandler_1.ValidationError(`Guild not found or bot doesn't have access: ${guildId}`);
        }
        return guild;
    }
    /**
     * Get guild member count
     */
    static async getMemberCount(guild) {
        try {
            await guild.members.fetch();
            return guild.memberCount;
        }
        catch (error) {
            return guild.memberCount; // Fallback to cached count
        }
    }
    /**
     * Check if guild has specific features
     */
    static hasFeature(guild, feature) {
        return guild.features.includes(feature);
    }
}
exports.GuildUtils = GuildUtils;
/**
 * Member utilities
 */
class MemberUtils {
    /**
     * Safely fetch member by ID
     */
    static async fetchMember(guild, userId) {
        try {
            return await guild.members.fetch(userId);
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Check if member exists in guild
     */
    static async validateMemberAccess(guild, userId) {
        const member = await this.fetchMember(guild, userId);
        if (!member) {
            throw new errorHandler_1.ValidationError(`Member not found in guild: ${userId}`);
        }
        return member;
    }
    /**
     * Check if member has specific permission
     */
    static hasPermission(member, permission) {
        return member.permissions.has(discord_js_1.PermissionFlagsBits[permission]);
    }
    /**
     * Check if member has any of the specified permissions
     */
    static hasAnyPermission(member, permissions) {
        return permissions.some(permission => this.hasPermission(member, permission));
    }
    /**
     * Check if member has admin permissions
     */
    static isAdmin(member) {
        return this.hasPermission(member, 'Administrator');
    }
    /**
     * Get member's highest role
     */
    static getHighestRole(member) {
        return member.roles.highest;
    }
    /**
     * Check if member can manage another member
     */
    static canManageMember(manager, target) {
        if (manager.id === target.id)
            return false;
        if (this.isAdmin(manager))
            return true;
        const managerHighest = this.getHighestRole(manager);
        const targetHighest = this.getHighestRole(target);
        if (!managerHighest || !targetHighest)
            return false;
        return managerHighest.position > targetHighest.position;
    }
}
exports.MemberUtils = MemberUtils;
/**
 * Role utilities
 */
class RoleUtils {
    /**
     * Safely fetch role by ID
     */
    static async fetchRole(guild, roleId) {
        try {
            return await guild.roles.fetch(roleId);
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Find role by name (case-insensitive)
     */
    static findRoleByName(guild, roleName) {
        return guild.roles.cache.find(role => role.name.toLowerCase() === roleName.toLowerCase()) || null;
    }
    /**
     * Check if role can be managed by bot
     */
    static canManageRole(guild, role) {
        const botMember = guild.members.me;
        if (!botMember)
            return false;
        const botHighestRole = MemberUtils.getHighestRole(botMember);
        if (!botHighestRole)
            return false;
        return botHighestRole.position > role.position;
    }
    /**
     * Get manageable roles for bot
     */
    static getManageableRoles(guild) {
        return guild.roles.cache.filter(role => this.canManageRole(guild, role) && role.name !== '@everyone').map(role => role);
    }
}
exports.RoleUtils = RoleUtils;
/**
 * Channel utilities
 */
class ChannelUtils {
    /**
     * Safely fetch channel by ID
     */
    static async fetchChannel(client, channelId) {
        try {
            const channel = await client.channels.fetch(channelId);
            return channel?.isTextBased() ? channel : null;
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Check if bot can send messages in channel
     */
    static canSendMessages(channel) {
        const permissions = channel.permissionsFor(channel.guild.members.me);
        return permissions?.has([discord_js_1.PermissionFlagsBits.SendMessages, discord_js_1.PermissionFlagsBits.ViewChannel]) || false;
    }
    /**
     * Check if bot can embed links in channel
     */
    static canEmbedLinks(channel) {
        const permissions = channel.permissionsFor(channel.guild.members.me);
        return permissions?.has(discord_js_1.PermissionFlagsBits.EmbedLinks) || false;
    }
    /**
     * Send message with error handling
     */
    static async safeSendMessage(channel, content) {
        try {
            if (!this.canSendMessages(channel)) {
                return false;
            }
            await channel.send(content);
            return true;
        }
        catch (error) {
            return false;
        }
    }
}
exports.ChannelUtils = ChannelUtils;
/**
 * Embed utilities
 */
class EmbedUtils {
    /**
     * Validate embed limits
     */
    static validateEmbed(embed) {
        const data = embed.toJSON();
        if (data.title && data.title.length > 256) {
            throw new errorHandler_1.ValidationError('Embed title cannot exceed 256 characters');
        }
        if (data.description && data.description.length > constants_1.DISCORD.MAX_EMBED_DESCRIPTION_LENGTH) {
            throw new errorHandler_1.ValidationError(`Embed description cannot exceed ${constants_1.DISCORD.MAX_EMBED_DESCRIPTION_LENGTH} characters`);
        }
        if (data.fields && data.fields.length > constants_1.DISCORD.MAX_EMBED_FIELDS) {
            throw new errorHandler_1.ValidationError(`Embed cannot have more than ${constants_1.DISCORD.MAX_EMBED_FIELDS} fields`);
        }
        const totalLength = (data.title?.length || 0) +
            (data.description?.length || 0) +
            (data.fields?.reduce((sum, field) => sum + field.name.length + field.value.length, 0) || 0);
        if (totalLength > 6000) {
            throw new errorHandler_1.ValidationError('Total embed content cannot exceed 6000 characters');
        }
    }
    /**
     * Truncate embed field value if too long
     */
    static truncateFieldValue(value, maxLength = 1024) {
        if (value.length <= maxLength)
            return value;
        return value.substring(0, maxLength - 3) + '...';
    }
    /**
     * Split long text into multiple embed fields
     */
    static splitIntoFields(text, fieldName, maxLength = 1024) {
        const fields = [];
        if (text.length <= maxLength) {
            fields.push({ name: fieldName, value: text });
            return fields;
        }
        let currentText = text;
        let partNumber = 1;
        while (currentText.length > 0) {
            let chunk = currentText.substring(0, maxLength);
            // Try to break at a newline
            if (currentText.length > maxLength) {
                const lastNewline = chunk.lastIndexOf('\n');
                if (lastNewline > maxLength * 0.5) {
                    chunk = chunk.substring(0, lastNewline);
                }
            }
            fields.push({
                name: partNumber === 1 ? fieldName : `${fieldName} (${partNumber})`,
                value: chunk
            });
            currentText = currentText.substring(chunk.length);
            partNumber++;
        }
        return fields;
    }
}
exports.EmbedUtils = EmbedUtils;
/**
 * Button utilities
 */
class ButtonUtils {
    /**
     * Create action row with buttons
     */
    static createActionRow(buttons) {
        if (buttons.length > 5) {
            throw new errorHandler_1.ValidationError('Action row cannot have more than 5 buttons');
        }
        return new discord_js_1.ActionRowBuilder().addComponents(buttons);
    }
    /**
     * Create simple button
     */
    static createButton(customId, label, style = discord_js_1.ButtonStyle.Primary, emoji) {
        if (label.length > constants_1.DISCORD.MAX_BUTTON_LABEL_LENGTH) {
            throw new errorHandler_1.ValidationError(`Button label cannot exceed ${constants_1.DISCORD.MAX_BUTTON_LABEL_LENGTH} characters`);
        }
        const button = new discord_js_1.ButtonBuilder()
            .setCustomId(customId)
            .setLabel(label)
            .setStyle(style);
        if (emoji) {
            button.setEmoji(emoji);
        }
        return button;
    }
    /**
     * Create link button
     */
    static createLinkButton(url, label, emoji) {
        const button = new discord_js_1.ButtonBuilder()
            .setURL(url)
            .setLabel(label)
            .setStyle(discord_js_1.ButtonStyle.Link);
        if (emoji) {
            button.setEmoji(emoji);
        }
        return button;
    }
}
exports.ButtonUtils = ButtonUtils;
/**
 * Comprehensive Discord utility
 */
class DiscordUtils {
    /**
     * Parse Discord ID from mention or return as-is
     */
    static parseId(input) {
        const match = input.match(/^<[@#&]!?(\d+)>$/);
        return match ? match[1] : input;
    }
    /**
     * Check if string is a Discord ID
     */
    static isDiscordId(input) {
        return /^\d{17,20}$/.test(input);
    }
    /**
     * Format user mention
     */
    static formatUserMention(userId) {
        return `<@${userId}>`;
    }
    /**
     * Format role mention
     */
    static formatRoleMention(roleId) {
        return `<@&${roleId}>`;
    }
    /**
     * Format channel mention
     */
    static formatChannelMention(channelId) {
        return `<#${channelId}>`;
    }
}
exports.DiscordUtils = DiscordUtils;
DiscordUtils.guild = GuildUtils;
DiscordUtils.member = MemberUtils;
DiscordUtils.role = RoleUtils;
DiscordUtils.channel = ChannelUtils;
DiscordUtils.embed = EmbedUtils;
DiscordUtils.button = ButtonUtils;
exports.default = DiscordUtils;
//# sourceMappingURL=DiscordUtils.js.map