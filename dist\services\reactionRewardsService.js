"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.processReactionReward = processReactionReward;
exports.isChannelMonetized = isChannelMonetized;
exports.getUserReactionStats = getUserReactionStats;
const MonetizedChannel_1 = require("../models/MonetizedChannel");
const ReactionReward_1 = require("../models/ReactionReward");
const economyService_1 = require("./economyService");
const errorHandler_1 = require("../utils/errorHandler");
// Configuration constants
const REACTION_REWARD_AMOUNT = 5;
const RATE_LIMIT_SECONDS = 30;
const MESSAGE_AGE_LIMIT_HOURS = 24;
/**
 * Processes a reaction addition and awards coins if eligible
 */
async function processReactionReward(reaction, user) {
    try {
        // Skip if user is a bot
        if (user.bot) {
            return;
        }
        const message = reaction.message;
        const channelId = message.channelId;
        const messageId = message.id;
        const userId = user.id;
        // Check if user is reacting to their own message
        if (message.author?.id === userId) {
            console.log(`[Reaction Rewards] User ${user.tag} tried to react to their own message ${messageId}`);
            return;
        }
        // Check if channel is monetized
        const monetizedChannel = await MonetizedChannel_1.MonetizedChannel.findOne({
            channelId,
            enabled: true
        });
        if (!monetizedChannel) {
            return; // Channel not monetized, no reward
        }
        // Check message age (only reward reactions on messages newer than 24 hours)
        const messageAge = Date.now() - message.createdTimestamp;
        const maxAge = MESSAGE_AGE_LIMIT_HOURS * 60 * 60 * 1000; // 24 hours in milliseconds
        if (messageAge > maxAge) {
            console.log(`[Reaction Rewards] Message ${messageId} too old for rewards (${Math.round(messageAge / (60 * 60 * 1000))} hours)`);
            return;
        }
        // Check if user already received reward for this message
        const existingReward = await ReactionReward_1.ReactionReward.findOne({
            userId,
            messageId
        });
        if (existingReward) {
            console.log(`[Reaction Rewards] User ${user.tag} already rewarded for message ${messageId}`);
            return;
        }
        // Check rate limiting (user can't earn rewards too frequently)
        const rateLimitCutoff = new Date(Date.now() - (RATE_LIMIT_SECONDS * 1000));
        const recentReward = await ReactionReward_1.ReactionReward.findOne({
            userId,
            timestamp: { $gte: rateLimitCutoff }
        });
        if (recentReward) {
            const timeLeft = Math.ceil((recentReward.timestamp.getTime() + (RATE_LIMIT_SECONDS * 1000) - Date.now()) / 1000);
            console.log(`[Reaction Rewards] User ${user.tag} rate limited, ${timeLeft}s remaining`);
            return;
        }
        // All checks passed, award the coins
        await awardReactionCoins(userId, messageId, channelId, user.tag, reaction);
    }
    catch (error) {
        console.error('[Reaction Rewards] Error processing reaction reward:', error);
        // Don't throw error to prevent disrupting other bot functionality
    }
}
/**
 * Awards coins to a user for a reaction and records the transaction
 */
async function awardReactionCoins(userId, messageId, channelId, userTag, reaction) {
    try {
        // Record the reward first to prevent race conditions
        const reactionReward = await ReactionReward_1.ReactionReward.create({
            userId,
            messageId,
            channelId,
            coinsAwarded: REACTION_REWARD_AMOUNT,
            timestamp: new Date()
        });
        // Award the coins using the existing economy service
        await (0, economyService_1.adjustBalance)(userId, REACTION_REWARD_AMOUNT, 'reaction', `Reaction reward in channel ${channelId}`, reaction.message.client, reaction.message.guild?.id);
        console.log(`[Reaction Rewards] Awarded ${REACTION_REWARD_AMOUNT} coins to ${userTag} for reaction on message ${messageId}`);
    }
    catch (error) {
        // If coin award fails, try to clean up the reward record
        try {
            await ReactionReward_1.ReactionReward.deleteOne({ userId, messageId });
        }
        catch (cleanupError) {
            console.error('[Reaction Rewards] Failed to cleanup reward record:', cleanupError);
        }
        if (error instanceof Error) {
            throw new errorHandler_1.DatabaseError(`Failed to award reaction coins: ${error.message}`, error);
        }
        throw new errorHandler_1.DatabaseError('Failed to award reaction coins');
    }
}
/**
 * Checks if a channel is monetized for reaction rewards
 */
async function isChannelMonetized(channelId) {
    try {
        const monetizedChannel = await MonetizedChannel_1.MonetizedChannel.findOne({
            channelId,
            enabled: true
        });
        return !!monetizedChannel;
    }
    catch (error) {
        console.error('[Reaction Rewards] Error checking channel monetization:', error);
        return false;
    }
}
/**
 * Gets reaction reward statistics for a user
 */
async function getUserReactionStats(userId, days = 7) {
    try {
        const cutoffDate = new Date(Date.now() - (days * 24 * 60 * 60 * 1000));
        const [totalStats, recentStats] = await Promise.all([
            ReactionReward_1.ReactionReward.aggregate([
                { $match: { userId } },
                {
                    $group: {
                        _id: null,
                        totalRewards: { $sum: 1 },
                        totalCoins: { $sum: '$coinsAwarded' }
                    }
                }
            ]),
            ReactionReward_1.ReactionReward.countDocuments({
                userId,
                timestamp: { $gte: cutoffDate }
            })
        ]);
        return {
            totalRewards: totalStats[0]?.totalRewards || 0,
            totalCoins: totalStats[0]?.totalCoins || 0,
            recentRewards: recentStats
        };
    }
    catch (error) {
        console.error('[Reaction Rewards] Error getting user stats:', error);
        return { totalRewards: 0, totalCoins: 0, recentRewards: 0 };
    }
}
//# sourceMappingURL=reactionRewardsService.js.map