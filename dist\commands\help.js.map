{"version": 3, "file": "help.js", "sourceRoot": "", "sources": ["../../src/commands/help.ts"], "names": [], "mappings": ";;AAAA,2CAMoB;AACpB,wDAAuE;AACvE,uEAAoE;AAEpE,sCAAsC;AACtC,MAAM,QAAQ,GAAG;IACb,iBAAiB;IACjB,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,yCAAyC,EAAE,QAAQ,EAAE,OAAO,EAAE;IAChH,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,wCAAwC,EAAE,QAAQ,EAAE,OAAO,EAAE;IACvG,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,sCAAsC,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC7G,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,+BAA+B,EAAE,QAAQ,EAAE,OAAO,EAAE;IAE9G,gBAAgB;IAChB,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,iDAAiD,EAAE,QAAQ,EAAE,OAAO,EAAE;IAEpH,iBAAiB;IACjB,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,qCAAqC,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC7G,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,sCAAsC,EAAE,QAAQ,EAAE,OAAO,EAAE;IAChH,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,wCAAwC,EAAE,QAAQ,EAAE,OAAO,EAAE;IACtH,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,mCAAmC,EAAE,QAAQ,EAAE,OAAO,EAAE;IACpG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,8CAA8C,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC/G,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,kDAAkD,EAAE,QAAQ,EAAE,OAAO,EAAE;IACjH,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,iBAAiB,EAAE,WAAW,EAAE,qDAAqD,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC3I,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,cAAc,EAAE,WAAW,EAAE,6DAA6D,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC7I,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,mBAAmB,EAAE,WAAW,EAAE,+CAA+C,EAAE,QAAQ,EAAE,OAAO,EAAE;IACxI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,cAAc,EAAE,WAAW,EAAE,2DAA2D,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC3I,EAAE,IAAI,EAAE,mBAAmB,EAAE,KAAK,EAAE,cAAc,EAAE,WAAW,EAAE,mEAAmE,EAAE,QAAQ,EAAE,OAAO,EAAE;CAC5J,CAAC;AAEF,MAAM,CAAC,OAAO,GAAG;IACb,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC1B,OAAO,CAAC,MAAM,CAAC;SACf,cAAc,CAAC,+BAA+B,CAAC;IACpD,OAAO,EAAE,IAAA,+BAAgB,EAAC,KAAK,EAAE,WAAwC,EAAE,EAAE;QACzE,IAAI,CAAC;YACD,8CAA8C;YAC9C,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC;YACtC,MAAM,eAAe,GAAG,OAAO,CAAC,CAAC,CAAC,MAAM,IAAA,uCAAkB,EAAC,OAAO,CAAC,CAAC,CAAC;gBACjE,4gBAA4gB,CAAC;YAEjhB,mCAAmC;YACnC,MAAM,YAAY,GAAG,IAAI,6BAAgB,EAAiB,CAAC,aAAa,CACpE,QAAQ;iBACH,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,OAAO,CAAC;iBACvC,GAAG,CAAC,GAAG,CAAC,EAAE,CACP,IAAI,0BAAa,EAAE;iBACd,WAAW,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;iBAC/B,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC;iBACnB,QAAQ,CAAC,wBAAW,CAAC,OAAO,CAAC,CACrC,CACR,CAAC;YAEF,MAAM,WAAW,GAAG,IAAI,6BAAgB,EAAiB,CAAC,aAAa,CACnE,QAAQ;iBACH,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,OAAO,CAAC;iBACvC,GAAG,CAAC,GAAG,CAAC,EAAE,CACP,IAAI,0BAAa,EAAE;iBACd,WAAW,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;iBAC/B,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC;iBACnB,QAAQ,CAAC,wBAAW,CAAC,OAAO,CAAC,CACrC,CACR,CAAC;YAEF,qFAAqF;YACrF,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC;YACvE,MAAM,aAAa,GAAG,IAAI,6BAAgB,EAAiB,CAAC,aAAa,CACrE,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAChC,IAAI,0BAAa,EAAE;iBACd,WAAW,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;iBAC/B,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC;iBACnB,QAAQ,CAAC,wBAAW,CAAC,MAAM,CAAC,CACpC,CACJ,CAAC;YAEF,MAAM,aAAa,GAAG,IAAI,6BAAgB,EAAiB,CAAC,aAAa,CACrE,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACjC,IAAI,0BAAa,EAAE;iBACd,WAAW,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;iBAC/B,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC;iBACnB,QAAQ,CAAC,wBAAW,CAAC,MAAM,CAAC,CACpC,CACJ,CAAC;YAEF,MAAM,OAAO,GAAG;gBACZ,yBAAyB;gBACzB,EAAE;gBACF,wBAAwB;gBACxB,GAAG,QAAQ;qBACN,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,OAAO,CAAC;qBACvC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;gBACzD,EAAE;gBACF,uBAAuB;gBACvB,GAAG,QAAQ;qBACN,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,OAAO,CAAC;qBACvC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;gBACzD,EAAE;gBACF,wBAAwB;gBACxB,GAAG,QAAQ;qBACN,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,OAAO,CAAC;qBACvC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;gBACzD,EAAE;gBACF,eAAe;aAClB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEb,6CAA6C;YAC7C,MAAM,WAAW,GAAG,IAAI,6BAAgB,EAAiB,CAAC,aAAa,CACnE,IAAI,0BAAa,EAAE;iBACd,QAAQ,CAAC,mBAAmB,CAAC;iBAC7B,QAAQ,CAAC,wBAAW,CAAC,IAAI,CAAC;iBAC1B,MAAM,CAAC,+BAA+B,CAAC,CAC/C,CAAC;YAEF,MAAM,WAAW,CAAC,KAAK,CAAC;gBACpB,OAAO;gBACP,UAAU,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,CAAC;gBAClF,SAAS,EAAE,KAAK,CAAC,4CAA4C;aAChE,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACtB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBACzB,MAAM,IAAI,2BAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC1C,CAAC;YACD,MAAM,IAAI,2BAAY,CAAC,uCAAuC,CAAC,CAAC;QACpE,CAAC;IACL,CAAC,CAAC;CACL,CAAC"}