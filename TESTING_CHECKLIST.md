# Trade System Testing Checklist

Comprehensive testing checklist to ensure the trade system works reliably before and after deployment.

## 🧪 Pre-Deployment Testing

### Unit Tests
Run all unit tests to verify core functionality:

```bash
# Run all tests
npm test

# Run with coverage (should be >80%)
npm run test:coverage

# Run trade-specific tests
npm run test:trade
```

**Expected Results:**
- [ ] All escrow tests pass (lock, release, refund, split)
- [ ] All trade flow tests pass (creation, acceptance, completion)
- [ ] All validation tests pass (edge cases, error handling)
- [ ] All dispute tests pass (creation, resolution, evidence)
- [ ] Code coverage >80% for trade system components

### Integration Tests

#### Database Integration
```bash
# Test database connection
npm run migrate:status

# Test migration system
npm run migrate up --dry-run
npm run migrate down --dry-run
```

**Expected Results:**
- [ ] Database connection successful
- [ ] Migration system works correctly
- [ ] All collections and indexes created properly
- [ ] Rollback functionality works

#### Discord Integration
Test Discord bot functionality:

**Expected Results:**
- [ ] <PERSON><PERSON> connects to Discord successfully
- [ ] Slash commands register properly
- [ ] Button interactions work
- [ ] DM notifications send correctly
- [ ] Embed formatting displays properly

## 🎯 Functional Testing

### Core Trade Flows

#### 1. Seller-Initiated Trade
**Test Steps:**
1. <PERSON><PERSON> creates trade proposal: `/trade sell @buyer 1000 Diamond Sword`
2. Buyer receives DM notification with accept/decline buttons
3. Buyer clicks "Accept Trade"
4. Verify escrow locks buyer's funds
5. Both parties complete real-world exchange
6. Seller confirms: `/trade confirm [trade-id]`
7. Buyer confirms: `/trade confirm [trade-id]`
8. Verify funds released to seller

**Expected Results:**
- [ ] Trade created successfully
- [ ] Notifications sent to both parties
- [ ] Escrow locks correct amount
- [ ] Confirmations work properly
- [ ] Funds released correctly
- [ ] User stats updated

#### 2. Buyer-Initiated Trade
**Test Steps:**
1. Buyer creates trade proposal: `/trade buy @seller 1000 Diamond Sword`
2. Seller receives DM notification
3. Seller accepts trade
4. Complete trade flow as above

**Expected Results:**
- [ ] Buyer-initiated flow works identically
- [ ] Correct party pays (buyer)
- [ ] Correct party receives payment (seller)

#### 3. Trade Cancellation
**Test Steps:**
1. Create active trade (with escrow locked)
2. One party cancels: `/trade cancel [trade-id] Changed my mind`
3. Verify refund processed

**Expected Results:**
- [ ] Trade cancelled successfully
- [ ] Escrow refunded to buyer
- [ ] Both parties notified
- [ ] User stats updated

#### 4. Trade Expiration
**Test Steps:**
1. Create trade with short expiration (for testing)
2. Wait for expiration
3. Verify automatic processing

**Expected Results:**
- [ ] Trade expires automatically
- [ ] Escrow refunded if locked
- [ ] Expiration notifications sent
- [ ] Background service processes correctly

### Security & Validation Testing

#### Rate Limiting
**Test Steps:**
1. Create maximum daily trades for a user
2. Attempt to create one more trade
3. Verify rejection

**Expected Results:**
- [ ] Daily limit enforced (10 trades/day)
- [ ] Active trade limit enforced (5 active)
- [ ] Cooldown period respected
- [ ] Appropriate error messages

#### Input Validation
**Test Cases:**
- [ ] Minimum trade amount (100 PLC)
- [ ] Maximum trade amount (100,000 PLC)
- [ ] Invalid Discord IDs
- [ ] Self-trading prevention
- [ ] Empty/long item descriptions
- [ ] Special characters in descriptions

#### Security Checks
**Test Cases:**
- [ ] High-risk user detection
- [ ] Automatic restrictions applied
- [ ] Dispute ratio monitoring
- [ ] Suspicious pattern detection

### Dispute System Testing

#### 1. Dispute Creation
**Test Steps:**
1. Create active trade
2. Initiate dispute: `/trade dispute [trade-id] Item not received`
3. Verify dispute created

**Expected Results:**
- [ ] Dispute case created
- [ ] Trade state updated to DISPUTED
- [ ] Both parties notified
- [ ] Evidence collection period started

#### 2. Evidence Submission
**Test Steps:**
1. Both parties add evidence
2. Verify evidence recorded
3. Test evidence deadline

**Expected Results:**
- [ ] Evidence properly stored
- [ ] Deadline enforced
- [ ] Non-parties cannot add evidence

#### 3. Admin Resolution
**Test Steps:**
1. Admin views disputes: `/tradeadmin disputes`
2. Admin resolves: `/tradeadmin resolve [dispute-id] FAVOR_INITIATOR "Evidence shows non-delivery"`
3. Verify resolution

**Expected Results:**
- [ ] Admin can view all disputes
- [ ] Resolution options work (favor initiator/respondent, split, custom)
- [ ] Funds distributed correctly
- [ ] Resolution notifications sent

### Admin Interface Testing

#### Trade Administration
**Test Commands:**
- [ ] `/tradeadmin overview` - System statistics
- [ ] `/tradeadmin stats @user` - User trade history
- [ ] `/tradeadmin restrict @user "Reason" 7` - User restrictions
- [ ] `/tradeadmin unrestrict @user` - Remove restrictions
- [ ] `/tradeadmin cancel [trade-id] "Emergency"` - Emergency cancellation

#### Trade Monitoring
**Test Commands:**
- [ ] `/trademonitor active` - View active trades
- [ ] `/trademonitor stuck` - Find stuck trades
- [ ] `/trademonitor escrow` - Check escrow consistency
- [ ] `/trademonitor health` - System health check
- [ ] `/trademonitor user @user` - User analysis

### Background Services Testing

#### Automated Tasks
**Test Cases:**
- [ ] Expired trade processing (every 5 minutes)
- [ ] Warning notifications (6h and 1h before expiration)
- [ ] Cleanup operations (hourly)
- [ ] Health checks (every 15 minutes)

**Verification:**
- [ ] Check logs for task execution
- [ ] Verify task statistics
- [ ] Test manual cleanup trigger

### Error Handling Testing

#### Database Errors
**Test Scenarios:**
- [ ] Database connection loss
- [ ] Transaction failures
- [ ] Concurrent modification conflicts
- [ ] Index constraint violations

#### Discord API Errors
**Test Scenarios:**
- [ ] User not found
- [ ] DM delivery failures
- [ ] Rate limiting from Discord
- [ ] Permission errors

#### Application Errors
**Test Scenarios:**
- [ ] Invalid trade states
- [ ] Insufficient balances
- [ ] Expired trades
- [ ] Missing data

## 🚀 Post-Deployment Testing

### Smoke Tests (Immediate)
After deployment, run these critical tests:

1. **Bot Status**
   - [ ] Bot shows online in Discord
   - [ ] Slash commands appear in command list
   - [ ] Help command works: `/trade help`

2. **Database Connectivity**
   - [ ] Check logs for database connection
   - [ ] Verify collections exist
   - [ ] Test basic query

3. **Basic Functionality**
   - [ ] Create a test trade
   - [ ] Cancel the test trade
   - [ ] Verify refund processed

### Load Testing (24-48 hours)
Monitor system under normal load:

- [ ] Multiple concurrent trades
- [ ] Background task performance
- [ ] Memory usage stability
- [ ] Database query performance

### User Acceptance Testing
Have trusted users test:

- [ ] Trade creation flow
- [ ] Notification delivery
- [ ] Button interactions
- [ ] Mobile Discord compatibility

## 📊 Performance Testing

### Response Time Benchmarks
- [ ] Trade creation: <2 seconds
- [ ] Trade acceptance: <3 seconds
- [ ] Trade confirmation: <2 seconds
- [ ] Admin commands: <5 seconds

### Resource Usage
- [ ] Memory usage stable over time
- [ ] CPU usage reasonable (<50% average)
- [ ] Database connections properly pooled
- [ ] No memory leaks detected

### Scalability Testing
- [ ] 100+ concurrent users
- [ ] 1000+ active trades
- [ ] High dispute volume
- [ ] Background task performance under load

## 🔍 Monitoring & Alerting

### Key Metrics to Monitor
- [ ] Trade success rate (>95%)
- [ ] Dispute rate (<5%)
- [ ] System uptime (>99.9%)
- [ ] Response times
- [ ] Error rates

### Alert Thresholds
- [ ] High error rate (>1% in 5 minutes)
- [ ] Slow response times (>10 seconds)
- [ ] Database connection failures
- [ ] Background task failures
- [ ] High dispute rate (>10% in 1 hour)

## ✅ Sign-off Checklist

Before marking testing complete:

### Technical Sign-off
- [ ] All unit tests pass
- [ ] All integration tests pass
- [ ] All functional tests pass
- [ ] Performance benchmarks met
- [ ] Security tests pass
- [ ] Error handling verified

### Business Sign-off
- [ ] Core trade flows work correctly
- [ ] Admin tools functional
- [ ] User experience acceptable
- [ ] Security measures adequate
- [ ] Monitoring in place

### Documentation
- [ ] Deployment guide updated
- [ ] User documentation complete
- [ ] Admin procedures documented
- [ ] Troubleshooting guide ready

---

**📝 Testing Notes:**
- Document any issues found during testing
- Include steps to reproduce problems
- Note any workarounds or limitations
- Update test cases based on findings

**🚨 Critical Issues:**
Any test failure in core trade flows, escrow operations, or security features should block deployment until resolved.
