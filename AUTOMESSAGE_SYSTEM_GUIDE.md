# 🤖 AutoMessage System - Comprehensive Guide

The Economy Bot's AutoMessage System provides powerful automated messaging capabilities for various server events. Create custom messages for member joins, role changes, and more with advanced customization options.

## 🚀 Quick Start

### 1. Create Your First Welcome Message
```
/automessage action:create trigger:member_join delivery:channel name:welcome channel:#welcome title:Welcome to {server}! description:Hello {user}! Welcome to **{server}**! You are member #{server.memberCount}!
```

### 2. Test Your Message
```
/automessage action:test name:welcome
```

### 3. View All Your Messages
```
/automessage action:list
```

## 📋 Command Reference

### `/automessage` Command

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `action` | Choice | ✅ | Action to perform | `create`, `remove`, `list`, `test` |
| `trigger` | Choice | For create | Event that triggers the message | `member_join`, `role_add`, `role_remove` |
| `delivery` | Choice | For create | How to deliver the message | `dm`, `channel`, `both` |
| `name` | String | For create/remove/test | Name for the message template | `welcome`, `new-member`, `vip-role` |
| `role` | Role | For role triggers | Specific role for role_add/role_remove | `@VIP`, `@Member` |
| `channel` | Channel | For channel delivery | Channel to send messages to | `#welcome`, `#announcements` |
| `title` | String | Optional | Title for the embed (max 256 chars) | `Welcome to {server}!` |
| `description` | String | Optional | Main content (max 4000 chars) | `Hello {user}! Welcome!` |
| `image` | String | Optional | URL to an image | `https://example.com/image.png` |
| `color` | String | Optional | Hex color code | `#dd7d00`, `#ff0000` |
| `buttons` | String | Optional | Buttons in format: Name1\|URL1 Name2\|URL2 | `Rules\|#rules Website\|https://example.com` |
| `embed` | Boolean | Optional | Send as embed (true) or plain text (false) | `true`, `false` |

### `/editautomessage` Command

Edit existing automated messages with the same parameters as creation, plus:

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `name` | String | Optional* | Name of template to edit (or use selection menu) | `welcome`, `vip-role` |
| `enabled` | Boolean | Optional | Enable or disable the message | `true`, `false` |

*If no name is provided, a selection menu will appear to choose which message to edit.

## 🎯 Trigger Types

### 1. Member Join (`member_join`)
Triggered when a new member joins the server.

**Example:**
```
/automessage action:create trigger:member_join delivery:both name:welcome channel:#welcome title:Welcome to {server}! description:Hello {user}! Welcome to **{server}**! You are member #{server.memberCount}! buttons:Rules|#rules Discord|https://discord.com
```

### 2. Role Added (`role_add`)
Triggered when a specific role is added to a member.

**Example:**
```
/automessage action:create trigger:role_add role:@VIP delivery:dm name:vip-welcome title:Welcome to VIP! description:Congratulations {user}! You now have VIP access!
```

### 3. Role Removed (`role_remove`)
Triggered when a specific role is removed from a member.

**Example:**
```
/automessage action:create trigger:role_remove role:@Member delivery:channel channel:#logs name:member-removed title:Member Role Removed description:{user} no longer has the Member role.
```

## 📨 Delivery Methods

### 1. Direct Message (`dm`)
Sends the message directly to the user's DMs.
- **Pros:** Private, personal
- **Cons:** May fail if user has DMs disabled

### 2. Channel (`channel`)
Sends the message to a specified channel.
- **Pros:** Public, always works
- **Cons:** Can be spammy in busy servers

### 3. Both (`both`)
Attempts to send to both DM and channel.
- **Pros:** Maximum reach
- **Cons:** Potential duplication

## 🔧 Placeholders

Use these placeholders in your messages - they'll be automatically replaced with real values:

### User Placeholders
- `{user}` - User's display name
- `{user.mention}` - Mentions the user (@username)
- `{user.username}` - User's username
- `{user.displayName}` - User's display name in server
- `{user.id}` - User's Discord ID
- `{user.tag}` - User's full tag (username#discriminator)

### Server Placeholders
- `{server}` - Server name
- `{server.name}` - Server name
- `{server.id}` - Server ID
- `{server.memberCount}` - Total member count

### Role Placeholders (for role triggers)
- `{role}` - Role name
- `{role.name}` - Role name
- `{role.id}` - Role ID
- `{role.mention}` - Mentions the role (@rolename)

### Date/Time Placeholders
- `{date}` - Current date (MM/DD/YYYY)
- `{time}` - Current time (HH:MM:SS AM/PM)
- `{datetime}` - Current date and time

**View all placeholders:** `/placeholders`

## 🎨 Customization Options

### Message Format
Choose between two message formats:

#### Embed Messages (Default)
Rich, styled messages with advanced formatting:
- **Title:** Up to 256 characters
- **Description:** Up to 4000 characters
- **Color:** Hex color codes (e.g., `#dd7d00`)
- **Image:** Any valid image URL
- **Timestamp:** Automatically added
- **Buttons:** Interactive link buttons

#### Plain Text Messages
Simple text messages without embed styling:
- **Content:** Uses the description field (up to 4000 characters)
- **Buttons:** Still supported
- **Placeholders:** Fully supported
- **Formatting:** Basic Discord markdown supported

**To create a plain text message:**
```
/automessage action:create trigger:member_join delivery:channel name:simple-welcome description:Welcome {user}! You are member #{server.memberCount}! embed:false
```

### Interactive Buttons
Add up to 5 buttons per message using the format: `Name1|URL1 Name2|URL2`

**Example:**
```
buttons:Rules|#rules Website|https://example.com Discord|https://discord.gg/invite
```

## 📝 Example Use Cases

### 1. Comprehensive Welcome System
```bash
# Main welcome message
/automessage action:create trigger:member_join delivery:both name:main-welcome channel:#welcome title:🎉 Welcome to {server}! description:Hello {user}! Welcome to **{server}**!\n\nWe're glad to have you here. You are member #{server.memberCount}!\n\nEnjoy your time in our community! 🎉 color:#dd7d00 buttons:Rules|#rules Getting-Started|#info

# VIP role welcome
/automessage action:create trigger:role_add role:@VIP delivery:dm name:vip-perks title:🌟 VIP Access Granted! description:Congratulations {user}! You now have VIP access to exclusive channels and perks! color:#ffd700

# Member verification
/automessage action:create trigger:role_add role:@Verified delivery:channel channel:#general name:new-verified title:New Verified Member description:Please welcome {user.mention} to our verified community! 🎉
```

### 2. Role Management Notifications
```bash
# Staff role added
/automessage action:create trigger:role_add role:@Staff delivery:both name:staff-welcome channel:#staff-chat title:👑 Welcome to the Team! description:Welcome to the staff team, {user}! Check #staff-chat for important information.

# Staff role removed
/automessage action:create trigger:role_remove role:@Staff delivery:channel channel:#staff-logs name:staff-removed title:Staff Role Removed description:{user} is no longer a staff member. (Removed on {datetime})
```

## 🔧 Management Commands

### List All Messages
```
/automessage action:list
```
Shows all configured automated messages grouped by trigger type.

### Test a Message
```
/automessage action:test name:welcome
```
Previews how your message will look without actually sending it.

### Remove a Message
```
/automessage action:remove name:welcome
```
Permanently deletes an automated message template.

### Edit a Message
```
/editautomessage name:welcome title:New Title description:Updated content
```
Modifies an existing message template. You can change any parameter including trigger type, delivery method, content, and format.

**Interactive editing:**
```
/editautomessage
```
Shows a selection menu to choose which message to edit, then provides guidance on available parameters.

## ⚙️ Advanced Features

### Automatic User Mentions for Role Changes
Role-based triggers (`role_add` and `role_remove`) automatically include user mentions to ensure Discord notifications:

- **Embed Messages**: User mention added as separate `content` field alongside the embed
- **Plain Text Messages**: User mention prepended to the message content
- **Member Join**: No automatic mentions (preserves existing behavior)

**Example Results:**
- Role Add (Embed): `{ content: "<@userId>", embeds: [embedObject] }`
- Role Remove (Plain): `{ content: "<@userId> Your role has been removed." }`
- Member Join: `{ embeds: [embedObject] }` (no mention)

### Multiple Messages per Trigger
You can create multiple messages for the same trigger - they'll all be sent when the event occurs.

### Priority System
Messages are processed in priority order (higher priority first). Default priority is 1.

### Error Handling
- Failed DM deliveries won't stop channel deliveries
- Detailed error logging for troubleshooting
- Graceful handling of deleted channels/roles

## 🚨 Troubleshooting

### Common Issues

1. **Message not sending**
   - Check that the trigger event is correct
   - Verify the bot has permissions in the target channel
   - Ensure the message template is enabled

2. **DM delivery failing**
   - User may have DMs disabled
   - Use `both` delivery method as fallback

3. **Role triggers not working**
   - Make sure you specified the correct role
   - Verify the role still exists
   - Check that the bot can see role changes

4. **Placeholders not replacing**
   - Check placeholder spelling and syntax
   - Ensure you're using the correct trigger type for role placeholders

### Getting Help
- Use `/automessage action:list` to see all your messages
- Use `/automessage action:test` to preview messages
- Use `/placeholders` to see available placeholders
- Check the bot's error messages for specific issues

## 🔒 Permissions

- **Administrator permissions** required to manage automated messages
- **Send Messages** permission needed in target channels
- **Embed Links** permission recommended for rich embeds
- **Use External Emojis** permission for custom emojis in messages

## 🎯 Best Practices

1. **Keep messages concise** - Users appreciate brief, helpful messages
2. **Use placeholders** - Personalize messages with user/server info
3. **Test before deploying** - Always test your messages first
4. **Monitor delivery** - Check logs for failed deliveries
5. **Update regularly** - Keep messages current with server changes
6. **Use appropriate channels** - Send messages where they make sense
7. **Consider user experience** - Don't overwhelm users with too many messages

---

The AutoMessage System replaces the old welcome system with much more powerful and flexible automation capabilities. Enjoy creating engaging, personalized experiences for your server members!
