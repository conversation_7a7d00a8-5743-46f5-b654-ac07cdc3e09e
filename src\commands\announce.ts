import { SlashCommandBuilder, ChatInputCommandInteraction, ButtonInteraction, PermissionFlagsBits, Role, GuildMember, EmbedBuilder } from 'discord.js';
import { withErrorHandler, ValidationError, PermissionError, DatabaseError } from '../utils/errorHandler';
import { createAdminEmbed, createSuccessEmbed, createErrorEmbed, createConfirmationButtons, EMOJIS, COLORS } from '../utils/embedBuilder';

// Global cooldown tracking
const announcementCooldowns = new Map<string, number>();
const COOLDOWN_DURATION = 10 * 60 * 1000; // 10 minutes in milliseconds
const DM_DELAY = 2500; // 2.5 seconds between DMs
const MAX_RECIPIENTS = 100; // Maximum users to DM
const MAX_TITLE_LENGTH = 100;
const MAX_DESCRIPTION_LENGTH = 1500;

module.exports = {
    data: new SlashCommandBuilder()
        .setName('announce')
        .setDescription('Send announcements to all members with a specific role (admin only)')
        .addRoleOption(option =>
            option.setName('role')
                .setDescription('The role to target for announcements')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('title')
                .setDescription('Announcement title/subject line')
                .setRequired(true)
                .setMaxLength(MAX_TITLE_LENGTH))
        .addStringOption(option =>
            option.setName('description')
                .setDescription('Main announcement message content')
                .setRequired(true)
                .setMaxLength(MAX_DESCRIPTION_LENGTH))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError();
        }

        const guild = interaction.guild;
        if (!guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        const targetRole = interaction.options.getRole('role', true) as Role;
        const title = interaction.options.getString('title', true);
        const description = interaction.options.getString('description', true);
        const adminId = interaction.user.id;

        // Check cooldown
        const lastAnnouncement = announcementCooldowns.get(guild.id);
        if (lastAnnouncement && Date.now() - lastAnnouncement < COOLDOWN_DURATION) {
            const remainingTime = Math.ceil((COOLDOWN_DURATION - (Date.now() - lastAnnouncement)) / 1000 / 60);
            throw new ValidationError(`Announcement cooldown active. Please wait ${remainingTime} more minutes before sending another announcement.`);
        }

        // Validate role
        if (targetRole.name === '@everyone' || targetRole.name === '@here') {
            throw new ValidationError('Cannot target @everyone or @here roles for announcements.');
        }

        // Get role members
        await guild.members.fetch(); // Ensure all members are cached
        const roleMembers = targetRole.members.filter(member => !member.user.bot);

        if (roleMembers.size === 0) {
            throw new ValidationError(`No members found with the ${targetRole.name} role.`);
        }

        if (roleMembers.size > MAX_RECIPIENTS) {
            throw new ValidationError(`Too many recipients (${roleMembers.size}). Maximum allowed is ${MAX_RECIPIENTS} users.`);
        }

        // Create confirmation embed
        const confirmationEmbed = createAdminEmbed('Announcement Confirmation Required')
            .setDescription(
                `${EMOJIS.ADMIN.WARNING} **Please confirm this announcement**\n\n` +
                `You are about to send a DM to **${roleMembers.size}** members with the **${targetRole.name}** role.`
            )
            .addFields(
                {
                    name: `${EMOJIS.MISC.SCROLL} Target Role`,
                    value: `**${targetRole.name}**`,
                    inline: true
                },
                {
                    name: `${EMOJIS.ACTIONS.TARGET} Recipients`,
                    value: `**${roleMembers.size}** members`,
                    inline: true
                },
                {
                    name: `${EMOJIS.MISC.CLOCK} Estimated Time`,
                    value: `~${Math.ceil(roleMembers.size * DM_DELAY / 1000 / 60)} minutes`,
                    inline: true
                },
                {
                    name: `${EMOJIS.MISC.BOOK} Title`,
                    value: title,
                    inline: false
                },
                {
                    name: `${EMOJIS.MISC.SCROLL} Message Preview`,
                    value: description.length > 200 ? description.substring(0, 200) + '...' : description,
                    inline: false
                }
            )
            .setColor(COLORS.WARNING)
            .setFooter({
                text: 'This action cannot be undone. Click Confirm to proceed.'
            });

        // Create confirmation buttons
        const confirmationButtons = createConfirmationButtons('announce_confirm', 'announce_cancel');

        await interaction.reply({
            embeds: [confirmationEmbed],
            components: [confirmationButtons],
            ephemeral: true
        });

        // Store announcement data for confirmation handler
        const announcementData = {
            targetRole,
            title,
            description,
            roleMembers: Array.from(roleMembers.values()),
            adminId,
            guildId: guild.id,
            guildName: guild.name
        };

        // Store in a temporary cache (you might want to use a proper cache/database)
        (global as any).pendingAnnouncements = (global as any).pendingAnnouncements || new Map();
        (global as any).pendingAnnouncements.set(interaction.id, announcementData);

        // Clean up after 5 minutes
        setTimeout(() => {
            (global as any).pendingAnnouncements?.delete(interaction.id);
        }, 5 * 60 * 1000);
    })
};

/**
 * Process the actual announcement sending
 */
async function processAnnouncement(
    interaction: ChatInputCommandInteraction | ButtonInteraction,
    announcementData: any
): Promise<void> {
    const { targetRole, title, description, roleMembers, adminId, guildId, guildName } = announcementData;

    // Set cooldown
    announcementCooldowns.set(guildId, Date.now());

    // Create progress embed
    const progressEmbed = createAdminEmbed('Announcement in Progress')
        .setDescription(
            `${EMOJIS.ACTIONS.ROCKET} **Sending announcements...**\n\n` +
            `${EMOJIS.MISC.CLOCK} Please wait while DMs are being sent to **${roleMembers.length}** members.`
        )
        .addFields(
            {
                name: `${EMOJIS.ACTIONS.LIGHTNING} Status`,
                value: `${EMOJIS.MISC.CLOCK} Starting...`,
                inline: true
            },
            {
                name: `${EMOJIS.ECONOMY.CHART} Progress`,
                value: `0/${roleMembers.length}`,
                inline: true
            }
        )
        .setColor(COLORS.INFO);

    await interaction.editReply({
        embeds: [progressEmbed],
        components: []
    });

    // Create the DM embed
    const dmEmbed = createAdminEmbed(title)
        .setDescription(description)
        .addFields(
            {
                name: `${EMOJIS.MISC.SCROLL} Server`,
                value: guildName,
                inline: true
            },
            {
                name: `${EMOJIS.MISC.CLOCK} Sent`,
                value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: true
            }
        )
        .setFooter({
            text: `Official announcement from ${guildName} administration`
        });

    let successCount = 0;
    let failureCount = 0;
    const failedUsers: string[] = [];

    // Send DMs with rate limiting
    for (let i = 0; i < roleMembers.length; i++) {
        const member = roleMembers[i];

        try {
            await member.send({ embeds: [dmEmbed] });
            successCount++;

            console.log(`[Announcement] Successfully sent DM to ${member.user.tag} (${member.id})`);
        } catch (error) {
            failureCount++;
            failedUsers.push(member.user.tag);

            console.log(`[Announcement] Failed to send DM to ${member.user.tag} (${member.id}):`, error);
        }

        // Update progress every 5 users or on last user
        if ((i + 1) % 5 === 0 || i === roleMembers.length - 1) {
            const updatedProgressEmbed = createAdminEmbed('Announcement in Progress')
                .setDescription(
                    `${EMOJIS.ACTIONS.ROCKET} **Sending announcements...**\n\n` +
                    `${EMOJIS.MISC.CLOCK} Progress: **${i + 1}/${roleMembers.length}** members processed`
                )
                .addFields(
                    {
                        name: `${EMOJIS.SUCCESS.CHECK} Successful`,
                        value: `${successCount}`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.ADMIN.WARNING} Failed`,
                        value: `${failureCount}`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.ECONOMY.CHART} Progress`,
                        value: `${i + 1}/${roleMembers.length}`,
                        inline: true
                    }
                )
                .setColor(COLORS.INFO);

            try {
                await interaction.editReply({ embeds: [updatedProgressEmbed] });
            } catch (editError) {
                console.error('[Announcement] Failed to update progress:', editError);
            }
        }

        // Rate limiting delay (except for last message)
        if (i < roleMembers.length - 1) {
            await new Promise(resolve => setTimeout(resolve, DM_DELAY));
        }
    }

    // Create final results embed
    const resultsEmbed = createSuccessEmbed('Announcement Complete!')
        .setDescription(
            `${EMOJIS.SUCCESS.PARTY} **Announcement delivery finished!**\n\n` +
            `Your message has been sent to members with the **${targetRole.name}** role.`
        )
        .addFields(
            {
                name: `${EMOJIS.SUCCESS.CHECK} Successfully Delivered`,
                value: `**${successCount}** members`,
                inline: true
            },
            {
                name: `${EMOJIS.ADMIN.WARNING} Failed Deliveries`,
                value: `**${failureCount}** members`,
                inline: true
            },
            {
                name: `${EMOJIS.ECONOMY.CHART} Success Rate`,
                value: `**${Math.round((successCount / roleMembers.length) * 100)}%**`,
                inline: true
            },
            {
                name: `${EMOJIS.MISC.BOOK} Announcement Title`,
                value: title,
                inline: false
            },
            {
                name: `${EMOJIS.MISC.CLOCK} Completed`,
                value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: false
            }
        );

    if (failureCount > 0 && failedUsers.length > 0) {
        const failedList = failedUsers.slice(0, 10).join(', ') + (failedUsers.length > 10 ? ` and ${failedUsers.length - 10} more...` : '');
        resultsEmbed.addFields({
            name: `${EMOJIS.MISC.MAGNIFYING} Failed Recipients`,
            value: failedList,
            inline: false
        });
    }

    resultsEmbed.setFooter({
        text: 'Announcement logged for audit purposes'
    });

    await interaction.editReply({
        embeds: [resultsEmbed],
        components: []
    });

    // Log the announcement for audit purposes
    console.log(`[Announcement] Completed by ${interaction.user.tag} (${adminId}) in ${guildName} (${guildId})`);
    console.log(`[Announcement] Role: ${targetRole.name}, Recipients: ${roleMembers.length}, Success: ${successCount}, Failed: ${failureCount}`);
}

// Export the processAnnouncement function for use in button interactions
module.exports.processAnnouncement = processAnnouncement;
