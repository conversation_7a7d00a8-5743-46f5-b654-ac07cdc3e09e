/**
 * Escrow Manager Unit Tests
 * Tests for secure escrow operations
 */

import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from '@jest/jest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { EscrowManager } from '../../src/services/trade/managers/EscrowManager';
import { User, Transaction, EscrowTransaction, Trade } from '../../src/models';
import { TRADE } from '../../src/config/constants';

describe('EscrowManager', () => {
  let mongoServer: MongoMemoryServer;
  let escrowManager: EscrowManager;
  let mockApp: any;

  // Test data
  const testGuildId = '123456789012345678';
  const testSellerId = '111111111111111111';
  const testBuyerId = '222222222222222222';
  const testTradeAmount = 1000;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear database
    await User.deleteMany({});
    await Transaction.deleteMany({});
    await EscrowTransaction.deleteMany({});
    await Trade.deleteMany({});

    // Create mock app
    mockApp = {
      logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
        debug: jest.fn()
      }
    };

    escrowManager = new EscrowManager(mockApp);
    await escrowManager.initialize();

    // Create test users
    await User.create([
      { discordId: testSellerId, balance: 0 },
      { discordId: testBuyerId, balance: testTradeAmount + 500 }
    ]);
  });

  afterEach(async () => {
    // Clean up after each test
    await User.deleteMany({});
    await Transaction.deleteMany({});
    await EscrowTransaction.deleteMany({});
    await Trade.deleteMany({});
  });

  describe('lockEscrow', () => {
    it('should successfully lock funds in escrow', async () => {
      const session = await mongoose.startSession();
      
      try {
        await session.withTransaction(async () => {
          // Create test trade
          const trade = await Trade.create([{
            tradeId: 'test_trade_001',
            sellerId: testSellerId,
            buyerId: testBuyerId,
            guildId: testGuildId,
            amount: testTradeAmount,
            itemDescription: 'Test Item',
            state: TRADE.STATES.ACCEPTED,
            initiatedBy: 'SELLER',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
            escrowLocked: false,
            escrowAmount: 0,
            sellerConfirmed: false,
            buyerConfirmed: false,
            warningsSent: 0,
            extensionGranted: false
          }], { session });

          // Lock escrow
          const escrowTransaction = await escrowManager.lockEscrow(trade[0], session);

          // Verify escrow transaction
          expect(escrowTransaction.tradeId).toBe('test_trade_001');
          expect(escrowTransaction.discordId).toBe(testBuyerId);
          expect(escrowTransaction.amount).toBe(testTradeAmount);
          expect(escrowTransaction.transactionType).toBe('LOCK');
          expect(escrowTransaction.status).toBe('COMPLETED');

          // Verify buyer balance was deducted
          const buyer = await User.findOne({ discordId: testBuyerId }).session(session);
          expect(buyer?.balance).toBe(500); // Original 1500 - 1000

          // Verify trade escrow status
          expect(trade[0].escrowLocked).toBe(true);
          expect(trade[0].escrowAmount).toBe(testTradeAmount);

          // Verify transaction record
          const transaction = await Transaction.findOne({ 
            discordId: testBuyerId, 
            type: 'trade_escrow' 
          }).session(session);
          expect(transaction).toBeTruthy();
          expect(transaction?.amount).toBe(-testTradeAmount);
        });
      } finally {
        await session.endSession();
      }
    });

    it('should fail when buyer has insufficient balance', async () => {
      const session = await mongoose.startSession();
      
      try {
        await session.withTransaction(async () => {
          // Update buyer to have insufficient balance
          await User.updateOne(
            { discordId: testBuyerId }, 
            { balance: testTradeAmount - 100 },
            { session }
          );

          const trade = await Trade.create([{
            tradeId: 'test_trade_002',
            sellerId: testSellerId,
            buyerId: testBuyerId,
            guildId: testGuildId,
            amount: testTradeAmount,
            itemDescription: 'Test Item',
            state: TRADE.STATES.ACCEPTED,
            initiatedBy: 'SELLER',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
            escrowLocked: false,
            escrowAmount: 0,
            sellerConfirmed: false,
            buyerConfirmed: false,
            warningsSent: 0,
            extensionGranted: false
          }], { session });

          // Attempt to lock escrow should fail
          await expect(escrowManager.lockEscrow(trade[0], session))
            .rejects.toThrow('Insufficient balance');
        });
      } finally {
        await session.endSession();
      }
    });
  });

  describe('releaseEscrow', () => {
    it('should successfully release funds to seller', async () => {
      const session = await mongoose.startSession();
      
      try {
        await session.withTransaction(async () => {
          // Create trade with locked escrow
          const trade = await Trade.create([{
            tradeId: 'test_trade_003',
            sellerId: testSellerId,
            buyerId: testBuyerId,
            guildId: testGuildId,
            amount: testTradeAmount,
            itemDescription: 'Test Item',
            state: TRADE.STATES.ACTIVE,
            initiatedBy: 'SELLER',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
            escrowLocked: true,
            escrowAmount: testTradeAmount,
            sellerConfirmed: false,
            buyerConfirmed: false,
            warningsSent: 0,
            extensionGranted: false
          }], { session });

          // Deduct buyer balance (simulate locked escrow)
          await User.updateOne(
            { discordId: testBuyerId },
            { $inc: { balance: -testTradeAmount } },
            { session }
          );

          // Release escrow
          const escrowTransaction = await escrowManager.releaseEscrow(
            trade[0], 
            'Trade completed successfully', 
            session
          );

          // Verify escrow transaction
          expect(escrowTransaction.tradeId).toBe('test_trade_003');
          expect(escrowTransaction.discordId).toBe(testSellerId);
          expect(escrowTransaction.amount).toBe(testTradeAmount);
          expect(escrowTransaction.transactionType).toBe('RELEASE');
          expect(escrowTransaction.status).toBe('COMPLETED');

          // Verify seller balance was increased
          const seller = await User.findOne({ discordId: testSellerId }).session(session);
          expect(seller?.balance).toBe(testTradeAmount);

          // Verify trade escrow status
          expect(trade[0].escrowLocked).toBe(false);

          // Verify transaction record
          const transaction = await Transaction.findOne({ 
            discordId: testSellerId, 
            type: 'trade_release' 
          }).session(session);
          expect(transaction).toBeTruthy();
          expect(transaction?.amount).toBe(testTradeAmount);
        });
      } finally {
        await session.endSession();
      }
    });

    it('should fail when no funds are in escrow', async () => {
      const session = await mongoose.startSession();
      
      try {
        await session.withTransaction(async () => {
          const trade = await Trade.create([{
            tradeId: 'test_trade_004',
            sellerId: testSellerId,
            buyerId: testBuyerId,
            guildId: testGuildId,
            amount: testTradeAmount,
            itemDescription: 'Test Item',
            state: TRADE.STATES.ACTIVE,
            initiatedBy: 'SELLER',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
            escrowLocked: false, // No escrow locked
            escrowAmount: 0,
            sellerConfirmed: false,
            buyerConfirmed: false,
            warningsSent: 0,
            extensionGranted: false
          }], { session });

          await expect(escrowManager.releaseEscrow(trade[0], 'Test release', session))
            .rejects.toThrow('No funds in escrow');
        });
      } finally {
        await session.endSession();
      }
    });
  });

  describe('refundEscrow', () => {
    it('should successfully refund funds to buyer', async () => {
      const session = await mongoose.startSession();
      
      try {
        await session.withTransaction(async () => {
          // Create trade with locked escrow
          const trade = await Trade.create([{
            tradeId: 'test_trade_005',
            sellerId: testSellerId,
            buyerId: testBuyerId,
            guildId: testGuildId,
            amount: testTradeAmount,
            itemDescription: 'Test Item',
            state: TRADE.STATES.CANCELLED,
            initiatedBy: 'SELLER',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
            escrowLocked: true,
            escrowAmount: testTradeAmount,
            sellerConfirmed: false,
            buyerConfirmed: false,
            warningsSent: 0,
            extensionGranted: false
          }], { session });

          // Deduct buyer balance (simulate locked escrow)
          const originalBalance = 1500;
          await User.updateOne(
            { discordId: testBuyerId },
            { balance: originalBalance - testTradeAmount },
            { session }
          );

          // Refund escrow
          const escrowTransaction = await escrowManager.refundEscrow(
            trade[0], 
            'Trade cancelled', 
            session
          );

          // Verify escrow transaction
          expect(escrowTransaction.tradeId).toBe('test_trade_005');
          expect(escrowTransaction.discordId).toBe(testBuyerId);
          expect(escrowTransaction.amount).toBe(testTradeAmount);
          expect(escrowTransaction.transactionType).toBe('REFUND');
          expect(escrowTransaction.status).toBe('COMPLETED');

          // Verify buyer balance was restored
          const buyer = await User.findOne({ discordId: testBuyerId }).session(session);
          expect(buyer?.balance).toBe(originalBalance);

          // Verify trade escrow status
          expect(trade[0].escrowLocked).toBe(false);

          // Verify transaction record
          const transaction = await Transaction.findOne({ 
            discordId: testBuyerId, 
            type: 'trade_refund' 
          }).session(session);
          expect(transaction).toBeTruthy();
          expect(transaction?.amount).toBe(testTradeAmount);
        });
      } finally {
        await session.endSession();
      }
    });
  });

  describe('splitEscrow', () => {
    it('should successfully split funds between parties', async () => {
      const session = await mongoose.startSession();
      
      try {
        await session.withTransaction(async () => {
          const sellerAmount = 600;
          const buyerAmount = 400;

          // Create trade with locked escrow
          const trade = await Trade.create([{
            tradeId: 'test_trade_006',
            sellerId: testSellerId,
            buyerId: testBuyerId,
            guildId: testGuildId,
            amount: testTradeAmount,
            itemDescription: 'Test Item',
            state: TRADE.STATES.DISPUTED,
            initiatedBy: 'SELLER',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
            escrowLocked: true,
            escrowAmount: testTradeAmount,
            sellerConfirmed: false,
            buyerConfirmed: false,
            warningsSent: 0,
            extensionGranted: false
          }], { session });

          // Deduct buyer balance (simulate locked escrow)
          await User.updateOne(
            { discordId: testBuyerId },
            { balance: 500 }, // Original 1500 - 1000
            { session }
          );

          // Split escrow
          const result = await escrowManager.splitEscrow(
            trade[0],
            sellerAmount,
            buyerAmount,
            'Dispute resolved with split',
            session
          );

          // Verify both transactions exist
          expect(result.sellerTransaction).toBeTruthy();
          expect(result.buyerTransaction).toBeTruthy();

          // Verify seller transaction
          expect(result.sellerTransaction.discordId).toBe(testSellerId);
          expect(result.sellerTransaction.amount).toBe(sellerAmount);
          expect(result.sellerTransaction.transactionType).toBe('RELEASE');

          // Verify buyer transaction
          expect(result.buyerTransaction?.discordId).toBe(testBuyerId);
          expect(result.buyerTransaction?.amount).toBe(buyerAmount);
          expect(result.buyerTransaction?.transactionType).toBe('REFUND');

          // Verify final balances
          const seller = await User.findOne({ discordId: testSellerId }).session(session);
          const buyer = await User.findOne({ discordId: testBuyerId }).session(session);
          
          expect(seller?.balance).toBe(sellerAmount);
          expect(buyer?.balance).toBe(500 + buyerAmount); // Original remaining + refund

          // Verify trade escrow status
          expect(trade[0].escrowLocked).toBe(false);
        });
      } finally {
        await session.endSession();
      }
    });

    it('should fail when split amounts do not equal total escrow', async () => {
      const session = await mongoose.startSession();
      
      try {
        await session.withTransaction(async () => {
          const trade = await Trade.create([{
            tradeId: 'test_trade_007',
            sellerId: testSellerId,
            buyerId: testBuyerId,
            guildId: testGuildId,
            amount: testTradeAmount,
            itemDescription: 'Test Item',
            state: TRADE.STATES.DISPUTED,
            initiatedBy: 'SELLER',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
            escrowLocked: true,
            escrowAmount: testTradeAmount,
            sellerConfirmed: false,
            buyerConfirmed: false,
            warningsSent: 0,
            extensionGranted: false
          }], { session });

          // Attempt split with incorrect amounts
          await expect(escrowManager.splitEscrow(
            trade[0],
            600, // Total = 900, but escrow is 1000
            300,
            'Invalid split',
            session
          )).rejects.toThrow('Split amounts must equal total escrow amount');
        });
      } finally {
        await session.endSession();
      }
    });
  });

  describe('getEscrowBalance', () => {
    it('should return correct escrow balance for a trade', async () => {
      // This test would require setting up escrow transactions
      // and testing the static method
      const balance = await EscrowTransaction.getEscrowBalance('nonexistent_trade');
      expect(balance).toBe(0);
    });
  });

  describe('getUserEscrowedAmount', () => {
    it('should return total escrowed amount for a user', async () => {
      // This test would require setting up multiple escrow transactions
      // and testing the static method
      const amount = await EscrowTransaction.getUserEscrowedAmount('nonexistent_user');
      expect(amount).toBe(0);
    });
  });
});
