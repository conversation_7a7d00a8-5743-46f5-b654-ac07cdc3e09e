/**
 * Command Manager
 * Centralized command loading and management system
 */
import { Collection } from 'discord.js';
import { BaseCommand, CommandRegistry, CommandCategory } from './base/BaseCommand';
/**
 * Command loading statistics
 */
interface CommandLoadStats {
    totalLoaded: number;
    newArchitecture: number;
    legacyCommands: number;
    failedLoads: number;
    categories: Record<CommandCategory, number>;
}
/**
 * Command manager for loading and organizing commands
 */
export declare class CommandManager {
    private logger;
    private registry;
    private discordCommands;
    constructor();
    /**
     * Load all commands (both new and legacy)
     */
    loadCommands(): Promise<CommandLoadStats>;
    /**
     * Load new architecture commands
     */
    private loadNewCommands;
    /**
     * Load legacy commands from files
     */
    private loadLegacyCommands;
    /**
     * Get all command files recursively
     */
    private getCommandFiles;
    /**
     * Categorize legacy commands based on name
     */
    private categorizeLegacyCommand;
    /**
     * Get Discord commands collection
     */
    getDiscordCommands(): Collection<string, any>;
    /**
     * Get command registry
     */
    getRegistry(): CommandRegistry;
    /**
     * Get command by name
     */
    getCommand(name: string): any;
    /**
     * Get commands by category
     */
    getCommandsByCategory(category: CommandCategory): BaseCommand[];
    /**
     * Get command statistics
     */
    getStats(): any;
    /**
     * Clear all commands
     */
    clear(): void;
}
/**
 * Global command manager instance
 */
export declare const commandManager: CommandManager;
export default CommandManager;
//# sourceMappingURL=CommandManager.d.ts.map