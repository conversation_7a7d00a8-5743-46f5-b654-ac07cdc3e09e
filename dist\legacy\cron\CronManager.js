"use strict";
/**
 * Legacy Cron Manager
 * Extracted cron job management from monolithic index.ts
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyCronManager = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const taxService_1 = require("../../services/taxService");
/**
 * Cron job manager for legacy compatibility
 */
class LegacyCronManager {
    constructor(client) {
        this.jobs = new Map();
        this.client = client;
    }
    /**
     * Initialize all cron jobs
     */
    initializeJobs() {
        this.initializeTaxCollection();
        this.initializeMilestoneTracking();
        console.log('[Legacy Cron] All cron jobs initialized');
    }
    /**
     * Initialize tax collection cron job (runs every hour)
     */
    initializeTaxCollection() {
        const job = node_cron_1.default.schedule('0 * * * *', async () => {
            console.log('[Tax Collection] Running scheduled tax collection check...');
            try {
                // Process tax collection for all guilds the bot is in
                for (const [guildId, guild] of this.client.guilds.cache) {
                    try {
                        const result = await (0, taxService_1.processTaxCollection)(this.client, guildId);
                        if (result.totalProcessed > 0) {
                            console.log(`[Tax Collection] Guild ${guild.name}: Processed ${result.totalProcessed}, ` +
                                `Taxed ${result.totalTaxed}, Roles Removed ${result.totalRolesRemoved}`);
                            if (result.errors.length > 0) {
                                console.error(`[Tax Collection] Guild ${guild.name} errors:`, result.errors);
                            }
                        }
                    }
                    catch (error) {
                        console.error(`[Tax Collection] Failed for guild ${guild.name}:`, error);
                    }
                }
            }
            catch (error) {
                console.error('[Tax Collection] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.jobs.set('taxCollection', job);
        console.log('[Tax Collection] Cron job initialized - running every hour');
    }
    /**
     * Initialize milestone tracking cron job (runs daily at midnight UTC)
     */
    initializeMilestoneTracking() {
        const job = node_cron_1.default.schedule('0 0 * * *', async () => {
            console.log('[Milestone Tracking] Running daily milestone check...');
            try {
                // Process login streak tracking for all guilds
                for (const [guildId, guild] of this.client.guilds.cache) {
                    try {
                        // This will be handled by the milestone service when users are active
                        // The cron job mainly serves as a daily reset trigger
                        console.log(`[Milestone Tracking] Daily reset processed for guild ${guild.name}`);
                    }
                    catch (error) {
                        console.error(`[Milestone Tracking] Failed for guild ${guild.name}:`, error);
                    }
                }
            }
            catch (error) {
                console.error('[Milestone Tracking] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.jobs.set('milestoneTracking', job);
        console.log('[Milestone Tracking] Daily cron job initialized - running at midnight UTC');
    }
    /**
     * Stop a specific cron job
     */
    stopJob(jobName) {
        const job = this.jobs.get(jobName);
        if (job) {
            job.stop();
            console.log(`[Legacy Cron] Stopped job: ${jobName}`);
            return true;
        }
        return false;
    }
    /**
     * Start a specific cron job
     */
    startJob(jobName) {
        const job = this.jobs.get(jobName);
        if (job) {
            job.start();
            console.log(`[Legacy Cron] Started job: ${jobName}`);
            return true;
        }
        return false;
    }
    /**
     * Stop all cron jobs
     */
    stopAllJobs() {
        for (const [jobName, job] of this.jobs) {
            job.stop();
            console.log(`[Legacy Cron] Stopped job: ${jobName}`);
        }
    }
    /**
     * Start all cron jobs
     */
    startAllJobs() {
        for (const [jobName, job] of this.jobs) {
            job.start();
            console.log(`[Legacy Cron] Started job: ${jobName}`);
        }
    }
    /**
     * Destroy all cron jobs
     */
    destroyAllJobs() {
        for (const [jobName, job] of this.jobs) {
            job.destroy();
            console.log(`[Legacy Cron] Destroyed job: ${jobName}`);
        }
        this.jobs.clear();
    }
    /**
     * Get job status
     */
    getJobStatus(jobName) {
        const job = this.jobs.get(jobName);
        if (!job)
            return null;
        // node-cron ScheduledTask does not have a 'running' property
        // We'll just check if the job exists (scheduled) or not
        return job ? 'scheduled' : 'stopped';
    }
    /**
     * Get all job statuses
     */
    getAllJobStatuses() {
        const statuses = {};
        for (const [jobName, job] of this.jobs) {
            // node-cron ScheduledTask does not have a 'running' property
            statuses[jobName] = job ? 'scheduled' : 'stopped';
        }
        return statuses;
    }
    /**
     * Get job count
     */
    getJobCount() {
        return this.jobs.size;
    }
}
exports.LegacyCronManager = LegacyCronManager;
exports.default = LegacyCronManager;
//# sourceMappingURL=CronManager.js.map