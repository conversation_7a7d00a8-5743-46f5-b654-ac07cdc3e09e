/**
 * Legacy Database Initializer
 * Extracted from monolithic index.ts for backward compatibility
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Database initialization and cleanup
 */
export class LegacyDatabaseInitializer {
  /**
   * Initialize database connection and perform cleanup
   */
  static async initialize(): Promise<void> {
    try {
      // Connect to MongoDB
      await mongoose.connect(process.env.MONGODB_URI as string);
      console.log('Connected to MongoDB');

      // Perform database initialization
      await this.initializeDatabase();
    } catch (error) {
      console.error('MongoDB connection error:', error);
      throw error;
    }
  }

  /**
   * Database cleanup and initialization
   */
  private static async initializeDatabase(): Promise<void> {
    try {
      console.log('Initializing database...');

      // Get the users collection
      const db = mongoose.connection.db;
      if (!db) {
        throw new Error('Database connection not established');
      }
      const usersCollection = db.collection('users');

      // Check for existing indexes
      const indexes = await usersCollection.indexes();
      console.log('Existing indexes:', indexes.map(idx => idx.name));

      // Remove old userId index if it exists
      try {
        await usersCollection.dropIndex('userId_1');
        console.log('Dropped old userId_1 index');
      } catch (error) {
        // Index might not exist, which is fine
        console.log('userId_1 index not found (this is expected)');
      }

      // Clean up any records with null discordId
      const deleteResult = await usersCollection.deleteMany({
        $or: [
          { discordId: null },
          { discordId: { $exists: false } },
          { userId: { $exists: true } } // Remove old schema records
        ]
      });

      if (deleteResult.deletedCount > 0) {
        console.log(`Cleaned up ${deleteResult.deletedCount} corrupted user records`);
      }

      // Ensure proper index on discordId
      await usersCollection.createIndex({ discordId: 1 }, { unique: true });
      console.log('Ensured discordId index exists');

      console.log('Database initialization complete');
    } catch (error) {
      console.error('Database initialization error:', error);
      throw error;
    }
  }

  /**
   * Get database connection status
   */
  static getConnectionStatus(): string {
    const states = ['disconnected', 'connected', 'connecting', 'disconnecting'];
    return states[mongoose.connection.readyState] || 'unknown';
  }

  /**
   * Close database connection
   */
  static async close(): Promise<void> {
    try {
      await mongoose.disconnect();
      console.log('Database connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
      throw error;
    }
  }
}

export default LegacyDatabaseInitializer;
