/**
 * Base Service Class
 * Abstract base class for all application services
 */
import { IService, ILogger, IApplicationContext } from '../../core/interfaces';
/**
 * Abstract base service class
 */
export declare abstract class BaseService implements IService {
    abstract readonly name: string;
    protected logger: ILogger;
    protected app?: IApplicationContext;
    constructor(app?: IApplicationContext);
    /**
     * Initialize the service
     */
    initialize?(): Promise<void>;
    /**
     * Shutdown the service
     */
    shutdown?(): Promise<void>;
    /**
     * Override in subclasses for initialization logic
     */
    protected onInitialize(): Promise<void>;
    /**
     * Override in subclasses for shutdown logic
     */
    protected onShutdown(): Promise<void>;
    /**
     * Check if a feature is enabled
     */
    protected isFeatureEnabled(featureName: string): boolean;
    /**
     * Get another service from the application context
     */
    protected getService<T extends IService>(serviceName: string): T;
    /**
     * Handle service errors with consistent logging
     */
    protected handleError(error: any, context?: any): void;
    /**
     * Log service operations
     */
    protected logOperation(operation: string, details?: any): void;
    /**
     * Validate required dependencies
     */
    protected validateDependencies(dependencies: string[]): void;
    /**
     * Create a child logger with additional context
     */
    protected createChildLogger(context: string): ILogger;
}
/**
 * Service registry for managing service instances
 */
export declare class ServiceRegistry {
    private services;
    private logger;
    constructor();
    /**
     * Register a service
     */
    register(service: IService): void;
    /**
     * Get a service by name
     */
    get<T extends IService>(name: string): T;
    /**
     * Check if a service is registered
     */
    has(name: string): boolean;
    /**
     * Get all registered services
     */
    getAll(): IService[];
    /**
     * Unregister a service
     */
    unregister(name: string): boolean;
    /**
     * Clear all services
     */
    clear(): void;
}
/**
 * Global service registry instance
 */
export declare const serviceRegistry: ServiceRegistry;
//# sourceMappingURL=BaseService.d.ts.map