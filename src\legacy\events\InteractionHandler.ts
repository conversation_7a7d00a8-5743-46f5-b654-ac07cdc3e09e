/**
 * Legacy Interaction Handler
 * Extracted interaction handling from monolithic index.ts
 */

import { Client, Interaction, ButtonInteraction } from 'discord.js';
import { handleCommandError, handleButtonError } from '../../utils/errorHandler';
import { EMOJIS } from '../../utils/embedBuilder';

/**
 * Interaction handler for legacy compatibility
 */
export class LegacyInteractionHandler {
  private client: Client;

  constructor(client: Client) {
    this.client = client;
  }

  /**
   * Handle interaction create events
   */
  async handleInteraction(interaction: Interaction): Promise<void> {
    if (interaction.isChatInputCommand()) {
      await this.handleChatInputCommand(interaction);
    } else if (interaction.isButton()) {
      await this.handleButtonInteraction(interaction);
    }
  }

  /**
   * Handle chat input command interactions
   */
  private async handleChatInputCommand(interaction: any): Promise<void> {
    const command = (this.client as any).commands.get(interaction.commandName);
    if (!command) return;

    try {
      await command.execute(interaction);
    } catch (error) {
      await handleCommandError(interaction, error);
    }
  }

  /**
   * Handle button interactions
   */
  private async handleButtonInteraction(interaction: ButtonInteraction): Promise<void> {
    try {
      const { customId } = interaction;

      if (customId === 'quick_balance') {
        await this.handleQuickCommand(interaction, 'balance');
      } else if (customId === 'quick_leaderboard') {
        await this.handleQuickCommand(interaction, 'leaderboard');
      } else if (customId === 'quick_roles') {
        await this.handleQuickCommand(interaction, 'roles');
      } else if (customId.startsWith('buy_role_')) {
        await this.handleRoleAchievementInfo(interaction, customId);
      } else if (customId === 'announce_confirm') {
        await this.handleAnnouncementConfirm(interaction);
      } else if (customId === 'announce_cancel') {
        await this.handleAnnouncementCancel(interaction);
      } else if (customId.startsWith('help_')) {
        await this.handleHelpButton(interaction, customId);
      } else {
        await interaction.reply({
          content: 'This button interaction is not yet implemented.',
          ephemeral: true
        });
      }
    } catch (error) {
      await handleButtonError(interaction, error);
    }
  }

  /**
   * Handle quick command buttons
   */
  private async handleQuickCommand(interaction: ButtonInteraction, commandName: string): Promise<void> {
    const command = (this.client as any).commands.get(commandName);
    if (command) {
      await command.execute(interaction);
    } else {
      await interaction.reply({
        content: `Command "${commandName}" not found.`,
        ephemeral: true
      });
    }
  }

  /**
   * Handle role achievement info buttons
   */
  private async handleRoleAchievementInfo(interaction: ButtonInteraction, customId: string): Promise<void> {
    const roleId = customId.replace('buy_role_', '');
    await interaction.reply({
      content: `Role achievements are automatically unlocked when you reach the required PLC balance! Keep earning coins to unlock this achievement.`,
      ephemeral: true
    });
  }

  /**
   * Handle announcement confirmation
   */
  private async handleAnnouncementConfirm(interaction: ButtonInteraction): Promise<void> {
    const pendingAnnouncements = (global as any).pendingAnnouncements;
    if (!pendingAnnouncements) {
      await interaction.reply({
        content: 'No pending announcements found. Please try the command again.',
        ephemeral: true
      });
      return;
    }

    const originalInteractionId = interaction.message?.interaction?.id;
    const announcementData = pendingAnnouncements.get(originalInteractionId);

    if (!announcementData) {
      await interaction.reply({
        content: 'Announcement data not found or expired. Please try the command again.',
        ephemeral: true
      });
      return;
    }

    // Clean up the pending announcement
    pendingAnnouncements.delete(originalInteractionId);

    // Process the announcement
    await interaction.deferUpdate();
    const announceModule = require('../../commands/announce');
    await announceModule.processAnnouncement(interaction, announcementData);
  }

  /**
   * Handle announcement cancellation
   */
  private async handleAnnouncementCancel(interaction: ButtonInteraction): Promise<void> {
    const pendingAnnouncements = (global as any).pendingAnnouncements;
    const originalInteractionId = interaction.message?.interaction?.id;

    if (pendingAnnouncements && originalInteractionId) {
      pendingAnnouncements.delete(originalInteractionId);
    }

    await interaction.update({
      content: `${EMOJIS.ADMIN.WARNING} Announcement cancelled.`,
      embeds: [],
      components: []
    });
  }

  /**
   * Handle help command buttons
   */
  private async handleHelpButton(interaction: ButtonInteraction, customId: string): Promise<void> {
    const commandName = customId.replace('help_', '');

    // Commands that can be executed directly
    const instantTriggerCommands = ['balance', 'roles', 'leaderboard', 'history'];

    if (instantTriggerCommands.includes(commandName)) {
      // Execute the command directly
      const command = (this.client as any).commands.get(commandName);
      if (command) {
        await command.execute(interaction);
      } else {
        await interaction.reply({
          content: `Command "${commandName}" not found.`,
          ephemeral: true
        });
      }
    } else {
      // For other commands, show usage information
      const commandDescriptions: Record<string, string> = {
        'pay': 'Transfer coins to another user. Usage: `/pay @user amount`',
        'addrole': '**[Admin Only]** Add a role achievement. Usage: `/addrole @role price`',
        'editrole': '**[Admin Only]** Edit a role achievement. Usage: `/editrole role_name new_price`',
        'removerole': '**[Admin Only]** Remove a role achievement. Usage: `/removerole role_name`',
        'give': '**[Admin Only]** Give coins to a user. Usage: `/give @user amount`',
        'fine': '**[Admin Only]** Remove coins from a user. Usage: `/fine @user amount`',
        'tax': '**[Admin Only]** Configure automatic taxation system. Usage: `/tax status:on/off frequency:weeks amount:plc role:@role`',
        'starterbalance': '**[Admin Only]** Manage starter balance rules. Usage: `/starterbalance action:add/edit/remove/list role:@role amount:plc`',
        'incomecredentials': '**[Admin Only]** Customize income earning guide text displayed in /help command. Supports line breaks (\\n) and formatting. Usage: `/incomecredentials text:"Your custom income guide text"`',
        'automessage': '**[Admin Only]** Manage automated messages for server events. Usage: `/automessage action:create trigger:member_join delivery:channel name:welcome title:Welcome! description:Hello {user}!`',
        'placeholders': 'View available placeholders for automated messages. Usage: `/placeholders`',
        'testcleanup': '**[Admin Only]** Test user data cleanup functionality. Usage: `/testcleanup user:@user action:check/simulate`'
      };

      const description = commandDescriptions[commandName] || `Use the /${commandName} command.`;

      await interaction.reply({
        content: `**/${commandName}**\n${description}`,
        ephemeral: true
      });
    }
  }
}

export default LegacyInteractionHandler;
