/**
 * Legacy Application Manager
 * Orchestrates all legacy components for backward compatibility
 */
import { Client } from 'discord.js';
import { LegacyClientManager } from './client/ClientManager';
import { LegacyCronManager } from './cron/CronManager';
import { LegacyInteractionHandler } from './events/InteractionHandler';
import { LegacyMessageHandler } from './events/MessageHandler';
import { LegacyReactionHandler } from './events/ReactionHandler';
import { LegacyMemberHandler } from './events/MemberHandler';
/**
 * Legacy application manager that orchestrates all components
 */
export declare class LegacyApplication {
    private clientManager;
    private cronManager;
    private interactionHandler;
    private messageHandler;
    private reactionHandler;
    private memberHandler;
    private client;
    private isInitialized;
    constructor();
    /**
     * Initialize the legacy application
     */
    initialize(): Promise<void>;
    /**
     * Setup Discord event handlers
     */
    private setupEventHandlers;
    /**
     * Shutdown the legacy application
     */
    shutdown(): Promise<void>;
    /**
     * Get application status
     */
    getStatus(): any;
    /**
     * Get the Discord client
     */
    getClient(): Client;
    /**
     * Get component managers
     */
    getManagers(): {
        client: LegacyClientManager;
        cron: LegacyCronManager;
        interaction: LegacyInteractionHandler;
        message: LegacyMessageHandler;
        reaction: LegacyReactionHandler;
        member: LegacyMemberHandler;
    };
    /**
     * Check if application is ready
     */
    isReady(): boolean;
}
export default LegacyApplication;
//# sourceMappingURL=LegacyApplication.d.ts.map