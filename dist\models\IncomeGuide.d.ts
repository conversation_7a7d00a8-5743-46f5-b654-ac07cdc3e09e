import { Document } from 'mongoose';
export interface IIncomeGuide extends Document {
    guildId: string;
    customText: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare const IncomeGuide: import("mongoose").Model<IIncomeGuide, {}, {}, {}, Document<unknown, {}, IIncomeGuide, {}> & IIncomeGuide & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default IncomeGuide;
//# sourceMappingURL=IncomeGuide.d.ts.map