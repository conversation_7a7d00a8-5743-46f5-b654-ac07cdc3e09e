/**
 * Validation Utilities
 * Centralized validation functions with type safety
 */
/**
 * Discord ID validation
 */
export declare class DiscordValidator {
    /**
     * Validate Discord ID format
     */
    static validateDiscordId(id: string): void;
    /**
     * Validate and sanitize Discord ID
     */
    static sanitizeDiscordId(id: string): string;
}
/**
 * Amount validation for transactions
 */
export declare class AmountValidator {
    /**
     * Validate transaction amount
     */
    static validateAmount(amount: number, context?: string): void;
    /**
     * Validate positive amount
     */
    static validatePositiveAmount(amount: number, context?: string): void;
    /**
     * Validate balance sufficiency
     */
    static validateSufficientBalance(currentBalance: number, requiredAmount: number, context?: string): void;
}
/**
 * Text validation
 */
export declare class TextValidator {
    /**
     * Validate text length
     */
    static validateLength(text: string, maxLength: number, fieldName: string): void;
    /**
     * Validate description text
     */
    static validateDescription(description: string): void;
    /**
     * Validate reason text
     */
    static validateReason(reason: string): void;
    /**
     * Validate name text
     */
    static validateName(name: string): void;
    /**
     * Sanitize text input
     */
    static sanitizeText(text: string): string;
}
/**
 * Permission validation
 */
export declare class PermissionValidator {
    /**
     * Validate admin permissions
     */
    static validateAdminPermissions(member: any): void;
    /**
     * Validate guild context
     */
    static validateGuildContext(guild: any): void;
    /**
     * Validate user permissions for role management
     */
    static validateRoleManagementPermissions(member: any): void;
}
/**
 * Feature validation
 */
export declare class FeatureValidator {
    /**
     * Validate feature availability
     */
    static validateFeature(featureName: string, isEnabled: boolean): void;
    /**
     * Validate multiple features
     */
    static validateFeatures(features: Array<{
        name: string;
        enabled: boolean;
    }>): void;
}
/**
 * Rate limiting validation
 */
export declare class RateLimitValidator {
    private static lastUsage;
    /**
     * Validate rate limit
     */
    static validateRateLimit(userId: string, commandName: string, cooldownSeconds: number): void;
    /**
     * Clear rate limit for user/command
     */
    static clearRateLimit(userId: string, commandName: string): void;
    /**
     * Clear all rate limits
     */
    static clearAllRateLimits(): void;
}
/**
 * Comprehensive validation utility
 */
export declare class ValidationUtils {
    static discord: typeof DiscordValidator;
    static amount: typeof AmountValidator;
    static text: typeof TextValidator;
    static permission: typeof PermissionValidator;
    static feature: typeof FeatureValidator;
    static rateLimit: typeof RateLimitValidator;
    /**
     * Validate common command parameters
     */
    static validateCommandParams(params: {
        userId?: string;
        amount?: number;
        text?: string;
        adminRequired?: boolean;
        member?: any;
        guild?: any;
        features?: string[];
    }): void;
}
export default ValidationUtils;
//# sourceMappingURL=ValidationUtils.d.ts.map