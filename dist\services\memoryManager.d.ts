interface MemoryStats {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
    timestamp: number;
}
declare class MemoryManager {
    private static instance;
    private memoryStats;
    private readonly MAX_MEMORY_STATS;
    private readonly MEMORY_CHECK_INTERVAL;
    private readonly MAX_HEAP_USAGE_MB;
    private memoryCheckTimer?;
    private constructor();
    static getInstance(): MemoryManager;
    /**
     * Start periodic memory monitoring
     */
    private startMemoryMonitoring;
    /**
     * Stop memory monitoring
     */
    stopMemoryMonitoring(): void;
    /**
     * Record current memory statistics
     */
    private recordMemoryStats;
    /**
     * Check if memory usage is concerning
     */
    private checkMemoryUsage;
    /**
     * Perform emergency cleanup when memory usage is high
     */
    private performEmergencyCleanup;
    /**
     * Get current memory statistics
     */
    getMemoryStats(): {
        current: MemoryStats;
        average: Partial<MemoryStats>;
    };
}
export default MemoryManager;
//# sourceMappingURL=memoryManager.d.ts.map