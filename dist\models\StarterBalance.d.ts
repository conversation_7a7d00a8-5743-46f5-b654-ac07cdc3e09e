import { Document } from 'mongoose';
export interface IStarterBalance extends Document {
    guildId: string;
    roleId: string;
    roleName: string;
    amount: number;
    createdAt: Date;
    updatedAt: Date;
}
export declare const StarterBalance: import("mongoose").Model<IStarterBalance, {}, {}, {}, Document<unknown, {}, IStarterBalance, {}> & IStarterBalance & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=StarterBalance.d.ts.map