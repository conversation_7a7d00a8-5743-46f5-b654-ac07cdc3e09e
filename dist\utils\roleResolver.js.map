{"version": 3, "file": "roleResolver.js", "sourceRoot": "", "sources": ["../../src/utils/roleResolver.ts"], "names": [], "mappings": ";;AAaA,kCAoGC;AA8CD,0DA8BC;AA5LD,iDAAiD;AAQjD;;;GAGG;AACI,KAAK,UAAU,WAAW,CAAC,KAAY,EAAE,KAAa;IACzD,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,8BAAe,CAAC,iCAAiC,CAAC,CAAC;IACjE,CAAC;IAED,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IAElC,2DAA2D;IAC3D,IAAI,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;QACnC,IAAI,CAAC;YACD,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACnD,IAAI,IAAI,EAAE,CAAC;gBACP,OAAO;oBACH,IAAI;oBACJ,UAAU,EAAE,IAAI;oBAChB,UAAU,EAAE,GAAG;iBAClB,CAAC;YACN,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,iDAAiD;QACrD,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACxC,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;IAEvF,wCAAwC;IACxC,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;IACtE,IAAI,UAAU,EAAE,CAAC;QACb,OAAO;YACH,IAAI,EAAE,UAAU;YAChB,UAAU,EAAE,YAAY;YACxB,UAAU,EAAE,GAAG;SAClB,CAAC;IACN,CAAC;IAED,mCAAmC;IACnC,MAAM,oBAAoB,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC/C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,YAAY,CAAC,WAAW,EAAE,CACzD,CAAC;IACF,IAAI,oBAAoB,EAAE,CAAC;QACvB,OAAO;YACH,IAAI,EAAE,oBAAoB;YAC1B,UAAU,EAAE,YAAY;YACxB,UAAU,EAAE,GAAG;SAClB,CAAC;IACN,CAAC;IAED,uCAAuC;IACvC,MAAM,YAAY,GAAG,SAAS;SACzB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACV,IAAI;QACJ,KAAK,EAAE,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;KACtD,CAAC,CAAC;SACF,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;SAClC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAEvC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC5B,2DAA2D;QAC3D,MAAM,WAAW,GAAG,SAAS;aACxB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;aACX,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC;aAC7B,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhB,MAAM,IAAI,8BAAe,CACrB,SAAS,YAAY,yCAAyC,WAAW,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAClH,CAAC;IACN,CAAC;IAED,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC5B,OAAO;YACH,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;YAC1B,UAAU,EAAE,YAAY;YACxB,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK;SACpC,CAAC;IACN,CAAC;IAED,0EAA0E;IAC1E,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IACjC,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAEpC,IAAI,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;QAC3C,4CAA4C;QAC5C,OAAO;YACH,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,UAAU,EAAE,YAAY;YACxB,UAAU,EAAE,QAAQ,CAAC,KAAK;SAC7B,CAAC;IACN,CAAC;IAED,6CAA6C;IAC7C,MAAM,cAAc,GAAG,YAAY;SAC9B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;SACX,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;SACpC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEhB,MAAM,IAAI,8BAAe,CACrB,yBAAyB,YAAY,MAAM,cAAc,4BAA4B,CACxF,CAAC;AACN,CAAC;AAED;;;GAGG;AACH,SAAS,mBAAmB,CAAC,QAAgB,EAAE,KAAa;IACxD,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC7C,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IAEvC,cAAc;IACd,IAAI,aAAa,KAAK,UAAU,EAAE,CAAC;QAC/B,OAAO,GAAG,CAAC;IACf,CAAC;IAED,2CAA2C;IAC3C,IAAI,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QACrC,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED,uCAAuC;IACvC,IAAI,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QACvC,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED,yBAAyB;IACzB,MAAM,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAE3C,IAAI,aAAa,GAAG,CAAC,CAAC;IACtB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACjC,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;YAC3F,aAAa,EAAE,CAAC;QACpB,CAAC;IACL,CAAC;IAED,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;QACpB,OAAO,GAAG,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,CAAC,CAAC;AACb,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CAAC,KAAY,EAAE,IAAU;IAC5D,wCAAwC;IACxC,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;IACnC,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,MAAM,IAAI,8BAAe,CAAC,+BAA+B,CAAC,CAAC;IAC/D,CAAC;IAED,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;QAC5C,MAAM,IAAI,8BAAe,CAAC,8CAA8C,CAAC,CAAC;IAC9E,CAAC;IAED,0DAA0D;IAC1D,MAAM,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC;IAC/C,IAAI,IAAI,CAAC,QAAQ,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC;QAC3C,MAAM,IAAI,8BAAe,CACrB,uBAAuB,IAAI,CAAC,IAAI,0DAA0D,CAC7F,CAAC;IACN,CAAC;IAED,8BAA8B;IAC9B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,MAAM,IAAI,8BAAe,CACrB,uBAAuB,IAAI,CAAC,IAAI,4CAA4C,CAC/E,CAAC;IACN,CAAC;IAED,mCAAmC;IACnC,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QAC5B,MAAM,IAAI,8BAAe,CAAC,kCAAkC,CAAC,CAAC;IAClE,CAAC;AACL,CAAC"}