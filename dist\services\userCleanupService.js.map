{"version": 3, "file": "userCleanupService.js", "sourceRoot": "", "sources": ["../../src/services/userCleanupService.ts"], "names": [], "mappings": ";;;;;;AACA,0DAAkC;AAClC,wEAAgD;AAChD,6DAA0D;AAC1D,wDAAsD;AACtD,wDAAgC;AAWhC;;GAEG;AACH,MAAa,kBAAkB;IAI3B;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,MAAwC;QACxE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAkB;YAC1B,OAAO,EAAE,KAAK;YACd,eAAe,EAAE,KAAK;YACtB,mBAAmB,EAAE,CAAC;YACtB,sBAAsB,EAAE,CAAC;YACzB,MAAM,EAAE,EAAE;YACV,SAAS,EAAE,CAAC;SACf,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,IAAI,eAAe,CAAC;QACxD,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,IAAI,cAAc,CAAC;QAE/E,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,QAAQ,GAAG,kCAAkC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7B,OAAO,CAAC,KAAK,CAAC,kBAAkB,QAAQ,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC1C,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,4CAA4C,QAAQ,KAAK,MAAM,cAAc,SAAS,EAAE,CAAC,CAAC;QAEtG,IAAI,CAAC;YACD,yEAAyE;YACzE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAExE,gFAAgF;YAChF,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAC5D,IAAI,YAAY,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,uBAAuB,QAAQ,iDAAiD,CAAC,CAAC;oBAC9F,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;oBACtB,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;oBAC1C,OAAO,MAAM,CAAC;gBAClB,CAAC;YACL,CAAC;YAED,yCAAyC;YACzC,IAAI,kBAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;gBACvC,MAAM,QAAQ,GAAG,0DAA0D,CAAC;gBAC5E,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC7B,OAAO,CAAC,KAAK,CAAC,kBAAkB,QAAQ,EAAE,CAAC,CAAC;gBAC5C,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC1C,OAAO,MAAM,CAAC;YAClB,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,YAAY,EAAE,CAAC;YAE9C,IAAI,CAAC;gBACD,MAAM,OAAO,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;oBACrC,gCAAgC;oBAChC,MAAM,gBAAgB,GAAG,MAAM,cAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACtF,MAAM,CAAC,eAAe,GAAG,gBAAgB,CAAC,YAAY,GAAG,CAAC,CAAC;oBAE3D,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;wBACzB,OAAO,CAAC,GAAG,CAAC,kDAAkD,QAAQ,EAAE,CAAC,CAAC;oBAC9E,CAAC;oBAED,gCAAgC;oBAChC,MAAM,uBAAuB,GAAG,MAAM,qBAAW,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACrG,MAAM,CAAC,mBAAmB,GAAG,uBAAuB,CAAC,YAAY,CAAC;oBAElE,IAAI,MAAM,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;wBACjC,OAAO,CAAC,GAAG,CAAC,0BAA0B,MAAM,CAAC,mBAAmB,4BAA4B,QAAQ,EAAE,CAAC,CAAC;oBAC5G,CAAC;oBAED,oCAAoC;oBACpC,MAAM,0BAA0B,GAAG,MAAM,+BAAc,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBAChG,MAAM,CAAC,sBAAsB,GAAG,0BAA0B,CAAC,YAAY,CAAC;oBAExE,IAAI,MAAM,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;wBACpC,OAAO,CAAC,GAAG,CAAC,0BAA0B,MAAM,CAAC,sBAAsB,gCAAgC,QAAQ,EAAE,CAAC,CAAC;oBACnH,CAAC;oBAED,cAAc;oBACd,MAAM,mBAAmB,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,mBAAmB,GAAG,MAAM,CAAC,sBAAsB,CAAC;oBAC1H,OAAO,CAAC,GAAG,CAAC,uCAAuC,mBAAmB,sBAAsB,QAAQ,SAAS,SAAS,EAAE,CAAC,CAAC;gBAC9H,CAAC,CAAC,CAAC;gBAEH,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YAE1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,MAAM,QAAQ,GAAG,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;gBAC5G,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC7B,OAAO,CAAC,KAAK,CAAC,kBAAkB,QAAQ,EAAE,CAAC,CAAC;gBAC5C,MAAM,KAAK,CAAC;YAChB,CAAC;oBAAS,CAAC;gBACP,MAAM,OAAO,CAAC,UAAU,EAAE,CAAC;YAC/B,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,QAAQ,GAAG,2BAA2B,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;YACpH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7B,OAAO,CAAC,KAAK,CAAC,kBAAkB,QAAQ,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;QAC3B,CAAC;QAED,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE1C,mBAAmB;QACnB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,CAAC,GAAG,CAAC,wCAAwC,QAAQ,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;QAC7F,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,KAAK,CAAC,qCAAqC,QAAQ,UAAU,MAAM,CAAC,SAAS,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC/G,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,MAAc;QAK5C,IAAI,CAAC;YACD,MAAM,CAAC,UAAU,EAAE,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC1E,cAAI,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;gBACnC,qBAAW,CAAC,cAAc,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;gBACjD,+BAAc,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC;aAC5C,CAAC,CAAC;YAEH,OAAO;gBACH,aAAa,EAAE,CAAC,CAAC,UAAU;gBAC3B,gBAAgB;gBAChB,mBAAmB;aACtB,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,gDAAgD,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,IAAI,4BAAa,CAAC,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACtH,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,KAAK,CAAC,mBAAmB;QAKnC,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAEhE,IAAI,CAAC;YACD,+DAA+D;YAC/D,8EAA8E;YAC9E,OAAO,CAAC,GAAG,CAAC,mGAAmG,CAAC,CAAC;YAEjH,OAAO;gBACH,aAAa,EAAE,CAAC;gBAChB,oBAAoB,EAAE,CAAC;gBACvB,uBAAuB,EAAE,CAAC;aAC7B,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,IAAI,4BAAa,CAAC,iCAAiC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACzH,CAAC;IACL,CAAC;;AAzKL,gDA0KC;AAzK2B,qCAAkB,GAAG,KAAK,CAAC,CAAC,qBAAqB;AACjD,kCAAe,GAAG,IAAI,CAAC,CAAC,uCAAuC"}