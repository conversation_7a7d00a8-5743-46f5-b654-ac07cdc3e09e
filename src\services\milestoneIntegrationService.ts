import { Client } from 'discord.js';
import { checkAndProcessMilestones } from './milestoneService';
import { processReactionReward } from './reactionRewardsService';
import { checkAndAssignRoles } from './roleAssignmentService';
import { MilestoneAuditService } from './milestoneAuditService';

/**
 * Integration service to ensure milestone system works seamlessly with existing systems
 */
export class MilestoneIntegrationService {
    
    /**
     * Processes user activity and checks for both milestones and existing rewards
     */
    static async processUserActivity(
        client: Client,
        userId: string,
        guildId: string,
        activityType: 'message' | 'voice' | 'reaction' | 'login',
        activityData: any
    ): Promise<{
        milestoneResults: import('./milestoneService').MilestoneCheckResult[];
        existingRewards: any[];
        errors: string[];
    }> {
        const results = {
            milestoneResults: [] as import('./milestoneService').MilestoneCheckResult[],
            existingRewards: [] as any[],
            errors: [] as string[]
        };

        try {
            // Process milestone achievements
            const milestoneResults = await checkAndProcessMilestones(
                client,
                userId,
                guildId,
                activityType,
                activityData
            );
            results.milestoneResults = milestoneResults;

            // Log successful milestone processing
            if (milestoneResults.length > 0) {
                console.log(`[Integration] User ${userId} achieved ${milestoneResults.length} milestones`);
            }

        } catch (error) {
            const errorMsg = `Failed to process milestones: ${error}`;
            results.errors.push(errorMsg);
            console.error('[Integration] Milestone processing error:', error);
        }

        return results;
    }

    /**
     * Ensures milestone system doesn't conflict with role assignment system
     */
    static async validateRoleCompatibility(
        client: Client,
        userId: string,
        guildId: string,
        newBalance: number
    ): Promise<{ compatible: boolean; issues: string[] }> {
        const issues: string[] = [];

        try {
            // Check if role assignment would work with current balance
            const roleResult = await checkAndAssignRoles(client, userId, guildId, newBalance);
            // Use rolesAssigned instead of assignedRoles
            if (roleResult && roleResult.rolesAssigned && roleResult.rolesAssigned.length > 0) {
                console.log(`[Integration] Role assignment compatible: ${roleResult.rolesAssigned.length} roles would be assigned`);
            }
            return { compatible: true, issues };
        } catch (error) {
            issues.push(`Role assignment compatibility check failed: ${error}`);
            console.error('[Integration] Role compatibility error:', error);
            return { compatible: false, issues };
        }
    }

    /**
     * Validates that milestone transactions are properly recorded
     */
    static async validateTransactionIntegrity(
        userId: string,
        guildId: string,
        expectedMilestoneCount: number
    ): Promise<{ valid: boolean; issues: string[] }> {
        const issues: string[] = [];

        try {
            // This would check transaction records to ensure milestone rewards are properly recorded
            // For now, we'll just validate the audit logs
            const auditSummary = await MilestoneAuditService.getUserActivitySummary(guildId, userId, 1);
            
            if (auditSummary.totalAchievements !== expectedMilestoneCount) {
                issues.push(`Milestone count mismatch: expected ${expectedMilestoneCount}, found ${auditSummary.totalAchievements}`);
            }

            return { valid: issues.length === 0, issues };
        } catch (error) {
            issues.push(`Transaction integrity check failed: ${error}`);
            console.error('[Integration] Transaction integrity error:', error);
            return { valid: false, issues };
        }
    }

    /**
     * Performs a comprehensive system health check
     */
    static async performHealthCheck(guildId: string): Promise<{
        healthy: boolean;
        milestoneSystemStatus: string;
        integrationStatus: string;
        recommendations: string[];
        errors: string[];
    }> {
        const errors: string[] = [];
        const recommendations: string[] = [];
        let milestoneSystemStatus = 'unknown';
        let integrationStatus = 'unknown';

        try {
            // Check milestone configurations
            const { createDefaultMilestoneConfigurations } = await import('./milestoneService');
            milestoneSystemStatus = 'operational';

            // Check audit system
            const securityAlerts = await MilestoneAuditService.getSecurityAlerts(guildId, 24);
            if (securityAlerts.length > 10) {
                recommendations.push('High number of security alerts - review user activity');
            }

            // Check integration points
            integrationStatus = 'operational';

            if (securityAlerts.length === 0) {
                recommendations.push('No security alerts - system running smoothly');
            }

        } catch (error) {
            errors.push(`Health check failed: ${error}`);
            console.error('[Integration] Health check error:', error);
        }

        return {
            healthy: errors.length === 0,
            milestoneSystemStatus,
            integrationStatus,
            recommendations,
            errors
        };
    }

    /**
     * Migrates existing user data to work with milestone system
     */
    static async migrateExistingUsers(guildId: string): Promise<{
        migrated: number;
        errors: string[];
        skipped: number;
    }> {
        const results: { migrated: number; errors: string[]; skipped: number } = {
            migrated: 0,
            errors: [],
            skipped: 0
        };

        try {
            // This would migrate existing users to have UserActivity records
            // For now, we'll just log that migration would happen here
            console.log(`[Integration] Migration would process existing users for guild ${guildId}`);
            
            // In a real implementation, you would:
            // 1. Find all users with transactions in this guild
            // 2. Create UserActivity records for them
            // 3. Initialize their activity based on historical data
            
            results.migrated = 0; // Placeholder
        } catch (error) {
            results.errors.push(`Migration failed: ${error}`);
            console.error('[Integration] Migration error:', error);
        }

        return results;
    }

    /**
     * Ensures milestone system respects existing rate limits and cooldowns
     */
    static async validateRateLimitCompatibility(
        userId: string,
        guildId: string
    ): Promise<{ compatible: boolean; conflicts: string[] }> {
        const conflicts: string[] = [];

        try {
            // Check if user has any existing rate limits that might conflict
            // This would check reaction reward rate limits, tax collection, etc.
            
            // For now, we'll assume compatibility
            return { compatible: true, conflicts };
        } catch (error) {
            conflicts.push(`Rate limit compatibility check failed: ${error}`);
            console.error('[Integration] Rate limit compatibility error:', error);
            return { compatible: false, conflicts };
        }
    }

    /**
     * Provides integration status report
     */
    static async getIntegrationReport(guildId: string): Promise<{
        status: 'healthy' | 'warning' | 'error';
        milestoneConfigurations: number;
        activeUsers: number;
        recentAchievements: number;
        securityAlerts: number;
        systemCompatibility: boolean;
        lastUpdated: Date;
    }> {
        try {
            const [configs, securityAlerts, auditLogs] = await Promise.all([
                import('../models/MilestoneConfiguration').then(m => m.default.countDocuments({ guildId })),
                MilestoneAuditService.getSecurityAlerts(guildId, 24),
                MilestoneAuditService.getAuditLogs(guildId, { 
                    category: 'achievement',
                    startDate: new Date(Date.now() - (24 * 60 * 60 * 1000)),
                    limit: 100
                })
            ]);

            const activeUsers = new Set(auditLogs.map(log => log.userId)).size;
            const recentAchievements = auditLogs.filter(log => log.action === 'milestone_achieved').length;

            let status: 'healthy' | 'warning' | 'error' = 'healthy';
            if (securityAlerts.length > 5) {
                status = 'warning';
            }
            if (securityAlerts.length > 20) {
                status = 'error';
            }

            return {
                status,
                milestoneConfigurations: configs,
                activeUsers,
                recentAchievements,
                securityAlerts: securityAlerts.length,
                systemCompatibility: true,
                lastUpdated: new Date()
            };
        } catch (error) {
            console.error('[Integration] Error generating integration report:', error);
            return {
                status: 'error',
                milestoneConfigurations: 0,
                activeUsers: 0,
                recentAchievements: 0,
                securityAlerts: 0,
                systemCompatibility: false,
                lastUpdated: new Date()
            };
        }
    }
}
