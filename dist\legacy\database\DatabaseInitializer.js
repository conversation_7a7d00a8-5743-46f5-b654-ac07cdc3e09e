"use strict";
/**
 * Legacy Database Initializer
 * Extracted from monolithic index.ts for backward compatibility
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyDatabaseInitializer = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
/**
 * Database initialization and cleanup
 */
class LegacyDatabaseInitializer {
    /**
     * Initialize database connection and perform cleanup
     */
    static async initialize() {
        try {
            // Connect to MongoDB
            await mongoose_1.default.connect(process.env.MONGODB_URI);
            console.log('Connected to MongoDB');
            // Perform database initialization
            await this.initializeDatabase();
        }
        catch (error) {
            console.error('MongoDB connection error:', error);
            throw error;
        }
    }
    /**
     * Database cleanup and initialization
     */
    static async initializeDatabase() {
        try {
            console.log('Initializing database...');
            // Get the users collection
            const db = mongoose_1.default.connection.db;
            if (!db) {
                throw new Error('Database connection not established');
            }
            const usersCollection = db.collection('users');
            // Check for existing indexes
            const indexes = await usersCollection.indexes();
            console.log('Existing indexes:', indexes.map(idx => idx.name));
            // Remove old userId index if it exists
            try {
                await usersCollection.dropIndex('userId_1');
                console.log('Dropped old userId_1 index');
            }
            catch (error) {
                // Index might not exist, which is fine
                console.log('userId_1 index not found (this is expected)');
            }
            // Clean up any records with null discordId
            const deleteResult = await usersCollection.deleteMany({
                $or: [
                    { discordId: null },
                    { discordId: { $exists: false } },
                    { userId: { $exists: true } } // Remove old schema records
                ]
            });
            if (deleteResult.deletedCount > 0) {
                console.log(`Cleaned up ${deleteResult.deletedCount} corrupted user records`);
            }
            // Ensure proper index on discordId
            await usersCollection.createIndex({ discordId: 1 }, { unique: true });
            console.log('Ensured discordId index exists');
            console.log('Database initialization complete');
        }
        catch (error) {
            console.error('Database initialization error:', error);
            throw error;
        }
    }
    /**
     * Get database connection status
     */
    static getConnectionStatus() {
        const states = ['disconnected', 'connected', 'connecting', 'disconnecting'];
        return states[mongoose_1.default.connection.readyState] || 'unknown';
    }
    /**
     * Close database connection
     */
    static async close() {
        try {
            await mongoose_1.default.disconnect();
            console.log('Database connection closed');
        }
        catch (error) {
            console.error('Error closing database connection:', error);
            throw error;
        }
    }
}
exports.LegacyDatabaseInitializer = LegacyDatabaseInitializer;
exports.default = LegacyDatabaseInitializer;
//# sourceMappingURL=DatabaseInitializer.js.map