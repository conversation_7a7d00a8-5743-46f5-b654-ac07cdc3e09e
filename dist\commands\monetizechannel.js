"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const MonetizedChannel_1 = require("../models/MonetizedChannel");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('monetizechannel')
        .setDescription('Enable or disable reaction rewards for the current channel (admin only)')
        .addStringOption(option => option.setName('status')
        .setDescription('Enable or disable reaction rewards')
        .setRequired(true)
        .addChoices({ name: 'Enable', value: 'on' }, { name: 'Disable', value: 'off' }))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError();
        }
        const status = interaction.options.getString('status', true);
        const channelId = interaction.channelId;
        if (!channelId) {
            throw new errorHandler_1.ValidationError('This command must be used in a channel.');
        }
        // Validate status input
        if (status !== 'on' && status !== 'off') {
            throw new errorHandler_1.ValidationError('Status must be either "on" or "off".');
        }
        const enabled = status === 'on';
        try {
            // Update or create monetized channel record
            const monetizedChannel = await MonetizedChannel_1.MonetizedChannel.findOneAndUpdate({ channelId }, {
                channelId,
                enabled
            }, {
                new: true,
                upsert: true,
                runValidators: true
            });
            // Create rich admin embed
            const statusText = enabled ? 'enabled' : 'disabled';
            const statusEmoji = enabled ? embedBuilder_1.EMOJIS.SUCCESS.CHECK : embedBuilder_1.EMOJIS.ADMIN.WARNING;
            const channelMention = `<#${channelId}>`;
            const embed = (0, embedBuilder_1.createAdminEmbed)(`Reaction Rewards ${enabled ? 'Enabled' : 'Disabled'}`)
                .setDescription(`${statusEmoji} **Channel Monetization Updated**\n\n` +
                `Reaction rewards have been **${statusText}** for ${channelMention}`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Channel`,
                value: channelMention,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ADMIN.TOOLS} Status`,
                value: enabled ?
                    `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Active**` :
                    `${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Inactive**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ADMIN.KEY} Administrator`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Reward Details`,
                value: enabled ?
                    `${embedBuilder_1.EMOJIS.SUCCESS.THUMBS_UP} Users earn **5 Phalanx Loyalty Coins** for their first reaction on each message\n` +
                        `${embedBuilder_1.EMOJIS.ACTIONS.LIGHTNING} Anti-exploitation measures active\n` +
                        `${embedBuilder_1.EMOJIS.MISC.CLOCK} 30-second rate limit between rewards` :
                    `${embedBuilder_1.EMOJIS.ADMIN.LOCK} No coins will be awarded for reactions\n` +
                        `${embedBuilder_1.EMOJIS.MISC.MAGNIFYING} Users can still react normally`,
                inline: false
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Updated`,
                value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: false
            })
                .setColor(enabled ? embedBuilder_1.COLORS.SUCCESS : embedBuilder_1.COLORS.WARNING)
                .setFooter({
                text: 'Reaction rewards system - Administrative action logged'
            });
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            // Log the change
            console.log(`[Reaction Rewards] Channel ${channelId} monetization ${statusText} by ${interaction.user.tag}`);
        }
        catch (error) {
            if (error instanceof Error && error.name === 'ValidationError') {
                throw new errorHandler_1.ValidationError('Invalid channel data format');
            }
            else if (error instanceof Error && error.name === 'MongoServerError' && error.code === 11000) {
                throw new errorHandler_1.DatabaseError('Channel monetization record conflict');
            }
            else if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError('Failed to update channel monetization settings', error);
            }
            throw new errorHandler_1.DatabaseError('Unexpected error while updating channel settings');
        }
    })
};
//# sourceMappingURL=monetizechannel.js.map