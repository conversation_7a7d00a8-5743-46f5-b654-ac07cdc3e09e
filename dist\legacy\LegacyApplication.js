"use strict";
/**
 * Legacy Application Manager
 * Orchestrates all legacy components for backward compatibility
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyApplication = void 0;
const DatabaseInitializer_1 = require("./database/DatabaseInitializer");
const ClientManager_1 = require("./client/ClientManager");
const CronManager_1 = require("./cron/CronManager");
const InteractionHandler_1 = require("./events/InteractionHandler");
const MessageHandler_1 = require("./events/MessageHandler");
const ReactionHandler_1 = require("./events/ReactionHandler");
const MemberHandler_1 = require("./events/MemberHandler");
/**
 * Legacy application manager that orchestrates all components
 */
class LegacyApplication {
    constructor() {
        this.isInitialized = false;
        this.clientManager = new ClientManager_1.LegacyClientManager();
        this.client = this.clientManager.getClient();
        // Initialize handlers
        this.cronManager = new CronManager_1.LegacyCronManager(this.client);
        this.interactionHandler = new InteractionHandler_1.LegacyInteractionHandler(this.client);
        this.messageHandler = new MessageHandler_1.LegacyMessageHandler(this.client);
        this.reactionHandler = new ReactionHandler_1.LegacyReactionHandler(this.client);
        this.memberHandler = new MemberHandler_1.LegacyMemberHandler(this.client);
    }
    /**
     * Initialize the legacy application
     */
    async initialize() {
        try {
            console.log('[Legacy App] Initializing Economy Bot (Legacy Mode)...');
            // Initialize database
            await DatabaseInitializer_1.LegacyDatabaseInitializer.initialize();
            // Load commands
            this.clientManager.loadCommands();
            // Setup event handlers
            this.setupEventHandlers();
            // Login to Discord
            await this.clientManager.login();
            this.isInitialized = true;
            console.log('[Legacy App] Economy Bot initialized successfully in legacy mode');
        }
        catch (error) {
            console.error('[Legacy App] Failed to initialize:', error);
            throw error;
        }
    }
    /**
     * Setup Discord event handlers
     */
    setupEventHandlers() {
        // Ready event
        this.client.once('ready', () => {
            console.log(`[Legacy App] Logged in as ${this.client.user?.tag}`);
            // Initialize cron jobs after client is ready
            this.cronManager.initializeJobs();
        });
        // Interaction events
        this.client.on('interactionCreate', async (interaction) => {
            await this.interactionHandler.handleInteraction(interaction);
        });
        // Message events
        this.client.on('messageCreate', async (message) => {
            await this.messageHandler.handleMessageCreate(message);
        });
        // Reaction events
        this.client.on('messageReactionAdd', async (reaction, user) => {
            await this.reactionHandler.handleReactionAdd(reaction, user);
        });
        // Member lifecycle events
        this.client.on('guildMemberAdd', async (member) => {
            await this.memberHandler.handleMemberAdd(member);
        });
        this.client.on('guildMemberRemove', async (member) => {
            await this.memberHandler.handleMemberRemove(member);
        });
        this.client.on('guildMemberUpdate', async (oldMember, newMember) => {
            await this.memberHandler.handleMemberUpdate(oldMember, newMember);
        });
        // Voice state events
        this.client.on('voiceStateUpdate', async (oldState, newState) => {
            await this.memberHandler.handleVoiceStateUpdate(oldState, newState);
        });
        console.log('[Legacy App] Event handlers registered');
    }
    /**
     * Shutdown the legacy application
     */
    async shutdown() {
        try {
            console.log('[Legacy App] Shutting down...');
            // Stop cron jobs
            this.cronManager.destroyAllJobs();
            // Destroy client
            this.clientManager.destroy();
            // Close database connection
            await DatabaseInitializer_1.LegacyDatabaseInitializer.close();
            this.isInitialized = false;
            console.log('[Legacy App] Shutdown complete');
        }
        catch (error) {
            console.error('[Legacy App] Error during shutdown:', error);
        }
    }
    /**
     * Get application status
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            clientReady: this.clientManager.isReady(),
            uptime: this.clientManager.getUptime(),
            guildCount: this.clientManager.getGuildCount(),
            userCount: this.clientManager.getUserCount(),
            databaseStatus: DatabaseInitializer_1.LegacyDatabaseInitializer.getConnectionStatus(),
            cronJobs: this.cronManager.getAllJobStatuses(),
        };
    }
    /**
     * Get the Discord client
     */
    getClient() {
        return this.client;
    }
    /**
     * Get component managers
     */
    getManagers() {
        return {
            client: this.clientManager,
            cron: this.cronManager,
            interaction: this.interactionHandler,
            message: this.messageHandler,
            reaction: this.reactionHandler,
            member: this.memberHandler,
        };
    }
    /**
     * Check if application is ready
     */
    isReady() {
        return this.isInitialized && this.clientManager.isReady();
    }
}
exports.LegacyApplication = LegacyApplication;
exports.default = LegacyApplication;
//# sourceMappingURL=LegacyApplication.js.map