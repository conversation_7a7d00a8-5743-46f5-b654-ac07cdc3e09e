{"version": 3, "file": "voiceStateUpdate.js", "sourceRoot": "", "sources": ["../../src/events/voiceStateUpdate.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,iCAA0C;AAG1C;;GAEG;AACH,MAAa,4BAA6B,SAAQ,uBAAgB;IAGhE,YAAY,GAAwB;QAClC,KAAK,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;QAHjB,SAAI,GAAG,kBAAkB,CAAC;IAI1C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,QAAoB,EAAE,QAAoB;QACtD,IAAI,CAAC;YACH,iBAAiB;YACjB,IAAI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG;gBAAE,OAAO;YAEtC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC;YACxC,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAElC,IAAI,CAAC,MAAM;gBAAE,OAAO;YAEpB,sCAAsC;YACtC,IAAI,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC9C,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACrE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;gBACtB,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;gBAChC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAC1B,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE;gBAClC,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE;aACnC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,QAAoB,EACpB,QAAoB,EACpB,MAAc,EACd,OAAe;QAEf,IAAI,CAAC;YACH,6CAA6C;YAC7C,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC1C,8BAA8B;gBAC9B,IAAI,CAAC,YAAY,CAAC,8BAA8B,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE;oBACvE,MAAM;oBACN,OAAO;oBACP,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;oBAC9B,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI;iBACnC,CAAC,CAAC;gBAEH,6CAA6C;gBAC7C,MAAM,EAAE,yBAAyB,EAAE,GAAG,wDAAa,8BAA8B,GAAC,CAAC;gBAEnF,MAAM,gBAAgB,GAAG,MAAM,yBAAyB,CACtD,IAAI,CAAC,GAAG,CAAC,MAAM,EACf,MAAM,EACN,OAAO,EACP,OAAO,EACP;oBACE,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;oBAC9B,OAAO,EAAE,CAAC,EAAE,kCAAkC;oBAC9C,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CACF,CAAC;gBAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,QAAQ,CAAC,MAAM,EAAE,WAAW,aAAa,gBAAgB,CAAC,MAAM,mCAAmC,CAAC,CAAC;gBACnJ,CAAC;YACH,CAAC;YAED,yEAAyE;iBACpE,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC/C,IAAI,CAAC,YAAY,CAAC,4BAA4B,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE;oBACrE,MAAM;oBACN,OAAO;oBACP,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;oBAC9B,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI;iBACnC,CAAC,CAAC;YACL,CAAC;YAED,yCAAyC;iBACpC,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,KAAK,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;gBAC7F,IAAI,CAAC,YAAY,CAAC,iCAAiC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE;oBACtG,MAAM;oBACN,OAAO;oBACP,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;oBACjC,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;oBACjC,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI;oBACrC,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI;iBACtC,CAAC,CAAC;YACL,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sDAAsD,EAAE;gBACxE,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE;gBAClC,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE;aACnC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AA3GD,oEA2GC"}