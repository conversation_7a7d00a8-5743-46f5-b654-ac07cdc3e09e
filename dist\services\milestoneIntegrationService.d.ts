import { Client } from 'discord.js';
/**
 * Integration service to ensure milestone system works seamlessly with existing systems
 */
export declare class MilestoneIntegrationService {
    /**
     * Processes user activity and checks for both milestones and existing rewards
     */
    static processUserActivity(client: Client, userId: string, guildId: string, activityType: 'message' | 'voice' | 'reaction' | 'login', activityData: any): Promise<{
        milestoneResults: import('./milestoneService').MilestoneCheckResult[];
        existingRewards: any[];
        errors: string[];
    }>;
    /**
     * Ensures milestone system doesn't conflict with role assignment system
     */
    static validateRoleCompatibility(client: Client, userId: string, guildId: string, newBalance: number): Promise<{
        compatible: boolean;
        issues: string[];
    }>;
    /**
     * Validates that milestone transactions are properly recorded
     */
    static validateTransactionIntegrity(userId: string, guildId: string, expectedMilestoneCount: number): Promise<{
        valid: boolean;
        issues: string[];
    }>;
    /**
     * Performs a comprehensive system health check
     */
    static performHealthCheck(guildId: string): Promise<{
        healthy: boolean;
        milestoneSystemStatus: string;
        integrationStatus: string;
        recommendations: string[];
        errors: string[];
    }>;
    /**
     * Migrates existing user data to work with milestone system
     */
    static migrateExistingUsers(guildId: string): Promise<{
        migrated: number;
        errors: string[];
        skipped: number;
    }>;
    /**
     * Ensures milestone system respects existing rate limits and cooldowns
     */
    static validateRateLimitCompatibility(userId: string, guildId: string): Promise<{
        compatible: boolean;
        conflicts: string[];
    }>;
    /**
     * Provides integration status report
     */
    static getIntegrationReport(guildId: string): Promise<{
        status: 'healthy' | 'warning' | 'error';
        milestoneConfigurations: number;
        activeUsers: number;
        recentAchievements: number;
        securityAlerts: number;
        systemCompatibility: boolean;
        lastUpdated: Date;
    }>;
}
//# sourceMappingURL=milestoneIntegrationService.d.ts.map