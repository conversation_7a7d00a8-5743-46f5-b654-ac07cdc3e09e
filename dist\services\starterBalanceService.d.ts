import { <PERSON><PERSON><PERSON>ber, Role } from 'discord.js';
import { IStarterBalance } from '../models/StarterBalance';
/**
 * Processes starter balance when a user receives a role
 */
export declare function processStarterBalance(member: GuildMember, role: Role): Promise<boolean>;
/**
 * Gets all starter balance rules for a guild
 */
export declare function getStarterBalanceRules(guildId: string): Promise<IStarterBalance[]>;
/**
 * Creates a new starter balance rule
 */
export declare function createStarterBalanceRule(guildId: string, roleId: string, roleName: string, amount: number): Promise<IStarterBalance>;
/**
 * Updates an existing starter balance rule
 */
export declare function updateStarterBalanceRule(guildId: string, roleId: string, newAmount: number): Promise<IStarterBalance | null>;
/**
 * Removes a starter balance rule
 */
export declare function removeStarterBalanceRule(guildId: string, roleId: string): Promise<boolean>;
/**
 * Checks if a starter balance rule exists for a specific role
 */
export declare function hasStarterBalanceRule(guildId: string, roleId: string): Promise<boolean>;
//# sourceMappingURL=starterBalanceService.d.ts.map