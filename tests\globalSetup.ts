/**
 * Jest Global Setup
 * Runs once before all tests
 */

export default async function globalSetup() {
  console.log('🧪 Setting up test environment...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests
  
  // Mock Discord.js client methods that might be called during tests
  const mockDiscordMethods = {
    login: jest.fn().mockResolvedValue('mock-token'),
    destroy: jest.fn().mockResolvedValue(undefined),
    on: jest.fn(),
    once: jest.fn(),
    emit: jest.fn()
  };

  // Store original methods if they exist
  (global as any).__ORIGINAL_DISCORD_METHODS__ = mockDiscordMethods;
  
  console.log('✅ Test environment setup complete');
}
