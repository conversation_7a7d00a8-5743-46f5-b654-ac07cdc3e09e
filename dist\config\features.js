"use strict";
/**
 * Feature Configuration System
 * Centralized feature toggle management with runtime configuration
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isGuildSpecificFeature = exports.requiresAdminPermission = exports.isFeatureActive = exports.featureManager = exports.FEATURE_REGISTRY = void 0;
exports.requireFeature = requireFeature;
const environment_1 = require("./environment");
const constants_1 = require("./constants");
/**
 * Feature registry with metadata
 */
exports.FEATURE_REGISTRY = {
    // Core Economy System
    ECONOMY_SYSTEM: {
        enabled: constants_1.FEATURES.ECONOMY_SYSTEM,
        description: 'Core Phalanx Loyalty Coin economy system',
        dependencies: [],
    },
    // Milestone System
    MILESTONE_SYSTEM: {
        enabled: constants_1.FEATURES.MILESTONE_SYSTEM && (environment_1.ENV.ENABLE_MILESTONE_SYSTEM ?? true),
        description: 'Automated milestone rewards for user activities',
        dependencies: ['ECONOMY_SYSTEM'],
    },
    // Dynasty System
    DYNASTY_SYSTEM: {
        enabled: constants_1.FEATURES.DYNASTY_SYSTEM && (environment_1.ENV.ENABLE_DYNASTY_SYSTEM ?? false),
        description: 'Dynasty groups with shared progression and benefits',
        dependencies: ['ECONOMY_SYSTEM', 'MILESTONE_SYSTEM'],
    },
    // Reaction Rewards
    REACTION_REWARDS: {
        enabled: constants_1.FEATURES.REACTION_REWARDS && (environment_1.ENV.ENABLE_REACTION_REWARDS ?? true),
        description: 'Coin rewards for message reactions in monetized channels',
        dependencies: ['ECONOMY_SYSTEM'],
    },
    // Tax System
    TAX_SYSTEM: {
        enabled: constants_1.FEATURES.TAX_SYSTEM && (environment_1.ENV.ENABLE_TAX_SYSTEM ?? true),
        description: 'Automated taxation system for role maintenance',
        dependencies: ['ECONOMY_SYSTEM'],
        adminOnly: true,
    },
    // Starter Balance
    STARTER_BALANCE: {
        enabled: constants_1.FEATURES.STARTER_BALANCE,
        description: 'Automatic starter balance for new role assignments',
        dependencies: ['ECONOMY_SYSTEM'],
        adminOnly: true,
    },
    // Auto Messages
    AUTO_MESSAGES: {
        enabled: constants_1.FEATURES.AUTO_MESSAGES,
        description: 'Automated messages for server events',
        dependencies: [],
        adminOnly: true,
        guildSpecific: true,
    },
    // Role Automation
    ROLE_AUTOMATION: {
        enabled: constants_1.FEATURES.ROLE_AUTOMATION,
        description: 'Automatic role assignment based on PLC balance',
        dependencies: ['ECONOMY_SYSTEM'],
    },
    // User Cleanup
    USER_CLEANUP: {
        enabled: constants_1.FEATURES.USER_CLEANUP,
        description: 'Automatic cleanup of user data when members leave',
        dependencies: [],
        adminOnly: true,
    },
    // Audit Logging
    AUDIT_LOGGING: {
        enabled: constants_1.FEATURES.AUDIT_LOGGING,
        description: 'Comprehensive audit logging for admin actions',
        dependencies: [],
        adminOnly: true,
    },
};
/**
 * Feature dependency resolver
 */
class FeatureDependencyResolver {
    constructor() {
        this.resolved = new Set();
        this.resolving = new Set();
    }
    /**
     * Resolves feature dependencies and returns enabled features
     */
    resolve() {
        const enabledFeatures = new Set();
        for (const [featureName, config] of Object.entries(exports.FEATURE_REGISTRY)) {
            if (this.isFeatureEnabled(featureName)) {
                enabledFeatures.add(featureName);
            }
        }
        return enabledFeatures;
    }
    /**
     * Checks if a feature is enabled, considering dependencies
     */
    isFeatureEnabled(featureName) {
        if (this.resolved.has(featureName)) {
            return true;
        }
        if (this.resolving.has(featureName)) {
            throw new Error(`Circular dependency detected for feature: ${featureName}`);
        }
        const config = exports.FEATURE_REGISTRY[featureName];
        if (!config) {
            return false;
        }
        // Check if feature is explicitly disabled
        if (!config.enabled) {
            return false;
        }
        // Check dependencies
        this.resolving.add(featureName);
        if (config.dependencies) {
            for (const dependency of config.dependencies) {
                if (!this.isFeatureEnabled(dependency)) {
                    this.resolving.delete(featureName);
                    return false;
                }
            }
        }
        this.resolving.delete(featureName);
        this.resolved.add(featureName);
        return true;
    }
}
/**
 * Global feature manager instance
 */
class FeatureManager {
    constructor() {
        const resolver = new FeatureDependencyResolver();
        this.enabledFeatures = resolver.resolve();
        // Log enabled features
        console.log('[Feature Manager] Enabled features:', Array.from(this.enabledFeatures).join(', '));
    }
    /**
     * Check if a feature is enabled
     */
    isEnabled(featureName) {
        return this.enabledFeatures.has(featureName);
    }
    /**
     * Get all enabled features
     */
    getEnabledFeatures() {
        return Array.from(this.enabledFeatures);
    }
    /**
     * Get feature configuration
     */
    getFeatureConfig(featureName) {
        return exports.FEATURE_REGISTRY[featureName];
    }
    /**
     * Check if feature requires admin permissions
     */
    requiresAdmin(featureName) {
        const config = this.getFeatureConfig(featureName);
        return config?.adminOnly ?? false;
    }
    /**
     * Check if feature is guild-specific
     */
    isGuildSpecific(featureName) {
        const config = this.getFeatureConfig(featureName);
        return config?.guildSpecific ?? false;
    }
    /**
     * Get features by category
     */
    getFeaturesByCategory(category) {
        const coreFeatures = ['ECONOMY_SYSTEM', 'REACTION_REWARDS', 'ROLE_AUTOMATION'];
        const adminFeatures = ['TAX_SYSTEM', 'STARTER_BALANCE', 'AUTO_MESSAGES', 'USER_CLEANUP', 'AUDIT_LOGGING'];
        const advancedFeatures = ['MILESTONE_SYSTEM', 'DYNASTY_SYSTEM'];
        const categoryMap = {
            core: coreFeatures,
            admin: adminFeatures,
            advanced: advancedFeatures,
        };
        return categoryMap[category].filter(feature => this.isEnabled(feature));
    }
}
/**
 * Global feature manager instance
 */
exports.featureManager = new FeatureManager();
/**
 * Helper functions for feature checking
 */
const isFeatureActive = (featureName) => {
    return exports.featureManager.isEnabled(featureName);
};
exports.isFeatureActive = isFeatureActive;
const requiresAdminPermission = (featureName) => {
    return exports.featureManager.requiresAdmin(featureName);
};
exports.requiresAdminPermission = requiresAdminPermission;
const isGuildSpecificFeature = (featureName) => {
    return exports.featureManager.isGuildSpecific(featureName);
};
exports.isGuildSpecificFeature = isGuildSpecificFeature;
/**
 * Feature guard decorator for commands and services
 */
function requireFeature(featureName) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = function (...args) {
            if (!(0, exports.isFeatureActive)(featureName)) {
                throw new Error(`Feature ${featureName} is not enabled`);
            }
            return originalMethod.apply(this, args);
        };
        return descriptor;
    };
}
exports.default = exports.featureManager;
//# sourceMappingURL=features.js.map