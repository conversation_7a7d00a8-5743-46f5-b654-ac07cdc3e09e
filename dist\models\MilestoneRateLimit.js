"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const milestoneRateLimitSchema = new mongoose_1.Schema({
    discordId: {
        type: String,
        required: [true, 'Discord ID is required'],
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true
    },
    milestoneType: {
        type: String,
        required: [true, 'Milestone type is required'],
        index: true
    },
    // Rate limiting
    lastAchievement: {
        type: Date,
        default: Date.now,
        index: true
    },
    cooldownUntil: {
        type: Date,
        default: Date.now,
        index: true
    },
    // Daily/Weekly caps
    dailyCount: {
        type: Number,
        default: 0,
        min: [0, 'Daily count cannot be negative']
    },
    weeklyCount: {
        type: Number,
        default: 0,
        min: [0, 'Weekly count cannot be negative']
    },
    lastDailyReset: {
        type: Date,
        default: Date.now
    },
    lastWeeklyReset: {
        type: Date,
        default: Date.now
    },
    // Exploitation detection
    suspiciousActivityCount: {
        type: Number,
        default: 0,
        min: [0, 'Suspicious activity count cannot be negative']
    },
    lastSuspiciousActivity: {
        type: Date
    },
    isBlacklisted: {
        type: Boolean,
        default: false,
        index: true
    },
    blacklistReason: {
        type: String
    },
    blacklistUntil: {
        type: Date,
        index: true
    }
}, {
    timestamps: true
});
// Compound indexes for efficient queries
milestoneRateLimitSchema.index({ discordId: 1, guildId: 1, milestoneType: 1 }, { unique: true });
milestoneRateLimitSchema.index({ cooldownUntil: 1 });
milestoneRateLimitSchema.index({ lastDailyReset: 1 });
milestoneRateLimitSchema.index({ lastWeeklyReset: 1 });
milestoneRateLimitSchema.index({ isBlacklisted: 1, blacklistUntil: 1 });
exports.default = (0, mongoose_1.model)('MilestoneRateLimit', milestoneRateLimitSchema);
//# sourceMappingURL=MilestoneRateLimit.js.map