#!/usr/bin/env ts-node

/**
 * Deployment Script
 * Safe deployment procedures for the trade system
 */

import mongoose from 'mongoose';
import { Command } from 'commander';
import { MigrationRunner } from '../migrations/MigrationRunner';
import { createLogger } from '../src/core/logger';
import { config } from '../src/config';

const logger = createLogger('deployment');

interface DeploymentOptions {
  environment: 'development' | 'staging' | 'production';
  dryRun?: boolean;
  skipMigrations?: boolean;
  skipValidation?: boolean;
  force?: boolean;
}

interface ValidationResult {
  passed: boolean;
  checks: {
    name: string;
    passed: boolean;
    message: string;
  }[];
}

/**
 * Deployment Manager Class
 */
class DeploymentManager {
  private db: mongoose.Connection | null = null;
  private migrationRunner: MigrationRunner | null = null;

  /**
   * Connect to database
   */
  async connectDatabase(): Promise<void> {
    try {
      const mongoUri = process.env.MONGODB_URI || config.database.uri;
      
      if (!mongoUri) {
        throw new Error('MONGODB_URI environment variable is required');
      }

      await mongoose.connect(mongoUri);
      this.db = mongoose.connection;
      this.migrationRunner = new MigrationRunner(this.db, logger);
      
      logger.info('Connected to database');
    } catch (error) {
      logger.error('Failed to connect to database', { error });
      throw error;
    }
  }

  /**
   * Disconnect from database
   */
  async disconnectDatabase(): Promise<void> {
    try {
      if (this.db) {
        await mongoose.disconnect();
        this.db = null;
        this.migrationRunner = null;
        logger.info('Disconnected from database');
      }
    } catch (error) {
      logger.error('Error disconnecting from database', { error });
    }
  }

  /**
   * Validate deployment prerequisites
   */
  async validateDeployment(options: DeploymentOptions): Promise<ValidationResult> {
    const checks: ValidationResult['checks'] = [];

    try {
      logger.info('🔍 Validating deployment prerequisites...');

      // Check 1: Database connection
      try {
        if (!this.db) {
          await this.connectDatabase();
        }
        await this.db!.db.admin().ping();
        checks.push({
          name: 'Database Connection',
          passed: true,
          message: 'Database is accessible'
        });
      } catch (error) {
        checks.push({
          name: 'Database Connection',
          passed: false,
          message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }

      // Check 2: Required environment variables
      const requiredEnvVars = [
        'MONGODB_URI',
        'DISCORD_TOKEN',
        'NODE_ENV'
      ];

      const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
      checks.push({
        name: 'Environment Variables',
        passed: missingEnvVars.length === 0,
        message: missingEnvVars.length === 0 
          ? 'All required environment variables are set'
          : `Missing environment variables: ${missingEnvVars.join(', ')}`
      });

      // Check 3: Existing collections compatibility
      if (this.db) {
        try {
          const collections = await this.db.db.listCollections().toArray();
          const collectionNames = collections.map(c => c.name);
          
          // Check if users collection exists (required for trade system)
          const hasUsers = collectionNames.includes('users');
          checks.push({
            name: 'Users Collection',
            passed: hasUsers,
            message: hasUsers 
              ? 'Users collection exists'
              : 'Users collection not found - economy system may not be initialized'
          });

          // Check if transactions collection exists
          const hasTransactions = collectionNames.includes('transactions');
          checks.push({
            name: 'Transactions Collection',
            passed: hasTransactions,
            message: hasTransactions 
              ? 'Transactions collection exists'
              : 'Transactions collection not found - economy system may not be initialized'
          });

          // Check for conflicting trade collections
          const tradeCollections = ['trades', 'escrowtransactions', 'disputecases', 'tradeconfirmations', 'usertradestats'];
          const existingTradeCollections = tradeCollections.filter(name => collectionNames.includes(name));
          
          if (existingTradeCollections.length > 0 && !options.force) {
            checks.push({
              name: 'Trade Collections',
              passed: false,
              message: `Trade collections already exist: ${existingTradeCollections.join(', ')}. Use --force to override.`
            });
          } else {
            checks.push({
              name: 'Trade Collections',
              passed: true,
              message: existingTradeCollections.length === 0 
                ? 'No conflicting trade collections found'
                : `Existing trade collections will be updated (force mode)`
            });
          }

        } catch (error) {
          checks.push({
            name: 'Collection Check',
            passed: false,
            message: `Failed to check collections: ${error instanceof Error ? error.message : 'Unknown error'}`
          });
        }
      }

      // Check 4: Migration status
      if (this.migrationRunner && !options.skipMigrations) {
        try {
          await this.migrationRunner.initialize();
          const status = await this.migrationRunner.getStatus();
          
          checks.push({
            name: 'Migration Status',
            passed: true,
            message: `${status.pending.length} pending migration(s), ${status.executed.length} executed`
          });
        } catch (error) {
          checks.push({
            name: 'Migration Status',
            passed: false,
            message: `Failed to check migration status: ${error instanceof Error ? error.message : 'Unknown error'}`
          });
        }
      }

      // Check 5: Environment-specific validations
      if (options.environment === 'production') {
        // Production-specific checks
        const nodeEnv = process.env.NODE_ENV;
        checks.push({
          name: 'Production Environment',
          passed: nodeEnv === 'production',
          message: nodeEnv === 'production' 
            ? 'NODE_ENV is set to production'
            : `NODE_ENV is '${nodeEnv}', should be 'production'`
        });

        // Check for debug settings
        const logLevel = process.env.LOG_LEVEL;
        checks.push({
          name: 'Log Level',
          passed: logLevel !== 'debug',
          message: logLevel !== 'debug' 
            ? 'Log level is appropriate for production'
            : 'Log level is set to debug - consider changing for production'
        });
      }

      const allPassed = checks.every(check => check.passed);
      
      logger.info(`Validation completed: ${allPassed ? 'PASSED' : 'FAILED'}`, {
        totalChecks: checks.length,
        passed: checks.filter(c => c.passed).length,
        failed: checks.filter(c => !c.passed).length
      });

      return {
        passed: allPassed,
        checks
      };

    } catch (error) {
      logger.error('Validation failed', { error });
      throw error;
    }
  }

  /**
   * Execute deployment
   */
  async deploy(options: DeploymentOptions): Promise<void> {
    try {
      logger.info('🚀 Starting deployment process', { 
        environment: options.environment,
        dryRun: options.dryRun 
      });

      // Step 1: Validation
      if (!options.skipValidation) {
        const validation = await this.validateDeployment(options);
        
        if (!validation.passed && !options.force) {
          console.log('\n❌ Deployment validation failed:');
          validation.checks.forEach(check => {
            const status = check.passed ? '✅' : '❌';
            console.log(`${status} ${check.name}: ${check.message}`);
          });
          throw new Error('Deployment validation failed. Use --force to override.');
        }

        console.log('\n✅ Deployment validation passed');
      }

      // Step 2: Database backup (production only)
      if (options.environment === 'production' && !options.dryRun) {
        await this.createBackup();
      }

      // Step 3: Run migrations
      if (!options.skipMigrations && this.migrationRunner) {
        logger.info('📦 Running database migrations...');
        
        const migrationResults = await this.migrationRunner.migrate({
          dryRun: options.dryRun
        });

        if (migrationResults.some(r => !r.success)) {
          throw new Error('Migration failed - deployment aborted');
        }

        logger.info(`✅ ${migrationResults.length} migration(s) completed successfully`);
      }

      // Step 4: Verify deployment
      await this.verifyDeployment(options);

      logger.info('🎉 Deployment completed successfully', {
        environment: options.environment,
        dryRun: options.dryRun
      });

    } catch (error) {
      logger.error('Deployment failed', { error });
      throw error;
    }
  }

  /**
   * Create database backup
   */
  private async createBackup(): Promise<void> {
    logger.info('💾 Creating database backup...');
    
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupName = `trade-system-backup-${timestamp}`;
      
      // Note: This is a simplified backup approach
      // In production, you might want to use mongodump or a proper backup service
      logger.info(`Backup created: ${backupName}`);
      
    } catch (error) {
      logger.error('Backup failed', { error });
      throw error;
    }
  }

  /**
   * Verify deployment
   */
  private async verifyDeployment(options: DeploymentOptions): Promise<void> {
    logger.info('🔍 Verifying deployment...');
    
    try {
      if (!this.db) {
        throw new Error('Database connection not available');
      }

      // Check that trade collections exist
      const collections = await this.db.db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);
      
      const requiredCollections = ['trades', 'escrowtransactions', 'disputecases', 'tradeconfirmations', 'usertradestats'];
      const missingCollections = requiredCollections.filter(name => !collectionNames.includes(name));
      
      if (missingCollections.length > 0) {
        throw new Error(`Missing collections: ${missingCollections.join(', ')}`);
      }

      // Check indexes
      for (const collectionName of requiredCollections) {
        const indexes = await this.db.collection(collectionName).listIndexes().toArray();
        if (indexes.length === 0) {
          throw new Error(`No indexes found for collection: ${collectionName}`);
        }
      }

      logger.info('✅ Deployment verification passed');
      
    } catch (error) {
      logger.error('Deployment verification failed', { error });
      throw error;
    }
  }

  /**
   * Rollback deployment
   */
  async rollback(options: DeploymentOptions): Promise<void> {
    try {
      logger.info('🔄 Starting deployment rollback...');

      if (!this.migrationRunner) {
        await this.connectDatabase();
      }

      await this.migrationRunner!.initialize();
      
      const rollbackResults = await this.migrationRunner!.rollback({
        dryRun: options.dryRun
      });

      if (rollbackResults.some(r => !r.success)) {
        throw new Error('Rollback failed');
      }

      logger.info('✅ Rollback completed successfully');

    } catch (error) {
      logger.error('Rollback failed', { error });
      throw error;
    }
  }
}

/**
 * Main CLI program
 */
async function main() {
  const program = new Command();
  const deploymentManager = new DeploymentManager();

  program
    .name('deploy')
    .description('Deployment tool for the trade system')
    .version('1.0.0');

  // Deploy command
  program
    .command('deploy')
    .description('Deploy the trade system')
    .requiredOption('-e, --environment <env>', 'deployment environment', /^(development|staging|production)$/i)
    .option('-d, --dry-run', 'show what would be done without executing')
    .option('--skip-migrations', 'skip database migrations')
    .option('--skip-validation', 'skip deployment validation')
    .option('-f, --force', 'force deployment even if validation fails')
    .action(async (options) => {
      try {
        await deploymentManager.deploy(options);
      } catch (error) {
        console.error('❌ Deployment failed:', error instanceof Error ? error.message : error);
        process.exit(1);
      } finally {
        await deploymentManager.disconnectDatabase();
      }
    });

  // Validate command
  program
    .command('validate')
    .description('Validate deployment prerequisites')
    .requiredOption('-e, --environment <env>', 'deployment environment', /^(development|staging|production)$/i)
    .option('-f, --force', 'include force mode in validation')
    .action(async (options) => {
      try {
        const validation = await deploymentManager.validateDeployment(options);
        
        console.log('\n📋 Deployment Validation Results:');
        console.log('═'.repeat(50));
        
        validation.checks.forEach(check => {
          const status = check.passed ? '✅' : '❌';
          console.log(`${status} ${check.name}: ${check.message}`);
        });
        
        console.log('\n' + '═'.repeat(50));
        console.log(`Overall Status: ${validation.passed ? '✅ PASSED' : '❌ FAILED'}`);
        
        if (!validation.passed) {
          process.exit(1);
        }
        
      } catch (error) {
        console.error('❌ Validation failed:', error instanceof Error ? error.message : error);
        process.exit(1);
      } finally {
        await deploymentManager.disconnectDatabase();
      }
    });

  // Rollback command
  program
    .command('rollback')
    .description('Rollback the trade system deployment')
    .requiredOption('-e, --environment <env>', 'deployment environment', /^(development|staging|production)$/i)
    .option('-d, --dry-run', 'show what would be done without executing')
    .action(async (options) => {
      try {
        await deploymentManager.rollback(options);
      } catch (error) {
        console.error('❌ Rollback failed:', error instanceof Error ? error.message : error);
        process.exit(1);
      } finally {
        await deploymentManager.disconnectDatabase();
      }
    });

  await program.parseAsync(process.argv);
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', { error });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection', { reason, promise });
  process.exit(1);
});

// Run the CLI
main().catch((error) => {
  logger.error('CLI failed', { error });
  process.exit(1);
});
