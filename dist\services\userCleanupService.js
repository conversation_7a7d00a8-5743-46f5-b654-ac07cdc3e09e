"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserCleanupService = void 0;
const User_1 = __importDefault(require("../models/User"));
const Transaction_1 = __importDefault(require("../models/Transaction"));
const ReactionReward_1 = require("../models/ReactionReward");
const errorHandler_1 = require("../utils/errorHandler");
const mongoose_1 = __importDefault(require("mongoose"));
/**
 * Comprehensive cleanup service for removing user data when members leave the server
 */
class UserCleanupService {
    /**
     * Main cleanup function that removes all user data when a member leaves
     */
    static async cleanupUserData(member) {
        const startTime = Date.now();
        const result = {
            success: false,
            userDataRemoved: false,
            transactionsRemoved: 0,
            reactionRewardsRemoved: 0,
            errors: [],
            timeTaken: 0
        };
        const userId = member.user?.id;
        const guildName = member.guild?.name || 'Unknown Guild';
        const userName = member.displayName || member.user?.username || 'Unknown User';
        if (!userId) {
            const errorMsg = 'No user ID available for cleanup';
            result.errors.push(errorMsg);
            console.error(`[User Cleanup] ${errorMsg}`);
            result.timeTaken = Date.now() - startTime;
            return result;
        }
        console.log(`[User Cleanup] Starting cleanup for user ${userName} (${userId}) who left ${guildName}`);
        try {
            // Add grace period to prevent accidental cleanup if user rejoins quickly
            await new Promise(resolve => setTimeout(resolve, this.GRACE_PERIOD_MS));
            // Check if user has rejoined during grace period (only if we have guild access)
            if (member.guild) {
                const stillInGuild = member.guild.members.cache.has(userId);
                if (stillInGuild) {
                    console.log(`[User Cleanup] User ${userName} rejoined during grace period, skipping cleanup`);
                    result.success = true;
                    result.timeTaken = Date.now() - startTime;
                    return result;
                }
            }
            // Start database session for transaction
            if (mongoose_1.default.connection.readyState !== 1) {
                const errorMsg = 'Database is not connected. Please try again in a moment.';
                result.errors.push(errorMsg);
                console.error(`[User Cleanup] ${errorMsg}`);
                result.timeTaken = Date.now() - startTime;
                return result;
            }
            const session = await mongoose_1.default.startSession();
            try {
                await session.withTransaction(async () => {
                    // 1. Remove user balance record
                    const userDeleteResult = await User_1.default.deleteOne({ discordId: userId }).session(session);
                    result.userDataRemoved = userDeleteResult.deletedCount > 0;
                    if (result.userDataRemoved) {
                        console.log(`[User Cleanup] Removed user balance record for ${userName}`);
                    }
                    // 2. Remove transaction history
                    const transactionDeleteResult = await Transaction_1.default.deleteMany({ discordId: userId }).session(session);
                    result.transactionsRemoved = transactionDeleteResult.deletedCount;
                    if (result.transactionsRemoved > 0) {
                        console.log(`[User Cleanup] Removed ${result.transactionsRemoved} transaction records for ${userName}`);
                    }
                    // 3. Remove reaction reward records
                    const reactionRewardDeleteResult = await ReactionReward_1.ReactionReward.deleteMany({ userId }).session(session);
                    result.reactionRewardsRemoved = reactionRewardDeleteResult.deletedCount;
                    if (result.reactionRewardsRemoved > 0) {
                        console.log(`[User Cleanup] Removed ${result.reactionRewardsRemoved} reaction reward records for ${userName}`);
                    }
                    // Log summary
                    const totalRecordsRemoved = (result.userDataRemoved ? 1 : 0) + result.transactionsRemoved + result.reactionRewardsRemoved;
                    console.log(`[User Cleanup] Successfully removed ${totalRecordsRemoved} total records for ${userName} from ${guildName}`);
                });
                result.success = true;
            }
            catch (error) {
                const errorMsg = `Database transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
                result.errors.push(errorMsg);
                console.error(`[User Cleanup] ${errorMsg}`);
                throw error;
            }
            finally {
                await session.endSession();
            }
        }
        catch (error) {
            const errorMsg = `Cleanup failed for user ${userName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            result.errors.push(errorMsg);
            console.error(`[User Cleanup] ${errorMsg}`);
            result.success = false;
        }
        result.timeTaken = Date.now() - startTime;
        // Log final result
        if (result.success) {
            console.log(`[User Cleanup] Completed cleanup for ${userName} in ${result.timeTaken}ms`);
        }
        else {
            console.error(`[User Cleanup] Failed cleanup for ${userName} after ${result.timeTaken}ms:`, result.errors);
        }
        return result;
    }
    /**
     * Check if user has any data that would be cleaned up (for testing/verification)
     */
    static async checkUserData(userId) {
        try {
            const [userRecord, transactionCount, reactionRewardCount] = await Promise.all([
                User_1.default.findOne({ discordId: userId }),
                Transaction_1.default.countDocuments({ discordId: userId }),
                ReactionReward_1.ReactionReward.countDocuments({ userId })
            ]);
            return {
                hasUserRecord: !!userRecord,
                transactionCount,
                reactionRewardCount
            };
        }
        catch (error) {
            console.error(`[User Cleanup] Failed to check user data for ${userId}:`, error);
            throw new errorHandler_1.DatabaseError(`Failed to check user data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Cleanup orphaned data (for maintenance - removes data for users not in any guild)
     * This is a separate maintenance function, not part of the member leave cleanup
     */
    static async cleanupOrphanedData() {
        console.log('[User Cleanup] Starting orphaned data cleanup...');
        try {
            // This would require checking against all guilds the bot is in
            // For now, we'll just log that this function exists for future implementation
            console.log('[User Cleanup] Orphaned data cleanup not yet implemented - requires guild membership verification');
            return {
                orphanedUsers: 0,
                orphanedTransactions: 0,
                orphanedReactionRewards: 0
            };
        }
        catch (error) {
            console.error('[User Cleanup] Orphaned data cleanup failed:', error);
            throw new errorHandler_1.DatabaseError(`Orphaned data cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
exports.UserCleanupService = UserCleanupService;
UserCleanupService.CLEANUP_TIMEOUT_MS = 30000; // 30 seconds timeout
UserCleanupService.GRACE_PERIOD_MS = 5000; // 5 second grace period before cleanup
//# sourceMappingURL=userCleanupService.js.map