/**
 * Feature Management Command
 * Admin command for managing runtime feature toggles
 */
import { SlashCommandBuilder } from 'discord.js';
import { BaseCommand } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
/**
 * Feature management command implementation
 */
export declare class FeatureCommand extends BaseCommand {
    constructor();
    /**
     * Customize the command builder
     */
    protected customizeCommand(command: SlashCommandBuilder): void;
    /**
     * Execute the feature command
     */
    protected executeCommand(context: CommandContext): Promise<void>;
    /**
     * Handle list subcommand
     */
    private handleList;
    /**
     * Handle enable subcommand
     */
    private handleEnable;
    /**
     * Handle disable subcommand
     */
    private handleDisable;
    /**
     * Handle reset subcommand
     */
    private handleReset;
    /**
     * Handle status subcommand
     */
    private handleStatus;
    /**
     * Handle export subcommand
     */
    private handleExport;
    /**
     * Handle stats subcommand
     */
    private handleStats;
    /**
     * Get feature choices for command options
     */
    private getFeatureChoices;
}
//# sourceMappingURL=FeatureCommand.d.ts.map