{"version": 3, "file": "testcleanup.js", "sourceRoot": "", "sources": ["../../src/commands/testcleanup.ts"], "names": [], "mappings": ";;AAAA,2CAAmG;AACnG,wDAA2F;AAC3F,wDAAuG;AACvG,uEAAoE;AAEpE,MAAM,CAAC,OAAO,GAAG;IACb,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC1B,OAAO,CAAC,aAAa,CAAC;SACtB,cAAc,CAAC,mDAAmD,CAAC;SACnE,aAAa,CAAC,MAAM,CAAC,EAAE,CACpB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;SACjB,cAAc,CAAC,gCAAgC,CAAC;SAChD,WAAW,CAAC,IAAI,CAAC,CAAC;SAC1B,eAAe,CAAC,MAAM,CAAC,EAAE,CACtB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;SACnB,cAAc,CAAC,mBAAmB,CAAC;SACnC,WAAW,CAAC,IAAI,CAAC;SACjB,UAAU,CACP,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,EACtC,EAAE,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,UAAU,EAAE,CAClD,CAAC;SACT,2BAA2B,CAAC,gCAAmB,CAAC,aAAa,CAAC;IAEnE,OAAO,EAAE,IAAA,+BAAgB,EAAC,KAAK,EAAE,WAAwC,EAAE,EAAE;QACzE,oBAAoB;QACpB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACrB,MAAM,IAAI,8BAAe,CAAC,4CAA4C,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,gCAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,8BAAe,CAAC,yDAAyD,CAAC,CAAC;QACzF,CAAC;QAED,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAE7D,IAAI,CAAC;YACD,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;gBACrB,sCAAsC;gBACtC,MAAM,QAAQ,GAAG,MAAM,uCAAkB,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;gBAEvE,MAAM,KAAK,GAAG,IAAA,+BAAgB,EAAC,iBAAiB,CAAC;qBAC5C,cAAc,CACX,GAAG,qBAAM,CAAC,KAAK,CAAC,IAAI,qBAAqB,UAAU,CAAC,QAAQ,QAAQ;oBACpE,sEAAsE,CACzE;qBACA,SAAS,CACN;oBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,sBAAsB;oBACnD,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ;oBACpD,MAAM,EAAE,IAAI;iBACf,EACD;oBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,IAAI,sBAAsB;oBAChD,KAAK,EAAE,QAAQ,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,gBAAgB,UAAU,CAAC,CAAC,CAAC,QAAQ;oBAC1F,MAAM,EAAE,IAAI;iBACf,EACD;oBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,mBAAmB;oBAChD,KAAK,EAAE,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,mBAAmB,UAAU,CAAC,CAAC,CAAC,QAAQ;oBAChG,MAAM,EAAE,IAAI;iBACf,CACJ,CAAC;gBAEN,MAAM,YAAY,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,mBAAmB,CAAC;gBAEjH,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;oBACnB,KAAK,CAAC,SAAS,CAAC;wBACZ,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,OAAO,iBAAiB;wBAC9C,KAAK,EAAE,qCAAqC,YAAY,mFAAmF;wBAC3I,MAAM,EAAE,KAAK;qBAChB,CAAC,CAAC;gBACP,CAAC;qBAAM,CAAC;oBACJ,KAAK,CAAC,SAAS,CAAC;wBACZ,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,oBAAoB;wBACjD,KAAK,EAAE,mDAAmD;wBAC1D,MAAM,EAAE,KAAK;qBAChB,CAAC,CAAC;gBACP,CAAC;gBAED,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAElE,CAAC;iBAAM,IAAI,MAAM,KAAK,UAAU,EAAE,CAAC;gBAC/B,wEAAwE;gBACxE,MAAM,QAAQ,GAAG,MAAM,uCAAkB,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;gBACvE,MAAM,YAAY,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,mBAAmB,CAAC;gBAEjH,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;oBACrB,MAAM,KAAK,GAAG,IAAA,+BAAgB,EAAC,oBAAoB,CAAC;yBAC/C,cAAc,CACX,GAAG,qBAAM,CAAC,KAAK,CAAC,IAAI,6BAA6B,UAAU,CAAC,QAAQ,QAAQ;wBAC5E,sFAAsF,CACzF,CAAC;oBAEN,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC9D,OAAO;gBACX,CAAC;gBAED,MAAM,KAAK,GAAG,IAAA,+BAAgB,EAAC,oBAAoB,CAAC;qBAC/C,cAAc,CACX,GAAG,qBAAM,CAAC,KAAK,CAAC,OAAO,6BAA6B,UAAU,CAAC,QAAQ,QAAQ;oBAC/E,4DAA4D,CAC/D;qBACA,SAAS,CACN;oBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,KAAK,kBAAkB;oBAC7C,KAAK,EACD,qEAAqE;wBACrE,oEAAoE;wBACpE,qDAAqD;wBACrD,mDAAmD;oBACvD,MAAM,EAAE,KAAK;iBAChB,EACD;oBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,IAAI,oBAAoB;oBAC9C,KAAK,EACD,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,EAAE,EAAE;wBAC5D,GAAG,QAAQ,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,gBAAgB,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE;wBAChG,GAAG,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,mBAAmB,4BAA4B,CAAC,CAAC,CAAC,EAAE,EAAE;wBAC1G,cAAc,YAAY,YAAY;oBAC1C,MAAM,EAAE,KAAK;iBAChB,EACD;oBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,IAAI,kBAAkB;oBAC5C,KAAK,EACD,iEAAiE;wBACjE,+DAA+D;wBAC/D,mDAAmD;wBACnD,kDAAkD;oBACtD,MAAM,EAAE,KAAK;iBAChB,CACJ,CAAC;gBAEN,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAClE,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,KAAK,GAAG,IAAA,+BAAgB,EAAC,aAAa,EAAE,4DAA4D,CAAC,CAAC;YAC5G,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAClE,CAAC;IACL,CAAC,CAAC;CACL,CAAC"}