/**
 * Environment Configuration
 * Handles environment variables with validation and type safety
 */
/**
 * Environment variable schema with validation
 */
interface EnvironmentConfig {
    BOT_TOKEN: string;
    CLIENT_ID: string;
    MONGODB_URI: string;
    NODE_ENV: 'development' | 'production' | 'test';
    PORT: number;
    ENABLE_DYNASTY_SYSTEM?: boolean;
    ENABLE_MILESTONE_SYSTEM?: boolean;
    ENABLE_REACTION_REWARDS?: boolean;
    ENABLE_TAX_SYSTEM?: boolean;
    LOG_LEVEL?: 'error' | 'warn' | 'info' | 'debug';
    LOG_FILE_PATH?: string;
    DEBUG_MODE?: boolean;
    VERBOSE_LOGGING?: boolean;
}
/**
 * Validated environment configuration
 */
export declare const ENV: EnvironmentConfig;
/**
 * Helper functions for environment checks
 */
export declare const isDevelopment: () => boolean;
export declare const isProduction: () => boolean;
export declare const isTest: () => boolean;
/**
 * Feature flag helpers
 */
export declare const isFeatureEnabled: (feature: keyof typeof ENV) => boolean;
/**
 * Database configuration helper
 */
export declare const getDatabaseConfig: () => {
    uri: string;
    options: {
        maxPoolSize: number;
        serverSelectionTimeoutMS: number;
        socketTimeoutMS: number;
    };
};
/**
 * Discord configuration helper
 */
export declare const getDiscordConfig: () => {
    token: string;
    clientId: string;
    intents: string[];
};
/**
 * Logging configuration helper
 */
import { LoggingConfig } from '../core/interfaces';
export declare const getLoggingConfig: () => LoggingConfig;
/**
 * Export environment for external use
 */
export default ENV;
//# sourceMappingURL=environment.d.ts.map