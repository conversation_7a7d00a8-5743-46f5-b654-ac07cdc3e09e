{"version": 3, "file": "placeholderService.js", "sourceRoot": "", "sources": ["../../src/services/placeholderService.ts"], "names": [], "mappings": ";;;AAyDA,kDA0DC;AAKD,oDAiBC;AAKD,gDAgCC;AAKD,kEA0BC;AAjMD;;GAEG;AACU,QAAA,sBAAsB,GAAG;IAClC,oBAAoB;IACpB,QAAQ,EAAE,0BAA0B;IACpC,aAAa,EAAE,0BAA0B;IACzC,oBAAoB,EAAE,0BAA0B;IAChD,WAAW,EAAE,SAAS;IACtB,YAAY,EAAE,wBAAwB;IACtC,eAAe,EAAE,iBAAiB;IAClC,kBAAkB,EAAE,uBAAuB;IAC3C,mBAAmB,EAAE,qBAAqB;IAE1C,sBAAsB;IACtB,UAAU,EAAE,aAAa;IACzB,eAAe,EAAE,aAAa;IAC9B,aAAa,EAAE,WAAW;IAC1B,sBAAsB,EAAE,oBAAoB;IAC5C,qBAAqB,EAAE,qCAAqC;IAC5D,mBAAmB,EAAE,WAAW;IAChC,eAAe,EAAE,iBAAiB;IAClC,iBAAiB,EAAE,mBAAmB;IAEtC,8CAA8C;IAC9C,QAAQ,EAAE,sBAAsB;IAChC,aAAa,EAAE,2BAA2B;IAC1C,WAAW,EAAE,SAAS;IACtB,cAAc,EAAE,kBAAkB;IAClC,oBAAoB,EAAE,kCAAkC;IAExD,yBAAyB;IACzB,QAAQ,EAAE,2BAA2B;IACrC,QAAQ,EAAE,4BAA4B;IACtC,aAAa,EAAE,gBAAgB;IAC/B,YAAY,EAAE,kBAAkB;IAEhC,uBAAuB;IACvB,WAAW,EAAE,YAAY;IACzB,SAAS,EAAE,iBAAiB;CAC/B,CAAC;AAEF;;GAEG;AACH,SAAgB,mBAAmB,CAAC,IAAY,EAAE,OAA2B;IACzE,IAAI,CAAC,IAAI;QAAE,OAAO,IAAI,CAAC;IAEvB,IAAI,aAAa,GAAG,IAAI,CAAC;IACzB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IAE9C,0CAA0C;IAC1C,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC;IAC7D,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;IACtF,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC;IAElE,oBAAoB;IACpB,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IAClE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,sBAAsB,EAAE,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;IACnG,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9D,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAChE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/F,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC;IAEjG,wBAAwB;IACxB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/F,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,qBAAqB,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;IAEpF,sBAAsB;IACtB,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/D,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,iBAAiB,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IACrE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;IACjE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,wBAAwB,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;IACxF,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,uBAAuB,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;IACtF,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,qBAAqB,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IAClF,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,iBAAiB,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IAC7F,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,mBAAmB,EAAE,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IAElG,kDAAkD;IAClD,IAAI,IAAI,EAAE,CAAC;QACP,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACnE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9D,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IAChG,CAAC;IAED,yBAAyB;IACzB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,kBAAkB,EAAE,CAAC,CAAC;IAC3E,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IACrH,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;IAEnG,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClB,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,CAAC;IAC/F,CAAC;IAED,uBAAuB;IACvB,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAC1D,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IAEvD,OAAO,aAAa,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAAC,IAAY;IAC7C,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,mBAAmB,EAAE,EAAE,EAAE,CAAC;IAE3D,qCAAqC;IACrC,MAAM,kBAAkB,GAAG,UAAU,CAAC;IACtC,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;IAE/D,+BAA+B;IAC/B,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,8BAAsB,CAAC,CAAC;IAC9D,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAC/D,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAC3C,CAAC;IAEF,OAAO;QACH,KAAK,EAAE,mBAAmB,CAAC,MAAM,KAAK,CAAC;QACvC,mBAAmB,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,mBAAmB,CAAC,CAAC,CAAC,oBAAoB;KAC9E,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB;IAC9B,MAAM,UAAU,GAAG;QACf,kBAAkB,EAAE;YAChB,QAAQ,EAAE,aAAa,EAAE,oBAAoB,EAAE,WAAW;YAC1D,YAAY,EAAE,eAAe,EAAE,kBAAkB,EAAE,mBAAmB;SACzE;QACD,oBAAoB,EAAE;YAClB,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,sBAAsB;YAClE,qBAAqB,EAAE,mBAAmB,EAAE,eAAe,EAAE,iBAAiB;SACjF;QACD,kBAAkB,EAAE;YAChB,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,oBAAoB;SAC7E;QACD,aAAa,EAAE;YACX,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY;SAClD;QACD,SAAS,EAAE;YACP,WAAW,EAAE,SAAS;SACzB;KACJ,CAAC;IAEF,IAAI,QAAQ,GAAG,iCAAiC,CAAC;IAEjD,KAAK,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QAChE,QAAQ,IAAI,KAAK,QAAQ,OAAO,CAAC;QACjC,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACrC,QAAQ,IAAI,OAAO,WAAW,QAAQ,8BAAsB,CAAC,WAAkD,CAAC,IAAI,CAAC;QACzH,CAAC;QACD,QAAQ,IAAI,IAAI,CAAC;IACrB,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,SAAgB,2BAA2B,CAAC,QAAa,EAAE,OAA2B;IAClF,MAAM,SAAS,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;IAElC,sBAAsB;IACtB,IAAI,SAAS,CAAC,KAAK;QAAE,SAAS,CAAC,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACrF,IAAI,SAAS,CAAC,WAAW;QAAE,SAAS,CAAC,WAAW,GAAG,mBAAmB,CAAC,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACvG,IAAI,SAAS,CAAC,UAAU;QAAE,SAAS,CAAC,UAAU,GAAG,mBAAmB,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAEpG,iBAAiB;IACjB,IAAI,SAAS,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QACtD,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;YACrD,GAAG,KAAK;YACR,IAAI,EAAE,mBAAmB,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC;YAC9C,KAAK,EAAE,mBAAmB,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC;SACnD,CAAC,CAAC,CAAC;IACR,CAAC;IAED,wBAAwB;IACxB,IAAI,SAAS,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;QACxD,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;YACxD,GAAG,MAAM;YACT,KAAK,EAAE,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC;SACpD,CAAC,CAAC,CAAC;IACR,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC"}