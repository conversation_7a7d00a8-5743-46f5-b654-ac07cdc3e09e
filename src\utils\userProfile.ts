import User from '../models/User';
import type { IUser } from '../models/User';

/**
 * Retrieves an economy profile for a user, creating one if it doesn't exist.
 * @param userId The user's Discord ID.
 * @returns The Mongoose document for the user.
 */
export async function findOrCreateUser(userId: string): Promise<IUser> {
    const userProfile = await User.findOneAndUpdate({ userId }, {}, { new: true, upsert: true, setDefaultsOnInsert: true });
    return userProfile;
}