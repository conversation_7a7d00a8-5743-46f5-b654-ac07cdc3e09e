{"version": 3, "file": "roles.js", "sourceRoot": "", "sources": ["../../src/commands/roles.ts"], "names": [], "mappings": ";;;;;AAAA,2CAA8E;AAC9E,yCAA6C;AAC7C,0DAAkC;AAClC,wDAAwE;AACxE,wDAA+H;AAC/H,6EAA4E;AAE5E,MAAM,CAAC,OAAO,GAAG;IACf,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC5B,OAAO,CAAC,OAAO,CAAC;SAChB,cAAc,CAAC,mDAAmD,CAAC;IACtE,OAAO,EAAE,IAAA,+BAAgB,EAAC,KAAK,EAAE,WAAwC,EAAE,EAAE;QAC3E,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,kBAAW,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,0BAA0B;YACrF,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;YAEtC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAClB,MAAM,KAAK,GAAG,IAAA,iCAAkB,EAAC,mBAAmB,CAAC;qBAClD,cAAc,CAAC,GAAG,qBAAM,CAAC,KAAK,CAAC,IAAI,qIAAqI,CAAC;qBACzK,QAAQ,CAAC,qBAAM,CAAC,IAAI,CAAC,CAAC;gBAEzB,MAAM,WAAW,CAAC,KAAK,CAAC;oBACtB,MAAM,EAAE,CAAC,KAAK,CAAC;oBACf,SAAS,EAAE,IAAI;iBAChB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,6BAA6B;YAC7B,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;YAC/C,MAAM,cAAc,GAAG,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC;YAE1C,uCAAuC;YACvC,IAAI,gBAAgB,GAAmF,EAAE,CAAC;YAC1G,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBAChE,gBAAgB,GAAG,MAAM,IAAA,+CAAuB,EAAC,MAAM,CAAC,CAAC;YAC3D,CAAC;YAED,0CAA0C;YAC1C,MAAM,KAAK,GAAG,IAAA,iCAAkB,EAAC,2BAA2B,CAAC;iBAC1D,cAAc,CAAC,GAAG,qBAAM,CAAC,KAAK,CAAC,MAAM,oMAAoM,CAAC;iBAC1O,YAAY,CAAC,2DAA2D,CAAC,CAAC,CAAC,iCAAiC;YAE/G,6BAA6B;YAC7B,KAAK,CAAC,SAAS,CAAC;gBACd,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,uBAAuB;gBACpD,KAAK,EAAE,IAAA,0BAAW,EAAC,cAAc,CAAC;gBAClC,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,6BAA6B;YAC7B,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,KAAK,CAAC,SAAS,CAAC;oBACd,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,2BAA2B,gBAAgB,CAAC,MAAM,GAAG;oBAClF,KAAK,EAAE,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CACxC,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,MAAM,WAAW,CAAC,QAAQ,QAAQ,IAAA,0BAAW,EAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAC1F,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,KAAK;iBACd,CAAC,CAAC;YACL,CAAC;YAED,8BAA8B;YAC9B,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACzC,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAC1E,CAAC;YAEF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBAC3C,MAAM,SAAS,GAAG,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC;oBAC/C,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,qBAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAM,CAAC,IAAI,CAAC,KAAK,CAAC;oBACxE,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,QAAQ,IAAA,0BAAW,EAAC,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC;oBAEhH,OAAO,GAAG,WAAW,MAAM,IAAI,CAAC,IAAI,QAAQ,IAAA,0BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,KAAK,UAAU,EAAE,CAAC;gBACvF,CAAC,CAAC,CAAC;gBAEH,KAAK,CAAC,SAAS,CAAC;oBACd,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,KAAK,4BAA4B,cAAc,CAAC,MAAM,GAAG;oBAC/E,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC;oBAC9B,MAAM,EAAE,KAAK;iBACd,CAAC,CAAC;YACL,CAAC;YAED,0BAA0B;YAC1B,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;YAChC,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC;YAC9C,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC;YAE1E,KAAK,CAAC,SAAS,CAAC;gBACd,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,MAAM,uBAAuB;gBAClD,KAAK,EAAE,KAAK,aAAa,IAAI,UAAU,6BAA6B,kBAAkB,MAAM;oBACrF,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,oBAAoB,IAAA,0BAAW,EAAC,cAAc,CAAC,UAAU;gBACvF,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,KAAK,CAAC,SAAS,CAAC;gBACd,IAAI,EAAE,uFAAuF;aAC9F,CAAC,CAAC;YAEH,yBAAyB;YACzB,IAAA,0BAAW,EAAC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAErC,8BAA8B;YAC9B,MAAM,aAAa,GAAG,IAAA,uCAAwB,GAAE,CAAC;YAEjD,MAAM,UAAU,GAAG,CAAC,aAAa,CAAC,CAAC;YAEnC,MAAM,WAAW,CAAC,KAAK,CAAC;gBACtB,MAAM,EAAE,CAAC,KAAK,CAAC;gBACf,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,4BAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,4BAAa,CAAC,wBAAwB,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;IACH,CAAC,CAAC;CACH,CAAC"}