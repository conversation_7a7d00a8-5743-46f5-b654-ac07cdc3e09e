/**
 * Leaderboard Manager
 * Handles leaderboard operations and rankings
 */

import { LeaderboardEntry, ILogger } from '../../../core/interfaces';
import { DatabaseError } from '../../../utils/errorHandler';
import { requireFeature } from '../../../config/features';
import User from '../../../models/User';

/**
 * Leaderboard management operations
 */
export class LeaderboardManager {
  private logger: ILogger;

  constructor(logger: ILogger) {
    this.logger = logger;
  }

  /**
   * Get leaderboard
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getLeaderboard(guildId?: string, limit: number = 10): Promise<LeaderboardEntry[]> {
    try {
      this.validateLeaderboardParams(limit);

      const users = await User.find({})
        .sort({ balance: -1 })
        .limit(limit)
        .lean();

      const leaderboard = users.map((user, index) => ({
        discordId: user.discordId,
        balance: user.balance,
        rank: index + 1,
      }));

      this.logOperation('Leaderboard retrieved', { 
        guildId, 
        limit, 
        entriesCount: leaderboard.length 
      });

      return leaderboard;
    } catch (error) {
      this.handleError(error, { guildId, limit });
      throw new DatabaseError(`Failed to get leaderboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user rank
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getUserRank(discordId: string): Promise<number> {
    try {
      if (!discordId || typeof discordId !== 'string') {
        throw new DatabaseError('Invalid Discord ID provided');
      }

      const trimmedDiscordId = discordId.trim();
      
      // Get user's balance
      const user = await User.findOne({ discordId: trimmedDiscordId }).lean();
      if (!user) {
        throw new DatabaseError('User not found');
      }

      // Count users with higher balance
      const higherBalanceCount = await User.countDocuments({
        balance: { $gt: user.balance }
      });

      const rank = higherBalanceCount + 1;

      this.logOperation('User rank calculated', { 
        discordId: trimmedDiscordId, 
        balance: user.balance, 
        rank 
      });

      return rank;
    } catch (error) {
      this.handleError(error, { discordId });
      throw new DatabaseError(`Failed to get user rank: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get top users by balance
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getTopUsers(limit: number = 10): Promise<LeaderboardEntry[]> {
    try {
      this.validateLeaderboardParams(limit);

      const users = await User.find({})
        .sort({ balance: -1 })
        .limit(limit)
        .select('discordId balance')
        .lean();

      const topUsers = users.map((user, index) => ({
        discordId: user.discordId,
        balance: user.balance,
        rank: index + 1,
      }));

      this.logOperation('Top users retrieved', { 
        limit, 
        usersCount: topUsers.length 
      });

      return topUsers;
    } catch (error) {
      this.handleError(error, { limit });
      throw new DatabaseError(`Failed to get top users: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get leaderboard around user
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getLeaderboardAroundUser(discordId: string, range: number = 5): Promise<LeaderboardEntry[]> {
    try {
      if (!discordId || typeof discordId !== 'string') {
        throw new DatabaseError('Invalid Discord ID provided');
      }

      const trimmedDiscordId = discordId.trim();
      const userRank = await this.getUserRank(trimmedDiscordId);
      
      // Calculate range bounds
      const startRank = Math.max(1, userRank - range);
      const endRank = userRank + range;
      const skip = startRank - 1;
      const limit = endRank - startRank + 1;

      const users = await User.find({})
        .sort({ balance: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      const leaderboard = users.map((user, index) => ({
        discordId: user.discordId,
        balance: user.balance,
        rank: startRank + index,
      }));

      this.logOperation('Leaderboard around user retrieved', { 
        discordId: trimmedDiscordId, 
        userRank, 
        range, 
        entriesCount: leaderboard.length 
      });

      return leaderboard;
    } catch (error) {
      this.handleError(error, { discordId, range });
      throw new DatabaseError(`Failed to get leaderboard around user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get leaderboard statistics
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getLeaderboardStats(): Promise<any> {
    try {
      const stats = await User.aggregate([
        {
          $group: {
            _id: null,
            totalUsers: { $sum: 1 },
            totalBalance: { $sum: '$balance' },
            averageBalance: { $avg: '$balance' },
            maxBalance: { $max: '$balance' },
            minBalance: { $min: '$balance' }
          }
        }
      ]);

      const result = stats[0] || {
        totalUsers: 0,
        totalBalance: 0,
        averageBalance: 0,
        maxBalance: 0,
        minBalance: 0
      };

      this.logOperation('Leaderboard stats calculated', result);

      return result;
    } catch (error) {
      this.handleError(error);
      throw new DatabaseError(`Failed to get leaderboard stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate leaderboard parameters
   */
  private validateLeaderboardParams(limit: number): void {
    if (typeof limit !== 'number' || limit < 1 || limit > 100) {
      throw new DatabaseError('Limit must be a number between 1 and 100');
    }
  }

  /**
   * Log operation
   */
  private logOperation(operation: string, details?: any): void {
    this.logger.debug(`[LeaderboardManager] ${operation}`, details);
  }

  /**
   * Handle errors
   */
  private handleError(error: any, context?: any): void {
    this.logger.error('[LeaderboardManager] Error', {
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
      context,
    });
  }
}
