/**
 * Legacy Member Handler
 * Extracted member lifecycle handling from monolithic index.ts
 */
import { Client, GuildMember, PartialGuildMember, VoiceState } from 'discord.js';
/**
 * Member handler for legacy compatibility
 */
export declare class LegacyMemberHandler {
    private client;
    constructor(client: Client);
    /**
     * Handle guild member add events
     */
    handleMemberAdd(member: GuildMember): Promise<void>;
    /**
     * Handle guild member remove events
     */
    handleMemberRemove(member: GuildMember | PartialGuildMember): Promise<void>;
    /**
     * Handle guild member update events
     */
    handleMemberUpdate(oldMember: GuildMember | PartialGuildMember, newMember: GuildMember): Promise<void>;
    /**
     * Handle voice state update events
     */
    handleVoiceStateUpdate(oldState: VoiceState, newState: VoiceState): Promise<void>;
    /**
     * Process join messages
     */
    private processJoinMessages;
    /**
     * Track join activity for milestones
     */
    private trackJoinActivity;
    /**
     * Process user data cleanup
     */
    private processUserCleanup;
    /**
     * Process added roles
     */
    private processAddedRoles;
    /**
     * Process removed roles
     */
    private processRemovedRoles;
    /**
     * Track voice activity for milestones
     */
    private trackVoiceActivity;
    /**
     * Ensure member is fully fetched
     */
    private ensureFullMember;
    /**
     * Get member handler statistics
     */
    getStats(): any;
}
export default LegacyMemberHandler;
//# sourceMappingURL=MemberHandler.d.ts.map