/**
 * Update Names Command
 * Ensures all members have correct prefixes based on their current roles
 */

import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits, GuildMember, Role } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { RolePrefix, IRolePrefix } from '../../models/User';
import { ValidationError, PermissionError, DatabaseError } from '../../utils/errorHandler';
import { createSuccessEmbed, createErrorEmbed } from '../../utils/embedBuilder';

interface MemberPrefixInfo {
  member: GuildMember;
  expectedPrefix: string;
  currentNickname: string;
  baseUsername: string;
  needsUpdate: boolean;
  highestRole: Role;
}

/**
 * UpdateNames command implementation
 */
export class UpdateNamesCommand extends BaseCommand {
  constructor() {
    super({
      name: 'updatenames',
      description: 'Ensure all members have correct prefixes based on their current roles',
      category: CommandCategory.ROLE,
      adminOnly: false,
      guildOnly: true,
      cooldown: 30,
      requiredPermissions: ['ManageNicknames'],
    });
  }

  /**
   * Customize the command builder
   */
  protected customizeCommand(command: SlashCommandBuilder): void {
    // No additional options needed for this command
  }

  /**
   * Execute the update names command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction, guild, member } = context;

    // Defer reply immediately to prevent timeout
    await interaction.deferReply();

    if (!guild || !member) {
      throw new ValidationError('This command can only be used in a server.');
    }

    // Check if bot has Manage Nicknames permission
    const botMember = await guild.members.fetch(interaction.client.user.id);
    if (!botMember.permissions.has(PermissionFlagsBits.ManageNicknames)) {
      throw new PermissionError('I need the "Manage Nicknames" permission to use this command.');
    }

    try {
      // Get all role-prefix mappings for this guild
      const rolePrefixes = await RolePrefix.find({ guildId: guild.id });
      
      if (rolePrefixes.length === 0) {
        const embed = createErrorEmbed(
          'No Role Prefixes Found', 
          'No role prefixes have been configured for this server. Use `/enhancerole` to set up role prefixes first.'
        );
        await interaction.editReply({ embeds: [embed] });
        return;
      }

      // Fetch all guild members
      await guild.members.fetch();
      const allMembers = guild.members.cache;

      let checkedCount = 0;
      let updatedCount = 0;
      const errors: string[] = [];

      // Process each member
      for (const [, guildMember] of allMembers) {
        // Skip bots
        if (guildMember.user.bot) continue;

        try {
          const prefixInfo = await this.analyzeMemberPrefix(guildMember, rolePrefixes, guild);
          checkedCount++;

          if (prefixInfo && prefixInfo.needsUpdate) {
            const result = await this.updateMemberNickname(prefixInfo, botMember);
            if (result.updated) {
              updatedCount++;
            }
          }
        } catch (error) {
          errors.push(`${guildMember.displayName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Create response embed
      const embed = createSuccessEmbed(
        '✅ Name Update Complete',
        `Checked **${checkedCount}** members, updated **${updatedCount}** nicknames`
      );

      embed.addFields({
        name: '📊 Statistics',
        value: [
          `👥 Members checked: ${checkedCount}`,
          `✏️ Nicknames updated: ${updatedCount}`,
          `🏷️ Role prefixes configured: ${rolePrefixes.length}`
        ].join('\n'),
        inline: false
      });

      if (errors.length > 0 && errors.length <= 5) {
        embed.addFields({
          name: '❌ Errors',
          value: errors.slice(0, 5).join('\n'),
          inline: false
        });
      } else if (errors.length > 5) {
        embed.addFields({
          name: '❌ Errors',
          value: `${errors.length} errors occurred. Check logs for details.`,
          inline: false
        });
      }

      await interaction.editReply({ embeds: [embed] });

    } catch (error) {
      this.logger.error('Failed to update names', { error, guildId: guild.id });
      throw new DatabaseError(`Failed to update names: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Analyze a member's current prefix and determine if update is needed
   */
  private async analyzeMemberPrefix(
    member: GuildMember, 
    rolePrefixes: IRolePrefix[], 
    guild: any
  ): Promise<MemberPrefixInfo | null> {
    // Get member's roles that have prefixes
    const memberRolesWithPrefixes = rolePrefixes.filter(rp => 
      member.roles.cache.has(rp.roleId)
    );

    if (memberRolesWithPrefixes.length === 0) {
      // Member has no roles with prefixes, check if they have a prefix that should be removed
      const currentNickname = member.nickname || member.user.username;
      const hasAnyPrefix = rolePrefixes.some(rp => currentNickname.startsWith(rp.prefix));
      
      if (hasAnyPrefix && member.nickname) {
        // Remove prefix by resetting to username
        return {
          member,
          expectedPrefix: '',
          currentNickname,
          baseUsername: member.user.username,
          needsUpdate: true,
          highestRole: member.roles.highest
        };
      }
      return null;
    }

    // Find the highest priority role (by Discord role hierarchy position)
    let highestRole: Role | null = null;
    let highestRolePrefix = '';

    for (const rolePrefix of memberRolesWithPrefixes) {
      const role = await guild.roles.fetch(rolePrefix.roleId);
      if (role && (!highestRole || role.position > highestRole.position)) {
        highestRole = role;
        highestRolePrefix = rolePrefix.prefix;
      }
    }

    if (!highestRole) {
      return null;
    }

    // Determine base username (remove ALL existing prefixes)
    const currentNickname = member.nickname || member.user.username;
    let baseUsername = currentNickname;

    // Remove ALL known prefixes iteratively until no more prefixes are found
    let prefixRemoved = true;
    while (prefixRemoved) {
      prefixRemoved = false;
      for (const rp of rolePrefixes) {
        if (baseUsername.startsWith(rp.prefix)) {
          baseUsername = baseUsername.substring(rp.prefix.length);
          prefixRemoved = true;
          break; // Start over from the beginning to catch nested prefixes
        }
      }
    }

    // If no prefixes were removed and we have a nickname, it means the nickname doesn't contain known prefixes
    // In this case, we should still use the current nickname as the base (could be a custom nickname)
    // But if the baseUsername is empty after prefix removal, fall back to the actual username
    if (baseUsername.trim() === '') {
      baseUsername = member.user.username;
    }

    const expectedNickname = `${highestRolePrefix}${baseUsername}`;
    const needsUpdate = currentNickname !== expectedNickname;

    return {
      member,
      expectedPrefix: highestRolePrefix,
      currentNickname,
      baseUsername,
      needsUpdate,
      highestRole
    };
  }

  /**
   * Update a member's nickname based on prefix info
   */
  private async updateMemberNickname(
    prefixInfo: MemberPrefixInfo, 
    botMember: GuildMember
  ): Promise<{ updated: boolean; reason?: string }> {
    const { member, expectedPrefix, baseUsername } = prefixInfo;

    // Check if bot can manage this member
    if (member.roles.highest.position >= botMember.roles.highest.position && member.id !== member.guild.ownerId) {
      return { updated: false, reason: 'Higher role hierarchy' };
    }

    // Create new nickname
    let newNickname: string | null = null;
    
    if (expectedPrefix === '') {
      // Remove prefix, reset to username
      newNickname = null; // This will reset to username
    } else {
      newNickname = `${expectedPrefix}${baseUsername}`;
      
      // Truncate to 32 characters if necessary (Discord limit)
      if (newNickname.length > 32) {
        const maxNameLength = 32 - expectedPrefix.length;
        const truncatedName = baseUsername.substring(0, maxNameLength);
        newNickname = `${expectedPrefix}${truncatedName}`;
      }
    }

    try {
      await member.setNickname(newNickname, 'Name update: role prefix sync');
      return { updated: true };
    } catch (error) {
      throw new Error(`Failed to update nickname: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
