/**
 * Jest Global Teardown
 * Runs once after all tests
 */

export default async function globalTeardown() {
  console.log('🧹 Cleaning up test environment...');
  
  // Restore any global mocks if needed
  if ((global as any).__ORIGINAL_DISCORD_METHODS__) {
    delete (global as any).__ORIGINAL_DISCORD_METHODS__;
  }
  
  // Clean up any global resources
  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
  
  console.log('✅ Test environment cleanup complete');
}
