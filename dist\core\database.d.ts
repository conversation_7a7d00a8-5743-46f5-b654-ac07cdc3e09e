/**
 * Database Connection Manager
 * Centralized database connection and management
 */
import { IDatabaseService, ILogger, DatabaseConfig } from './interfaces';
/**
 * Database service implementation
 */
export declare class DatabaseService implements IDatabaseService {
    readonly name = "DatabaseService";
    private config;
    private logger;
    private connectionPromise;
    constructor(logger: ILogger, config?: DatabaseConfig);
    /**
     * Initialize the database service
     */
    initialize(): Promise<void>;
    /**
     * Connect to the database
     */
    connect(): Promise<void>;
    /**
     * Internal connection method
     */
    private _connect;
    /**
     * Disconnect from the database
     */
    disconnect(): Promise<void>;
    /**
     * Check if connected to database
     */
    isConnected(): boolean;
    /**
     * Get connection status string
     */
    getConnectionStatus(): string;
    /**
     * Shutdown the database service
     */
    shutdown(): Promise<void>;
    /**
     * Setup database event handlers
     */
    private setupEventHandlers;
    /**
     * Setup required database indexes
     */
    private setupIndexes;
    /**
     * Perform database cleanup operations
     */
    private performCleanup;
    /**
     * Get database statistics
     */
    getStats(): Promise<any>;
    /**
     * Health check
     */
    healthCheck(): Promise<boolean>;
}
//# sourceMappingURL=database.d.ts.map