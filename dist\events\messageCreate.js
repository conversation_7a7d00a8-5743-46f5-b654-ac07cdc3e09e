"use strict";
/**
 * Message Create Event Handler
 * Handles Discord message creation events for mention reactions and milestone tracking
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageCreateEventHandler = void 0;
const base_1 = require("./base");
/**
 * Message create event handler
 */
class MessageCreateEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'messageCreate');
        this.name = 'messageCreate';
    }
    /**
     * Execute message create event
     */
    async execute(message) {
        try {
            // Ignore bot messages and DMs
            if (message.author.bot || !message.guild)
                return;
            // Handle bot mentions
            await this.handleBotMention(message);
            // Track message activity for milestones
            if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
                await this.trackMessageActivity(message);
            }
        }
        catch (error) {
            this.handleError(error, {
                messageId: message.id,
                channelId: message.channel.id,
                guildId: message.guild?.id,
                userId: message.author.id,
            });
        }
    }
    /**
     * Handle bot mentions with reaction
     */
    async handleBotMention(message) {
        try {
            // Check if bot is mentioned
            const botMentioned = message.mentions.has(this.app.client.user);
            if (botMentioned) {
                // React with checkmark emoji
                await message.react('✅');
                this.logExecution('Bot mentioned, reacted with checkmark', {
                    messageId: message.id,
                    channelId: message.channel.id,
                    userId: message.author.id,
                });
            }
        }
        catch (error) {
            this.logger.error('[MessageCreate] Failed to react to bot mention', {
                error,
                messageId: message.id,
                channelId: message.channel.id,
            });
        }
    }
    /**
     * Track message activity for milestones
     */
    async trackMessageActivity(message) {
        try {
            // Import here to avoid circular dependencies
            const { checkAndProcessMilestones } = await Promise.resolve().then(() => __importStar(require('../services/milestoneService')));
            const milestoneResults = await checkAndProcessMilestones(this.app.client, message.author.id, message.guild.id, 'message', {
                channelId: message.channel.id,
                messageContent: message.content,
                messageLength: message.content.length,
                timestamp: message.createdAt
            });
            if (milestoneResults.length > 0) {
                this.logger.info(`[MessageCreate] User ${message.author.tag} achieved ${milestoneResults.length} milestone(s) from message activity`);
            }
        }
        catch (error) {
            this.logger.error('[MessageCreate] Error processing message milestones', {
                error,
                messageId: message.id,
                userId: message.author.id,
                guildId: message.guild?.id,
            });
        }
    }
}
exports.MessageCreateEventHandler = MessageCreateEventHandler;
//# sourceMappingURL=messageCreate.js.map