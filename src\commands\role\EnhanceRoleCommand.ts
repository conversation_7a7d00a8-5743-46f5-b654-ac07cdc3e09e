/**
 * Enhance Role Command
 * Assigns a prefix to all users with a specified role
 */

import { Slash<PERSON>ommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits, Role, GuildMember } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { RolePrefix } from '../../models/User';
import { ValidationError, PermissionError, DatabaseError } from '../../utils/errorHandler';
import { createSuccessEmbed, createErrorEmbed } from '../../utils/embedBuilder';

/**
 * EnhanceRole command implementation
 */
export class EnhanceRoleCommand extends BaseCommand {
  constructor() {
    super({
      name: 'enhancerole',
      description: 'Assign a prefix to all users with a specified role',
      category: CommandCategory.ROLE,
      adminOnly: false,
      guildOnly: true,
      cooldown: 10,
      requiredPermissions: ['ManageNicknames'],
    });
  }

  /**
   * Customize the command builder
   */
  protected customizeCommand(command: SlashCommandBuilder): void {
    command
      .addRoleOption(option =>
        option
          .setName('role')
          .setDescription('Discord role to target')
          .setRequired(true)
      )
      .addStringOption(option =>
        option
          .setName('prefix')
          .setDescription('Text/emoji prefix to prepend (max 10 characters)')
          .setRequired(true)
          .setMaxLength(10)
      );
  }

  /**
   * Execute the enhance role command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction, guild, member } = context;

    // Defer reply immediately to prevent timeout
    await interaction.deferReply();

    if (!guild || !member) {
      throw new ValidationError('This command can only be used in a server.');
    }

    // Check if bot has Manage Nicknames permission
    const botMember = await guild.members.fetch(interaction.client.user.id);
    if (!botMember.permissions.has(PermissionFlagsBits.ManageNicknames)) {
      throw new PermissionError('I need the "Manage Nicknames" permission to use this command.');
    }

    const targetRole = interaction.options.getRole('role', true) as Role;
    const prefix = interaction.options.getString('prefix', true).trim();

    // Validate prefix
    if (prefix.length === 0) {
      throw new ValidationError('Prefix cannot be empty.');
    }

    if (prefix.length > 10) {
      throw new ValidationError('Prefix cannot exceed 10 characters.');
    }

    // Check if role exists in the guild
    const guildRole = await guild.roles.fetch(targetRole.id);
    if (!guildRole) {
      throw new ValidationError('The specified role does not exist in this server.');
    }

    // Check if bot can manage this role (role hierarchy)
    if (guildRole.position >= botMember.roles.highest.position) {
      throw new PermissionError('I cannot manage this role due to role hierarchy restrictions.');
    }

    try {
      // Get all members with the specified role
      const membersWithRole = guildRole.members;
      
      if (membersWithRole.size === 0) {
        const embed = createErrorEmbed('No Members Found', `No members have the role ${guildRole.name}.`);
        await interaction.editReply({ embeds: [embed] });
        return;
      }

      let updatedCount = 0;
      let skippedCount = 0;
      const errors: string[] = [];

      // Get all existing role prefixes for proper cleanup
      const existingRolePrefixes = await RolePrefix.find({ guildId: guild.id });

      // Process each member
      for (const [, guildMember] of membersWithRole) {
        try {
          const result = await this.updateMemberNickname(guildMember, prefix, botMember, existingRolePrefixes);
          if (result.updated) {
            updatedCount++;
          } else {
            skippedCount++;
          }
        } catch (error) {
          errors.push(`${guildMember.displayName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          skippedCount++;
        }
      }

      // Store role-prefix mapping in database
      try {
        await RolePrefix.findOneAndUpdate(
          { guildId: guild.id, roleId: targetRole.id },
          { 
            guildId: guild.id,
            roleId: targetRole.id,
            prefix: prefix,
            createdAt: new Date()
          },
          { upsert: true, new: true }
        );
      } catch (error) {
        this.logger.error('Failed to store role-prefix mapping', { error, guildId: guild.id, roleId: targetRole.id });
        // Don't fail the command if database storage fails, just log it
      }

      // Create response embed
      const embed = createSuccessEmbed(
        '✅ Role Enhancement Complete',
        `Updated **${updatedCount}** out of **${membersWithRole.size}** members with role **${guildRole.name}**`
      );

      if (skippedCount > 0) {
        embed.addFields({
          name: '⚠️ Skipped Members',
          value: `${skippedCount} members were skipped (already had prefix or permission issues)`,
          inline: false
        });
      }

      if (errors.length > 0 && errors.length <= 5) {
        embed.addFields({
          name: '❌ Errors',
          value: errors.slice(0, 5).join('\n'),
          inline: false
        });
      } else if (errors.length > 5) {
        embed.addFields({
          name: '❌ Errors',
          value: `${errors.length} errors occurred. Check logs for details.`,
          inline: false
        });
      }

      embed.addFields({
        name: '🏷️ Prefix Applied',
        value: `\`${prefix}\``,
        inline: true
      });

      await interaction.editReply({ embeds: [embed] });

    } catch (error) {
      this.logger.error('Failed to enhance role', { error, guildId: guild.id, roleId: targetRole.id });
      throw new DatabaseError(`Failed to enhance role: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update a member's nickname with the prefix
   */
  private async updateMemberNickname(
    member: GuildMember,
    prefix: string,
    botMember: GuildMember,
    existingRolePrefixes: any[]
  ): Promise<{ updated: boolean; reason?: string }> {
    // Check if bot can manage this member
    if (member.roles.highest.position >= botMember.roles.highest.position && member.id !== member.guild.ownerId) {
      return { updated: false, reason: 'Higher role hierarchy' };
    }

    // Get current display name
    const currentName = member.nickname || member.user.username;

    // Remove ALL existing prefixes to get the clean base name
    let baseName = currentName;
    let prefixRemoved = true;
    while (prefixRemoved) {
      prefixRemoved = false;
      for (const rp of existingRolePrefixes) {
        if (baseName.startsWith(rp.prefix)) {
          baseName = baseName.substring(rp.prefix.length);
          prefixRemoved = true;
          break; // Start over from the beginning to catch nested prefixes
        }
      }
    }

    // If baseName is empty after prefix removal, fall back to username
    if (baseName.trim() === '') {
      baseName = member.user.username;
    }

    // Check if the final result would be the same (prefix already correctly applied)
    const expectedNickname = `${prefix}${baseName}`;
    if (currentName === expectedNickname) {
      return { updated: false, reason: 'Prefix already correctly applied' };
    }

    // Create new nickname
    let newNickname = expectedNickname;

    // Truncate to 32 characters if necessary (Discord limit)
    if (newNickname.length > 32) {
      const maxNameLength = 32 - prefix.length;
      const truncatedName = baseName.substring(0, maxNameLength);
      newNickname = `${prefix}${truncatedName}`;
    }

    try {
      await member.setNickname(newNickname, `Role enhancement: ${prefix}`);
      return { updated: true };
    } catch (error) {
      throw new Error(`Failed to update nickname: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
