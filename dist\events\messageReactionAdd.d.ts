/**
 * Message Reaction Add Event Handler
 * Handles Discord message reaction events for rewards and milestone tracking
 */
import { MessageReaction, User, PartialMessageReaction, PartialUser } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';
/**
 * Message reaction add event handler
 */
export declare class MessageReactionAddEventHandler extends BaseEventHandler {
    readonly name = "messageReactionAdd";
    constructor(app: IApplicationContext);
    /**
     * Execute message reaction add event
     */
    execute(reaction: MessageReaction | PartialMessageReaction, user: User | PartialUser): Promise<void>;
    /**
     * Ensure reaction is fully fetched
     */
    private ensureFullReaction;
    /**
     * Ensure user is fully fetched
     */
    private ensureFullUser;
    /**
     * Process reaction reward
     */
    private processReactionReward;
    /**
     * Track reaction activity for milestones
     */
    private trackReactionActivity;
}
//# sourceMappingURL=messageReactionAdd.d.ts.map