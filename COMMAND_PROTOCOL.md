# Discord Slash Command Protocol

Follow this checklist for every new slash command to ensure reliability and prevent common issues:

## Recent Updates (Database & UX Improvements)
- **Database Schema Validation**: Enhanced User model with strict Discord ID validation
- **Automatic Database Cleanup**: <PERSON><PERSON> now performs database maintenance on startup
- **No-Ping Policy**: All user references use display names instead of Discord mentions
- **Enhanced Input Validation**: Comprehensive validation for all user inputs and Discord IDs
- **Improved Error Handling**: Better error categorization and user-friendly messages
- **Role Management UX**: Role commands now accept role names instead of requiring role IDs
- **Smart Role Resolution**: Fuzzy matching and disambiguation for role names
- **Role Permission Validation**: Automatic validation of bot permissions for role management

1. **Command Definition**
   - Use `SlashCommandBuilder` to define the command name, description, and all options (with types and required flags).
   - Export the command using `module.exports` with both `data` and `execute` properties.
   - Use `withErrorHandler` wrapper from errorHandler.ts for all command executions.

2. **Error Handling and Logging**
   - All commands must use the centralized error handling system from utils/errorHandler.ts.
   - Use appropriate error types:
     - `ValidationError`: For input validation failures
     - `DatabaseError`: For database operation failures (with detailed context)
     - `PermissionError`: For permission check failures
     - `BalanceError`: For insufficient funds or balance-related issues
     - `CommandError`: For general command execution errors
   - Always wrap command logic in try/catch blocks.
   - Use proper error inheritance for specific error scenarios.
   - Implement comprehensive error logging:
     - Log the start and completion of each operation
     - Include relevant context (user IDs, amounts, operation types)
     - Use consistent log formatting for easier debugging
     - Log all database operations with before/after states
   - Handle specific MongoDB error cases:
     - Validation errors (error.name === 'ValidationError')
     - Duplicate key errors (error.code === 11000)
     - General MongoDB errors (error.name === 'MongoServerError')
   - Include debug information in development mode
   - Provide user-friendly error messages in production

3. **Input Validation**
   - Validate all user inputs before database operations.
   - Use `ValidationError` with clear error messages.
   - **Discord ID Validation**: Always validate Discord IDs are non-null, non-empty, and properly trimmed
   - Check for:
     - Positive numbers for amounts
     - Valid users (not self, not bot)
     - Required permissions
     - Valid input ranges and formats
     - Non-null and non-empty string inputs
     - Proper data types for all parameters

4. **Database Operations and Safety**
   - **Automatic Database Initialization**: Bot performs cleanup and index management on startup
   - **Schema Consistency**: Ensure all operations use `discordId` field consistently
   - Use MongoDB sessions for atomic transactions:
     - Start a session for each complex operation
     - Use `withTransaction` for multi-document operations
     - Properly handle session cleanup in finally blocks
   - Implement operation logging:
     - Log the start of database operations
     - Include operation parameters in logs
     - Log successful completion and results
     - Use structured logging format for consistency
   - Use helper functions for common operations:
     - `ensureUser` for user creation/verification with input validation
     - `adjustBalance` for all balance modifications with comprehensive validation
     - Transaction recording with proper logging
   - Handle race conditions and conflicts:
     - Use atomic updates with `$inc` and `$setOnInsert`
     - Implement proper retry logic for conflicts
     - Handle duplicate key errors gracefully
     - Clean up corrupted records automatically
   - Implement proper cleanup:
     - Always close database sessions
     - Handle rollbacks in case of errors
     - Remove old/invalid indexes during initialization
   - **Enhanced Data Validation**:
     - Validate data before database operations
     - Use MongoDB's built-in validation features
     - Trim and validate Discord IDs before storage
     - Prevent null/empty values from being inserted

5. **Response and User Experience**
   - Always reply to the interaction, even if the command fails.
   - **No-Ping Policy**: Never use Discord mentions (`<@userId>`) in command responses
     - Use `user.displayName` or `user.username` instead of `<@${user.id}>`
     - For leaderboards, fetch user objects to get display names
     - Handle cases where users can't be fetched (show "Unknown User")
   - Use `ephemeral: true` for:
     - Error messages
     - Private information (like transaction history)
     - Validation failures
     - Permission denials
   - Use `ephemeral: false` for:
     - Public confirmations
     - Successful transactions
     - Leaderboard displays
     - Role achievement unlocks
   - **User-Friendly Messages**:
     - Use clear, descriptive success messages
     - Include relevant emojis for visual appeal
     - Provide helpful context in error messages

6. **Permissions (if needed)**
   - Check user or bot permissions if the command requires them.
   - Use `PermissionError` for permission check failures.
   - Set proper `defaultMemberPermissions` in command definition.

7. **/help Command Integration**
   - All commands must be listed in the `/help` command's action row of buttons.
   - If a command requires no input, pressing its button in `/help` should instantly trigger the command for the user.
   - If a command requires input, pressing its button in `/help` should show usage/help info for that command.

8. **Role Management Best Practices**
   - **Use Role Resolution**: Always use `resolveRole()` utility for role input handling
   - **Accept Names or IDs**: Support both role names and role IDs for backward compatibility
   - **Validate Permissions**: Use `validateRolePermissions()` to ensure bot can manage the role
   - **Provide Clear Feedback**: Show resolution notes when fuzzy matching is used
   - **Handle Ambiguity**: Provide helpful error messages for ambiguous role names
   - **Case-Insensitive Matching**: Support case-insensitive role name matching
   - **Fuzzy Matching**: Use intelligent partial matching for user convenience
   - **Error Suggestions**: Suggest available roles when no match is found

9. **Database Schema Requirements**
   - **User Model**: Must use `discordId` field (not `userId`)
   - **Validation Rules**:
     - `discordId`: Required, unique, non-empty string with custom validator
     - `balance`: Number with minimum value of 0
   - **Index Management**: Bot automatically manages database indexes on startup
   - **Data Integrity**: All database operations must validate inputs before execution

10. **Testing**
    - After adding or updating a command, rebuild and redeploy commands.
    - Test the command in Discord to ensure it appears and responds as expected.
    - Test all error scenarios to ensure proper error handling.
    - Verify error messages are user-friendly and helpful.
    - **Specific Tests**:
      - Test with new users (should create records automatically)
      - Test with existing users (should update correctly)
      - Test edge cases (empty inputs, invalid users, etc.)
      - Verify no Discord mentions are sent in responses
    - **Role Management Tests**:
      - Test role resolution with exact names, partial names, and IDs
      - Test ambiguous role names and verify disambiguation messages
      - Test invalid role names and verify helpful error suggestions
      - Test role permission validation for managed/system roles

Example Command Structure:
```typescript
import { withErrorHandler, ValidationError, DatabaseError } from '../utils/errorHandler';
import { logDatabaseOperation } from '../services/economyService';
import { resolveRole, validateRolePermissions } from '../utils/roleResolver';

module.exports = {
    data: new SlashCommandBuilder()
        // ... command definition ...
    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        try {
            // Input validation with detailed messages
            const targetUser = interaction.options.getUser('user', true);
            const amount = interaction.options.getInteger('amount', true);

            // Enhanced input validation
            if (!targetUser || !targetUser.id || targetUser.id.trim().length === 0) {
                throw new ValidationError('Invalid user provided');
            }
            if (amount <= 0) {
                throw new ValidationError('Amount must be greater than zero');
            }
            if (targetUser.bot) {
                throw new ValidationError('You cannot perform this action on a bot');
            }

            // Role resolution example (for role management commands)
            const roleInput = interaction.options.getString('role', true);
            if (!interaction.guild) {
                throw new ValidationError('This command can only be used in a server');
            }

            const roleResolution = await resolveRole(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;
            validateRolePermissions(interaction.guild, role);

            // Database operations with comprehensive validation and logging
            try {
                logDatabaseOperation('Starting Operation', {
                    type: 'commandName',
                    userId: interaction.user.id,
                    targetUserId: targetUser.id,
                    amount: amount
                });

                // Use helper functions with built-in validation
                await adjustBalance(
                    targetUser.id,
                    amount,
                    'commandType',
                    `Operation by ${interaction.user.tag}`
                );

                logDatabaseOperation('Operation Complete', {
                    success: true,
                    targetUserId: targetUser.id,
                    amount: amount
                });

                // Success response with NO Discord mentions and role resolution feedback
                let successMessage = `Successfully performed operation on **${targetUser.displayName}** for **${amount}** coins! 🪙`;

                // Add role resolution note if fuzzy matching was used
                if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                    successMessage += `\n*Note: Resolved "${roleInput}" to "${role.name}"*`;
                }

                await interaction.reply({
                    content: successMessage,
                    ephemeral: false
                });
            } catch (error: unknown) {
                if (error instanceof Error) {
                    throw new DatabaseError('Operation failed', error);
                }
                throw new DatabaseError('Unexpected error during operation');
            }
        } catch (error) {
            // Error will be handled by withErrorHandler
            throw error;
        }
    })
};
```
