{"version": 3, "file": "MemberHandler.js", "sourceRoot": "", "sources": ["../../../src/legacy/events/MemberHandler.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,gFAA6E;AAC7E,0EAAiG;AACjG,0EAAuE;AACvE,sEAA4E;AAE5E;;GAEG;AACH,MAAa,mBAAmB;IAG9B,YAAY,MAAc;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,MAAmB;QACvC,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAEvC,2EAA2E;YAC3E,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAErC,+EAA+E;QACjF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,MAAwC;QAC/D,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEvD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,IAAI,eAAe,CAAC;YACxD,MAAM,WAAW,GAAG,UAAU,EAAE,WAAW,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,IAAI,cAAc,CAAC;YAEvF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;gBACzE,OAAO;YACT,CAAC;YAED,4BAA4B;YAC5B,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAqB,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QAEvF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CACtB,SAA2C,EAC3C,SAAsB;QAEtB,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAC7D,IAAI,CAAC,aAAa;gBAAE,OAAO;YAE3B,yBAAyB;YACzB,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YACjG,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YAEnG,sBAAsB;YACtB,IAAI,UAAU,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YACtD,CAAC;YAED,wBAAwB;YACxB,IAAI,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAC1D,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qDAAqD,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAAC,QAAoB,EAAE,QAAoB;QACrE,IAAI,CAAC;YACH,iBAAiB;YACjB,IAAI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG;gBAAE,OAAO;YAEtC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC;YACxC,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAElC,IAAI,CAAC,MAAM;gBAAE,OAAO;YAEpB,sCAAsC;YACtC,6CAA6C;YAC7C,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC1C,8BAA8B;gBAC9B,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,MAAmB;QACnD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAA,uCAAkB,EAAC,MAAM,CAAC,CAAC;YACpD,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,sBAAsB,UAAU,CAAC,kBAAkB,uBAAuB,MAAM,CAAC,WAAW,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YACtI,CAAC;YACD,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,KAAK,CAAC,qDAAqD,MAAM,CAAC,WAAW,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;YAC/G,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,MAAmB;QACjD,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAA,4CAAyB,EACtD,IAAI,CAAC,MAAM,EACX,MAAM,CAAC,IAAI,CAAC,EAAE,EACd,MAAM,CAAC,KAAK,CAAC,EAAE,EACf,OAAO,EACP,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAC1B,CAAC;YAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,OAAO,CAAC,GAAG,CAAC,0BAA0B,MAAM,CAAC,WAAW,aAAa,gBAAgB,CAAC,MAAM,uBAAuB,CAAC,CAAC;YACvH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,MAAmB,EACnB,MAAc,EACd,WAAmB,EACnB,SAAiB;QAEjB,IAAI,CAAC;YACH,uDAAuD;YACvD,MAAM,QAAQ,GAAG,MAAM,uCAAkB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAChE,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,gBAAgB,GAAG,CAAC,IAAI,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC;YAE5G,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,CAAC,GAAG,CAAC,oCAAoC,WAAW,KAAK,MAAM,cAAc,SAAS,oBAAoB,CAAC,CAAC;gBACnH,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,uBAAuB,WAAW,SAAS,SAAS,kBAAkB,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,gBAAgB,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,mBAAmB,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAEtT,kBAAkB;YAClB,MAAM,aAAa,GAAG,MAAM,uCAAkB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAEvE,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;gBAC1B,MAAM,YAAY,GAAG,EAAE,CAAC;gBACxB,IAAI,aAAa,CAAC,eAAe;oBAAE,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACrE,IAAI,aAAa,CAAC,mBAAmB,GAAG,CAAC;oBAAE,YAAY,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,mBAAmB,eAAe,CAAC,CAAC;gBAClH,IAAI,aAAa,CAAC,sBAAsB,GAAG,CAAC;oBAAE,YAAY,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,sBAAsB,mBAAmB,CAAC,CAAC;gBAE5H,OAAO,CAAC,GAAG,CAAC,mDAAmD,WAAW,KAAK,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,aAAa,CAAC,SAAS,KAAK,CAAC,CAAC;YAC3I,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,8CAA8C,WAAW,GAAG,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;YACpG,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,MAAmB,EAAE,UAAe;QAClE,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,UAAU,EAAE,CAAC;YACxC,IAAI,CAAC;gBACH,0BAA0B;gBAC1B,MAAM,OAAO,GAAG,MAAM,IAAA,6CAAqB,EAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC1D,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,GAAG,CAAC,gDAAgD,MAAM,CAAC,WAAW,aAAa,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC1G,CAAC;gBAED,4BAA4B;gBAC5B,MAAM,aAAa,GAAG,MAAM,IAAA,6CAAwB,EAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;gBAC/E,IAAI,aAAa,CAAC,IAAI,EAAE,CAAC;oBACvB,OAAO,CAAC,GAAG,CAAC,sBAAsB,aAAa,CAAC,kBAAkB,2BAA2B,MAAM,CAAC,WAAW,aAAa,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC3I,CAAC;gBACD,IAAI,aAAa,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpC,OAAO,CAAC,KAAK,CAAC,yDAAyD,MAAM,CAAC,WAAW,GAAG,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;gBACtH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,kDAAkD,MAAM,CAAC,WAAW,aAAa,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACtH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,MAAmB,EAAE,YAAiB;QACtE,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,YAAY,EAAE,CAAC;YAC1C,IAAI,CAAC;gBACH,+BAA+B;gBAC/B,MAAM,gBAAgB,GAAG,MAAM,IAAA,6CAAwB,EAAC,MAAM,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;gBACrF,IAAI,gBAAgB,CAAC,IAAI,EAAE,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,sBAAsB,gBAAgB,CAAC,kBAAkB,8BAA8B,MAAM,CAAC,WAAW,aAAa,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBACjJ,CAAC;gBACD,IAAI,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACvC,OAAO,CAAC,KAAK,CAAC,4DAA4D,MAAM,CAAC,WAAW,GAAG,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAC5H,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,qDAAqD,MAAM,CAAC,WAAW,aAAa,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACzH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,OAAe,EAAE,QAAoB;QACpF,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAA,4CAAyB,EACtD,IAAI,CAAC,MAAM,EACX,MAAM,EACN,OAAO,EACP,OAAO,EACP;gBACE,SAAS,EAAE,QAAQ,CAAC,OAAQ,CAAC,EAAE;gBAC/B,OAAO,EAAE,CAAC,EAAE,kCAAkC;gBAC9C,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CACF,CAAC;YAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,OAAO,CAAC,GAAG,CAAC,oBAAoB,QAAQ,CAAC,MAAM,EAAE,WAAW,aAAa,gBAAgB,CAAC,MAAM,mCAAmC,CAAC,CAAC;YACvI,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,MAAwC;QACrE,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC;gBACH,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;YAC9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;gBACjE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,MAAqB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO;YACL,WAAW,EAAE,qBAAqB;YAClC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YAClC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;SAC1C,CAAC;IACJ,CAAC;CACF;AArRD,kDAqRC;AAED,kBAAe,mBAAmB,CAAC"}