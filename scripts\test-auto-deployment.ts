/**
 * Test Auto-Deployment Process
 * 
 * This script tests the automated build and deployment functionality
 * to ensure the bot can successfully self-deploy without manual intervention.
 */

import 'dotenv/config';
import fs from 'fs';
import path from 'path';
import { 
  isProductionEnvironment, 
  needsBuild, 
  runBuild, 
  deployCommands, 
  deployRoleCommands,
  validateEnvironment,
  checkDependencies
} from '../src/utils/auto-deployment';

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  duration: number;
}

class AutoDeploymentTester {
  private results: TestResult[] = [];
  
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Auto-Deployment Tests...\n');
    
    // Test environment validation
    await this.runTest('Environment Validation', () => this.testEnvironmentValidation());
    
    // Test dependency checking
    await this.runTest('Dependency Check', () => this.testDependencyCheck());
    
    // Test production environment detection
    await this.runTest('Production Environment Detection', () => this.testProductionDetection());
    
    // Test build need detection
    await this.runTest('Build Need Detection', () => this.testBuildNeedDetection());
    
    // Test build process (if safe to do so)
    if (this.shouldTestBuild()) {
      await this.runTest('Build Process', () => this.testBuildProcess());
    } else {
      console.log('⏭️  Skipping build test (would overwrite existing dist/)');
    }
    
    // Test command deployment (only if environment allows)
    if (this.shouldTestCommandDeployment()) {
      await this.runTest('Command Deployment', () => this.testCommandDeployment());
      await this.runTest('Role Command Deployment', () => this.testRoleCommandDeployment());
    } else {
      console.log('⏭️  Skipping command deployment tests (missing CLIENT_ID or BOT_TOKEN)');
    }
    
    // Print results
    this.printResults();
  }
  
  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Running: ${name}...`);
      await testFn();
      
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: true,
        message: 'Passed',
        duration
      });
      
      console.log(`✅ ${name} - Passed (${duration}ms)\n`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: false,
        message: error.message,
        duration
      });
      
      console.log(`❌ ${name} - Failed: ${error.message} (${duration}ms)\n`);
    }
  }
  
  private async testEnvironmentValidation(): Promise<void> {
    const validation = validateEnvironment();
    
    if (!validation.valid) {
      throw new Error(`Missing environment variables: ${validation.missing.join(', ')}`);
    }
    
    console.log('   ✓ All required environment variables are present');
  }
  
  private async testDependencyCheck(): Promise<void> {
    const depCheck = await checkDependencies();
    
    if (!depCheck.valid) {
      throw new Error(`Dependency issues: ${depCheck.issues.join(', ')}`);
    }
    
    console.log('   ✓ All dependencies are available');
  }
  
  private async testProductionDetection(): Promise<void> {
    const isProduction = isProductionEnvironment();
    console.log(`   ✓ Production environment: ${isProduction}`);
    
    // Test with different environment variables
    const originalNodeEnv = process.env.NODE_ENV;
    const originalDiscloud = process.env.DISCLOUD;
    
    try {
      process.env.NODE_ENV = 'production';
      const testProd1 = isProductionEnvironment();
      if (!testProd1) {
        throw new Error('Failed to detect production environment with NODE_ENV=production');
      }
      
      process.env.NODE_ENV = originalNodeEnv;
      process.env.DISCLOUD = 'true';
      const testProd2 = isProductionEnvironment();
      if (!testProd2) {
        throw new Error('Failed to detect production environment with DISCLOUD=true');
      }
      
      console.log('   ✓ Production detection logic works correctly');
    } finally {
      process.env.NODE_ENV = originalNodeEnv;
      process.env.DISCLOUD = originalDiscloud;
    }
  }
  
  private async testBuildNeedDetection(): Promise<void> {
    const buildNeeded = await needsBuild();
    console.log(`   ✓ Build needed: ${buildNeeded}`);
    
    // Check if the logic makes sense
    const srcExists = fs.existsSync(path.join(process.cwd(), 'src'));
    const distExists = fs.existsSync(path.join(process.cwd(), 'dist'));
    
    if (srcExists && !distExists && !buildNeeded) {
      throw new Error('Build should be needed when src exists but dist does not');
    }
    
    console.log('   ✓ Build need detection logic is working');
  }
  
  private async testBuildProcess(): Promise<void> {
    // Create a backup of dist if it exists
    const distPath = path.join(process.cwd(), 'dist');
    const backupPath = path.join(process.cwd(), 'dist_backup_test');
    
    let hasBackup = false;
    
    try {
      if (fs.existsSync(distPath)) {
        fs.renameSync(distPath, backupPath);
        hasBackup = true;
      }
      
      const buildSuccess = await runBuild();
      if (!buildSuccess) {
        throw new Error('Build process failed');
      }
      
      // Verify build output
      if (!fs.existsSync(path.join(distPath, 'main.js'))) {
        throw new Error('Build did not produce expected main.js file');
      }
      
      console.log('   ✓ Build process completed successfully');
      console.log('   ✓ Build output verified');
    } finally {
      // Restore backup if we created one
      if (hasBackup) {
        if (fs.existsSync(distPath)) {
          fs.rmSync(distPath, { recursive: true, force: true });
        }
        fs.renameSync(backupPath, distPath);
      }
    }
  }
  
  private async testCommandDeployment(): Promise<void> {
    const deploySuccess = await deployCommands();
    if (!deploySuccess) {
      throw new Error('Command deployment failed');
    }
    
    console.log('   ✓ Commands deployed successfully');
  }
  
  private async testRoleCommandDeployment(): Promise<void> {
    const deploySuccess = await deployRoleCommands();
    if (!deploySuccess) {
      throw new Error('Role command deployment failed');
    }
    
    console.log('   ✓ Role commands deployed successfully');
  }
  
  private shouldTestBuild(): boolean {
    // Only test build if we're in a safe environment
    // and the user explicitly wants to test it
    return process.env.TEST_BUILD === 'true';
  }
  
  private shouldTestCommandDeployment(): boolean {
    return !!(process.env.CLIENT_ID && process.env.BOT_TOKEN && process.env.TEST_DEPLOY === 'true');
  }
  
  private printResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    
    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.name} (${result.duration}ms)`);
      if (!result.passed) {
        console.log(`   Error: ${result.message}`);
      }
    });
    
    console.log(`\n📈 Overall: ${passed}/${total} tests passed`);
    
    if (passed === total) {
      console.log('🎉 All tests passed! Auto-deployment should work correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please review the issues above.');
      process.exit(1);
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new AutoDeploymentTester();
  tester.runAllTests().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

export default AutoDeploymentTester;
