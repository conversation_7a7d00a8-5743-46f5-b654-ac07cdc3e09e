/**
 * User Manager
 * Handles user creation and management operations
 */

import { ILogger } from '../../../core/interfaces';
import { DatabaseError } from '../../../utils/errorHandler';
import { requireFeature } from '../../../config/features';
import User from '../../../models/User';

/**
 * User management operations
 */
export class UserManager {
  private logger: ILogger;

  constructor(logger: ILogger) {
    this.logger = logger;
  }

  /**
   * Ensure user exists in database
   */
  @requireFeature('ECONOMY_SYSTEM')
  async ensureUser(discordId: string): Promise<any> {
    try {
      const trimmedDiscordId = this.validateAndSanitizeDiscordId(discordId);

      const user = await User.findOneAndUpdate(
        { discordId: trimmedDiscordId },
        { $setOnInsert: { discordId: trimmedDiscordId, balance: 0 } },
        { new: true, upsert: true, runValidators: true }
      );

      this.logOperation('User ensured', { discordId: trimmedDiscordId, userId: user._id });
      return user;
    } catch (error) {
      this.handleError(error, { discordId });
      throw new DatabaseError(`Failed to ensure user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user by Discord ID
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getUser(discordId: string): Promise<any | null> {
    try {
      const trimmedDiscordId = this.validateAndSanitizeDiscordId(discordId);
      
      const user = await User.findOne({ discordId: trimmedDiscordId }).lean();
      
      this.logOperation('User retrieved', { discordId: trimmedDiscordId, found: !!user });
      return user;
    } catch (error) {
      this.handleError(error, { discordId });
      throw new DatabaseError(`Failed to get user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create new user
   */
  @requireFeature('ECONOMY_SYSTEM')
  async createUser(discordId: string, initialBalance: number = 0): Promise<any> {
    try {
      const trimmedDiscordId = this.validateAndSanitizeDiscordId(discordId);

      const user = new User({
        discordId: trimmedDiscordId,
        balance: initialBalance
      });

      await user.save();

      this.logOperation('User created', { 
        discordId: trimmedDiscordId, 
        userId: user._id, 
        initialBalance 
      });
      
      return user;
    } catch (error) {
      this.handleError(error, { discordId, initialBalance });
      throw new DatabaseError(`Failed to create user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update user data
   */
  @requireFeature('ECONOMY_SYSTEM')
  async updateUser(discordId: string, updateData: any): Promise<any> {
    try {
      const trimmedDiscordId = this.validateAndSanitizeDiscordId(discordId);

      const user = await User.findOneAndUpdate(
        { discordId: trimmedDiscordId },
        updateData,
        { new: true, runValidators: true }
      );

      if (!user) {
        throw new Error(`User not found: ${trimmedDiscordId}`);
      }

      this.logOperation('User updated', { 
        discordId: trimmedDiscordId, 
        userId: user._id, 
        updateData 
      });
      
      return user;
    } catch (error) {
      this.handleError(error, { discordId, updateData });
      throw new DatabaseError(`Failed to update user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete user
   */
  @requireFeature('ECONOMY_SYSTEM')
  async deleteUser(discordId: string): Promise<boolean> {
    try {
      const trimmedDiscordId = this.validateAndSanitizeDiscordId(discordId);

      const result = await User.deleteOne({ discordId: trimmedDiscordId });

      this.logOperation('User deletion attempted', { 
        discordId: trimmedDiscordId, 
        deleted: result.deletedCount > 0 
      });
      
      return result.deletedCount > 0;
    } catch (error) {
      this.handleError(error, { discordId });
      throw new DatabaseError(`Failed to delete user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate and sanitize Discord ID
   */
  private validateAndSanitizeDiscordId(discordId: string): string {
    if (!discordId || typeof discordId !== 'string') {
      throw new DatabaseError('Discord ID must be a non-empty string');
    }

    const trimmedId = discordId.trim();
    if (!trimmedId) {
      throw new DatabaseError('Discord ID cannot be empty');
    }

    // Basic Discord ID format validation (17-20 digits)
    if (!/^\d{17,20}$/.test(trimmedId)) {
      throw new DatabaseError('Invalid Discord ID format');
    }

    return trimmedId;
  }

  /**
   * Log operation
   */
  private logOperation(operation: string, details?: any): void {
    this.logger.debug(`[UserManager] ${operation}`, details);
  }

  /**
   * Handle errors
   */
  private handleError(error: any, context?: any): void {
    this.logger.error('[UserManager] Error', {
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
      context,
    });
  }
}
