/**
 * Escrow Manager
 * Handles secure escrow operations for trades
 */

import mongoose from 'mongoose';
import { BaseService } from '../../base/BaseService';
import { requireFeature } from '../../../core/decorators';
import { DatabaseError, ValidationError } from '../../../utils/errorHandler';
import { 
  User, 
  Transaction, 
  EscrowTransaction, 
  IEscrowTransaction,
  ITrade,
  TransactionType 
} from '../../../models';

export interface EscrowOperation {
  tradeId: string;
  discordId: string;
  amount: number;
  type: 'LOCK' | 'RELEASE' | 'REFUND' | 'DISPUTE_HOLD';
  reason: string;
}

/**
 * Escrow Manager Class
 */
export class EscrowManager extends BaseService {
  constructor(app: any) {
    super('EscrowManager', app);
  }

  /**
   * Initialize the escrow manager
   */
  async initialize(): Promise<void> {
    this.logger.info('[EscrowManager] Escrow manager initialized');
  }

  /**
   * Lock funds in escrow for a trade
   */
  @requireFeature('TRADE_SYSTEM')
  async lockEscrow(trade: ITrade, session: mongoose.ClientSession): Promise<IEscrowTransaction> {
    this.logOperation('Locking escrow funds', { 
      tradeId: trade.tradeId, 
      buyerId: trade.buyerId, 
      amount: trade.amount 
    });

    try {
      // Verify buyer has sufficient balance
      const buyer = await User.findOne({ discordId: trade.buyerId }).session(session);
      if (!buyer) {
        throw new ValidationError('Buyer not found');
      }

      if (buyer.balance < trade.amount) {
        throw new ValidationError(`Insufficient balance. Required: ${trade.amount}, Available: ${buyer.balance}`);
      }

      // Deduct from buyer's balance
      buyer.balance -= trade.amount;
      await buyer.save({ session });

      // Create main transaction record
      const transactionId = this.generateTransactionId();
      await Transaction.create([{
        discordId: trade.buyerId,
        type: 'trade_escrow' as TransactionType,
        amount: -trade.amount,
        details: `Escrow locked for trade ${trade.tradeId}`,
        tradeId: trade.tradeId,
        timestamp: new Date()
      }], { session });

      // Create escrow transaction record
      const escrowTransaction = await EscrowTransaction.create([{
        escrowId: this.generateEscrowId(),
        tradeId: trade.tradeId,
        discordId: trade.buyerId,
        guildId: trade.guildId,
        amount: trade.amount,
        transactionType: 'LOCK',
        status: 'COMPLETED',
        timestamp: new Date(),
        completedAt: new Date(),
        relatedTransactionId: transactionId,
        details: `Funds locked in escrow for trade ${trade.tradeId}`,
        reason: 'Trade escrow lock'
      }], { session });

      // Update trade escrow status
      trade.escrowLocked = true;
      trade.escrowAmount = trade.amount;
      await trade.save({ session });

      this.logOperation('Escrow funds locked successfully', { 
        tradeId: trade.tradeId,
        escrowId: escrowTransaction[0].escrowId,
        amount: trade.amount
      });

      return escrowTransaction[0];

    } catch (error) {
      this.handleError(error, { operation: 'lock_escrow', tradeId: trade.tradeId });
      throw error;
    }
  }

  /**
   * Release escrow funds to seller
   */
  @requireFeature('TRADE_SYSTEM')
  async releaseEscrow(trade: ITrade, reason: string, session: mongoose.ClientSession): Promise<IEscrowTransaction> {
    this.logOperation('Releasing escrow funds', { 
      tradeId: trade.tradeId, 
      sellerId: trade.sellerId, 
      amount: trade.escrowAmount 
    });

    try {
      if (!trade.escrowLocked || trade.escrowAmount <= 0) {
        throw new ValidationError('No funds in escrow for this trade');
      }

      // Add to seller's balance
      const seller = await User.findOneAndUpdate(
        { discordId: trade.sellerId },
        { $inc: { balance: trade.escrowAmount } },
        { new: true, upsert: true, session }
      );

      if (!seller) {
        throw new DatabaseError('Failed to update seller balance');
      }

      // Create main transaction record
      const transactionId = this.generateTransactionId();
      await Transaction.create([{
        discordId: trade.sellerId,
        type: 'trade_release' as TransactionType,
        amount: trade.escrowAmount,
        details: `Escrow released for trade ${trade.tradeId}`,
        tradeId: trade.tradeId,
        timestamp: new Date()
      }], { session });

      // Create escrow transaction record
      const escrowTransaction = await EscrowTransaction.create([{
        escrowId: this.generateEscrowId(),
        tradeId: trade.tradeId,
        discordId: trade.sellerId,
        guildId: trade.guildId,
        amount: trade.escrowAmount,
        transactionType: 'RELEASE',
        status: 'COMPLETED',
        timestamp: new Date(),
        completedAt: new Date(),
        relatedTransactionId: transactionId,
        details: `Escrow funds released to seller for trade ${trade.tradeId}`,
        reason
      }], { session });

      // Update trade escrow status
      trade.escrowLocked = false;
      await trade.save({ session });

      this.logOperation('Escrow funds released successfully', { 
        tradeId: trade.tradeId,
        escrowId: escrowTransaction[0].escrowId,
        amount: trade.escrowAmount,
        sellerId: trade.sellerId
      });

      return escrowTransaction[0];

    } catch (error) {
      this.handleError(error, { operation: 'release_escrow', tradeId: trade.tradeId });
      throw error;
    }
  }

  /**
   * Refund escrow funds to buyer
   */
  @requireFeature('TRADE_SYSTEM')
  async refundEscrow(trade: ITrade, reason: string, session: mongoose.ClientSession): Promise<IEscrowTransaction> {
    this.logOperation('Refunding escrow funds', { 
      tradeId: trade.tradeId, 
      buyerId: trade.buyerId, 
      amount: trade.escrowAmount 
    });

    try {
      if (!trade.escrowLocked || trade.escrowAmount <= 0) {
        throw new ValidationError('No funds in escrow for this trade');
      }

      // Add back to buyer's balance
      const buyer = await User.findOneAndUpdate(
        { discordId: trade.buyerId },
        { $inc: { balance: trade.escrowAmount } },
        { new: true, upsert: true, session }
      );

      if (!buyer) {
        throw new DatabaseError('Failed to update buyer balance');
      }

      // Create main transaction record
      const transactionId = this.generateTransactionId();
      await Transaction.create([{
        discordId: trade.buyerId,
        type: 'trade_refund' as TransactionType,
        amount: trade.escrowAmount,
        details: `Escrow refunded for trade ${trade.tradeId}`,
        tradeId: trade.tradeId,
        timestamp: new Date()
      }], { session });

      // Create escrow transaction record
      const escrowTransaction = await EscrowTransaction.create([{
        escrowId: this.generateEscrowId(),
        tradeId: trade.tradeId,
        discordId: trade.buyerId,
        guildId: trade.guildId,
        amount: trade.escrowAmount,
        transactionType: 'REFUND',
        status: 'COMPLETED',
        timestamp: new Date(),
        completedAt: new Date(),
        relatedTransactionId: transactionId,
        details: `Escrow funds refunded to buyer for trade ${trade.tradeId}`,
        reason
      }], { session });

      // Update trade escrow status
      trade.escrowLocked = false;
      await trade.save({ session });

      this.logOperation('Escrow funds refunded successfully', { 
        tradeId: trade.tradeId,
        escrowId: escrowTransaction[0].escrowId,
        amount: trade.escrowAmount,
        buyerId: trade.buyerId
      });

      return escrowTransaction[0];

    } catch (error) {
      this.handleError(error, { operation: 'refund_escrow', tradeId: trade.tradeId });
      throw error;
    }
  }

  /**
   * Split escrow funds between parties (for dispute resolution)
   */
  @requireFeature('TRADE_SYSTEM')
  async splitEscrow(
    trade: ITrade, 
    sellerAmount: number, 
    buyerAmount: number, 
    reason: string, 
    session: mongoose.ClientSession
  ): Promise<{ sellerTransaction: IEscrowTransaction; buyerTransaction?: IEscrowTransaction }> {
    this.logOperation('Splitting escrow funds', { 
      tradeId: trade.tradeId, 
      sellerAmount, 
      buyerAmount,
      totalEscrow: trade.escrowAmount
    });

    try {
      if (!trade.escrowLocked || trade.escrowAmount <= 0) {
        throw new ValidationError('No funds in escrow for this trade');
      }

      if (sellerAmount + buyerAmount !== trade.escrowAmount) {
        throw new ValidationError('Split amounts must equal total escrow amount');
      }

      if (sellerAmount < 0 || buyerAmount < 0) {
        throw new ValidationError('Split amounts cannot be negative');
      }

      let sellerTransaction: IEscrowTransaction;
      let buyerTransaction: IEscrowTransaction | undefined;

      // Release to seller if amount > 0
      if (sellerAmount > 0) {
        const seller = await User.findOneAndUpdate(
          { discordId: trade.sellerId },
          { $inc: { balance: sellerAmount } },
          { new: true, upsert: true, session }
        );

        if (!seller) {
          throw new DatabaseError('Failed to update seller balance');
        }

        // Create seller transaction records
        const sellerTransactionId = this.generateTransactionId();
        await Transaction.create([{
          discordId: trade.sellerId,
          type: 'trade_release' as TransactionType,
          amount: sellerAmount,
          details: `Partial escrow release (dispute resolution) for trade ${trade.tradeId}`,
          tradeId: trade.tradeId,
          timestamp: new Date()
        }], { session });

        const sellerEscrowTx = await EscrowTransaction.create([{
          escrowId: this.generateEscrowId(),
          tradeId: trade.tradeId,
          discordId: trade.sellerId,
          guildId: trade.guildId,
          amount: sellerAmount,
          transactionType: 'RELEASE',
          status: 'COMPLETED',
          timestamp: new Date(),
          completedAt: new Date(),
          relatedTransactionId: sellerTransactionId,
          details: `Partial escrow release to seller (dispute resolution) for trade ${trade.tradeId}`,
          reason
        }], { session });

        sellerTransaction = sellerEscrowTx[0];
      }

      // Refund to buyer if amount > 0
      if (buyerAmount > 0) {
        const buyer = await User.findOneAndUpdate(
          { discordId: trade.buyerId },
          { $inc: { balance: buyerAmount } },
          { new: true, upsert: true, session }
        );

        if (!buyer) {
          throw new DatabaseError('Failed to update buyer balance');
        }

        // Create buyer transaction records
        const buyerTransactionId = this.generateTransactionId();
        await Transaction.create([{
          discordId: trade.buyerId,
          type: 'trade_refund' as TransactionType,
          amount: buyerAmount,
          details: `Partial escrow refund (dispute resolution) for trade ${trade.tradeId}`,
          tradeId: trade.tradeId,
          timestamp: new Date()
        }], { session });

        const buyerEscrowTx = await EscrowTransaction.create([{
          escrowId: this.generateEscrowId(),
          tradeId: trade.tradeId,
          discordId: trade.buyerId,
          guildId: trade.guildId,
          amount: buyerAmount,
          transactionType: 'REFUND',
          status: 'COMPLETED',
          timestamp: new Date(),
          completedAt: new Date(),
          relatedTransactionId: buyerTransactionId,
          details: `Partial escrow refund to buyer (dispute resolution) for trade ${trade.tradeId}`,
          reason
        }], { session });

        buyerTransaction = buyerEscrowTx[0];
      }

      // Update trade escrow status
      trade.escrowLocked = false;
      await trade.save({ session });

      this.logOperation('Escrow funds split successfully', { 
        tradeId: trade.tradeId,
        sellerAmount,
        buyerAmount
      });

      return { sellerTransaction: sellerTransaction!, buyerTransaction };

    } catch (error) {
      this.handleError(error, { operation: 'split_escrow', tradeId: trade.tradeId });
      throw error;
    }
  }

  /**
   * Get escrow balance for a trade
   */
  async getEscrowBalance(tradeId: string): Promise<number> {
    try {
      return await EscrowTransaction.getEscrowBalance(tradeId);
    } catch (error) {
      this.handleError(error, { operation: 'get_escrow_balance', tradeId });
      throw error;
    }
  }

  /**
   * Get user's total escrowed amount
   */
  async getUserEscrowedAmount(discordId: string): Promise<number> {
    try {
      return await EscrowTransaction.getUserEscrowedAmount(discordId);
    } catch (error) {
      this.handleError(error, { operation: 'get_user_escrowed_amount', discordId });
      throw error;
    }
  }

  // Private helper methods

  private generateEscrowId(): string {
    return `escrow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
