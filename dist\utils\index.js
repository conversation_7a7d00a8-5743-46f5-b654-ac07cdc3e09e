"use strict";
/**
 * Utils Module Index
 * Centralized exports for all utility modules
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveRole = exports.DatabaseError = exports.ValidationError = exports.withErrorHandler = exports.createEconomyEmbed = exports.createErrorEmbed = exports.createSuccessEmbed = exports.formatCoins = exports.DiscordUtils = exports.FormatUtils = exports.ValidationUtils = void 0;
// Enhanced utility modules
__exportStar(require("./validation/ValidationUtils"), exports);
__exportStar(require("./formatting/FormatUtils"), exports);
__exportStar(require("./discord/DiscordUtils"), exports);
// Legacy utilities (maintained for compatibility)
__exportStar(require("./embedBuilder"), exports);
__exportStar(require("./errorHandler"), exports);
__exportStar(require("./roleResolver"), exports);
// Shorthand exports for common utilities
var ValidationUtils_1 = require("./validation/ValidationUtils");
Object.defineProperty(exports, "ValidationUtils", { enumerable: true, get: function () { return ValidationUtils_1.ValidationUtils; } });
var FormatUtils_1 = require("./formatting/FormatUtils");
Object.defineProperty(exports, "FormatUtils", { enumerable: true, get: function () { return FormatUtils_1.FormatUtils; } });
var DiscordUtils_1 = require("./discord/DiscordUtils");
Object.defineProperty(exports, "DiscordUtils", { enumerable: true, get: function () { return DiscordUtils_1.DiscordUtils; } });
// Legacy shorthand exports
var embedBuilder_1 = require("./embedBuilder");
Object.defineProperty(exports, "formatCoins", { enumerable: true, get: function () { return embedBuilder_1.formatCoins; } });
Object.defineProperty(exports, "createSuccessEmbed", { enumerable: true, get: function () { return embedBuilder_1.createSuccessEmbed; } });
Object.defineProperty(exports, "createErrorEmbed", { enumerable: true, get: function () { return embedBuilder_1.createErrorEmbed; } });
Object.defineProperty(exports, "createEconomyEmbed", { enumerable: true, get: function () { return embedBuilder_1.createEconomyEmbed; } });
var errorHandler_1 = require("./errorHandler");
Object.defineProperty(exports, "withErrorHandler", { enumerable: true, get: function () { return errorHandler_1.withErrorHandler; } });
Object.defineProperty(exports, "ValidationError", { enumerable: true, get: function () { return errorHandler_1.ValidationError; } });
Object.defineProperty(exports, "DatabaseError", { enumerable: true, get: function () { return errorHandler_1.DatabaseError; } });
var roleResolver_1 = require("./roleResolver");
Object.defineProperty(exports, "resolveRole", { enumerable: true, get: function () { return roleResolver_1.resolveRole; } });
//# sourceMappingURL=index.js.map