"use strict";
/**
 * Balance Command
 * Refactored balance command using the new command architecture
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BalanceCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const embedBuilder_1 = require("../../utils/embedBuilder");
const economyService_1 = require("../../services/economyService");
/**
 * Balance command implementation
 */
class BalanceCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'balance',
            description: 'Check your Phalanx Loyalty Coin balance',
            category: BaseCommand_1.CommandCategory.ECONOMY,
            requiredFeatures: ['ECONOMY_SYSTEM'],
        });
    }
    /**
     * Execute the balance command
     */
    async executeCommand(context) {
        const { interaction } = context;
        const discordId = interaction.user.id;
        try {
            // Use ensureUser helper to handle user creation/fetch
            const user = await (0, economyService_1.ensureUser)(discordId);
            // Create balance embed
            const embed = (0, embedBuilder_1.createEconomyEmbed)('Your Balance')
                .setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.COINS} **${(0, embedBuilder_1.formatCoins)(user.balance)}**`);
            // Dynasty system removed: related fields skipped
            // Add user info
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            // Add quick action buttons
            const buttons = (0, embedBuilder_1.createQuickActionButtons)();
            await interaction.reply({
                embeds: [embed],
                components: [buttons],
                ephemeral: false
            });
            this.logger.info(`Balance checked for user ${interaction.user.username}: ${user.balance} PLC`);
        }
        catch (error) {
            this.logger.error('Error executing balance command', { error, userId: discordId });
            throw error;
        }
    }
}
exports.BalanceCommand = BalanceCommand;
//# sourceMappingURL=BalanceCommand.js.map