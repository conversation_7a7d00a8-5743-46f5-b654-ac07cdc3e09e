import { Document } from 'mongoose';
export interface IUserActivity extends Document {
    discordId: string;
    guildId: string;
    lastSeen: Date;
    loginStreak: number;
    longestLoginStreak: number;
    lastLoginDate: Date;
    serverJoinDate: Date;
    totalDaysActive: number;
    dailyMessageCount: number;
    weeklyMessageCount: number;
    totalMessages: number;
    lastMessageDate: Date;
    uniqueChannelsToday: string[];
    uniqueChannelsThisWeek: string[];
    dailyVoiceMinutes: number;
    weeklyVoiceMinutes: number;
    totalVoiceMinutes: number;
    lastVoiceDate: Date;
    uniqueVoiceChannelsToday: string[];
    uniqueVoiceChannelsThisWeek: string[];
    dailyReactionCount: number;
    weeklyReactionCount: number;
    totalReactions: number;
    lastReactionDate: Date;
    uniqueReactionTypesToday: string[];
    uniqueReactionTypesThisWeek: string[];
    lastDailyReset: Date;
    lastWeeklyReset: Date;
    createdAt: Date;
    updatedAt: Date;
}
declare const _default: import("mongoose").Model<IUserActivity, {}, {}, {}, Document<unknown, {}, IUserActivity, {}> & IUserActivity & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=UserActivity.d.ts.map