import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits, Role, GuildMember } from 'discord.js';
import { withErrorHandler, ValidationError, PermissionError, DatabaseError } from '../utils/errorHandler';
import { createSuccessEmbed, createErrorEmbed, createAdminEmbed, EMOJIS, formatCoins } from '../utils/embedBuilder';
import { validateRolePermissions } from '../utils/roleResolver';
import User from '../models/User';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('richestrole')
        .setDescription('Assign a role to the user with the highest Phalanx Loyalty Coin balance (admin only)')
        .addRoleOption(option =>
            option.setName('role')
                .setDescription('The role to assign to the richest member')
                .setRequired(true)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    execute: with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(async (interaction: ChatInputCommandInteraction) => {
        // Check permissions
        if (!interaction.guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError('You need Administrator permissions to use this command.');
        }

        const role = interaction.options.getRole('role', true) as Role;

        try {
            // Validate role permissions
            validateRolePermissions(interaction.guild, role);

            // Find the user with the highest balance
            const richestUser = await User.findOne().sort({ balance: -1 }).limit(1);

            if (!richestUser || richestUser.balance <= 0) {
                const embed = createErrorEmbed('No Eligible Users')
                    .setDescription(`${EMOJIS.ADMIN.WARNING} **No Users Found**\n\nNo users with Phalanx Loyalty Coins found in the database.`)
                    .addFields({
                        name: `${EMOJIS.MISC.LIGHTBULB} Suggestion`,
                        value: 'Users need to have earned at least 1 PLC to be eligible for this role.',
                        inline: false
                    });

                await interaction.reply({ embeds: [embed], ephemeral: false });
                return;
            }

            // Check if the richest user is still in the server
            let richestMember: GuildMember;
            try {
                richestMember = await interaction.guild.members.fetch(richestUser.discordId);
            } catch (error) {
                // User not in server, find next richest user who is in server
                const allUsers = await User.find({ balance: { $gt: 0 } }).sort({ balance: -1 });
                let foundMember: GuildMember | null = null;
                let foundUser = null;

                for (const user of allUsers) {
                    try {
                        foundMember = await interaction.guild.members.fetch(user.discordId);
                        foundUser = user;
                        break;
                    } catch {
                        // User not in server, continue to next
                        continue;
                    }
                }

                if (!foundMember || !foundUser) {
                    const embed = createErrorEmbed('No Eligible Members')
                        .setDescription(`${EMOJIS.ADMIN.WARNING} **No Eligible Members**\n\nNo users with PLC balances are currently in this server.`)
                        .addFields({
                            name: `${EMOJIS.ADMIN.INFO} Database Status`,
                            value: `Found ${allUsers.length} user(s) with PLC balances, but none are in this server.`,
                            inline: false
                        });

                    await interaction.reply({ embeds: [embed], ephemeral: false });
                    return;
                }

                richestMember = foundMember;
                richestUser.discordId = foundUser.discordId;
                richestUser.balance = foundUser.balance;
            }

            // Get current role holders
            const currentHolders = role.members;
            const previousHoldersList = currentHolders.map(member => member.displayName).slice(0, 5); // Limit to 5 for display

            // Remove role from all current holders
            const removePromises = currentHolders.map(member => member.roles.remove(role));
            await Promise.all(removePromises);

            // Assign role to the richest user
            await richestMember.roles.add(role);

            // Create success response
            const embed = createSuccessEmbed('Richest Role Assigned')
                .setDescription(`${EMOJIS.SUCCESS.PARTY} **Role Updated!**\n\nThe "${role.name}" role has been assigned to the richest member.`)
                .addFields(
                    {
                        name: `${EMOJIS.ECONOMY.COINS} New Role Holder`,
                        value: `**${richestMember.displayName}**\nBalance: ${formatCoins(richestUser.balance)}`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.ADMIN.SETTINGS} Role Information`,
                        value: `**Role:** ${role.name}\n**Previous Holders:** ${currentHolders.size}\n**Action:** Role reassigned`,
                        inline: true
                    }
                );

            // Add previous holders info if any
            if (previousHoldersList.length > 0) {
                const previousHoldersText = previousHoldersList.length > 5 
                    ? `${previousHoldersList.join(', ')} and ${currentHolders.size - 5} more`
                    : previousHoldersList.join(', ');
                
                embed.addFields({
                    name: `${EMOJIS.ADMIN.INFO} Previous Holders`,
                    value: previousHoldersText || 'None',
                    inline: false
                });
            }

            // Add user thumbnail
            embed.setThumbnail(richestMember.displayAvatarURL({ size: 128 }));

            await interaction.reply({ embeds: [embed], ephemeral: false });

        } catch (error) {
            if (error instanceof ValidationError || error instanceof PermissionError) {
                throw error;
            }
            
            if (error instanceof Error) {
                throw new DatabaseError(`Failed to assign richest role: ${error.message}`);
            }
            
            throw new DatabaseError('An unexpected error occurred while assigning the richest role.');
        }
    })
};
