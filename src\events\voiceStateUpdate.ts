/**
 * Voice State Update Event Handler
 * Handles Discord voice state update events for milestone tracking
 */

import { VoiceState } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';

/**
 * Voice state update event handler
 */
export class VoiceStateUpdateEventHandler extends BaseEventHandler {
  public readonly name = 'voiceStateUpdate';

  constructor(app: IApplicationContext) {
    super(app, 'voiceStateUpdate');
  }

  /**
   * Execute voice state update event
   */
  async execute(oldState: VoiceState, newState: VoiceState): Promise<void> {
    try {
      // Skip bot users
      if (newState.member?.user.bot) return;

      const userId = newState.member?.user.id;
      const guildId = newState.guild.id;

      if (!userId) return;

      // Track voice activity for milestones
      if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
        await this.trackVoiceActivity(oldState, newState, userId, guildId);
      }

    } catch (error) {
      this.handleError(error, {
        userId: newState.member?.user.id,
        guildId: newState.guild.id,
        oldChannelId: oldState.channel?.id,
        newChannelId: newState.channel?.id,
      });
    }
  }

  /**
   * Track voice activity for milestones
   */
  private async trackVoiceActivity(
    oldState: VoiceState,
    newState: VoiceState,
    userId: string,
    guildId: string
  ): Promise<void> {
    try {
      // We'll track when users join voice channels
      if (!oldState.channel && newState.channel) {
        // User joined a voice channel
        this.logExecution(`User joined voice channel: ${newState.channel.name}`, {
          userId,
          guildId,
          channelId: newState.channel.id,
          channelName: newState.channel.name,
        });

        // Import here to avoid circular dependencies
        const { checkAndProcessMilestones } = await import('../services/milestoneService');

        const milestoneResults = await checkAndProcessMilestones(
          this.app.client,
          userId,
          guildId,
          'voice',
          {
            channelId: newState.channel.id,
            minutes: 1, // Initial join counts as 1 minute
            timestamp: new Date()
          }
        );

        if (milestoneResults.length > 0) {
          this.logger.info(`[VoiceStateUpdate] User ${newState.member?.displayName} achieved ${milestoneResults.length} milestone(s) from voice activity`);
        }
      }
      
      // Track when users leave voice channels (for future voice time tracking)
      else if (oldState.channel && !newState.channel) {
        this.logExecution(`User left voice channel: ${oldState.channel.name}`, {
          userId,
          guildId,
          channelId: oldState.channel.id,
          channelName: oldState.channel.name,
        });
      }
      
      // Track when users switch voice channels
      else if (oldState.channel && newState.channel && oldState.channel.id !== newState.channel.id) {
        this.logExecution(`User switched voice channels: ${oldState.channel.name} -> ${newState.channel.name}`, {
          userId,
          guildId,
          oldChannelId: oldState.channel.id,
          newChannelId: newState.channel.id,
          oldChannelName: oldState.channel.name,
          newChannelName: newState.channel.name,
        });
      }

    } catch (error) {
      this.logger.error('[VoiceStateUpdate] Error processing voice milestones', {
        error,
        userId,
        guildId,
        oldChannelId: oldState.channel?.id,
        newChannelId: newState.channel?.id,
      });
    }
  }
}
