/**
 * Configuration Module Index
 * Centralized exports for all configuration modules
 */
export { ENV, isDevelopment, isProduction, isTest, getDatabaseConfig, getDiscordConfig, getLoggingConfig } from './environment';
export { ECONOMY, MILESTONES, DYNASTY, DISCORD, DATABASE, ERROR_HANDLING, LOGGING, FEATURE<PERSON>, VA<PERSON>ID<PERSON>ION, SCHEDULES } from './constants';
export { featureManager, isFeatureActive, requiresAdminPermission, isGuildSpecificFeature, requireFeature, FEATURE_REGISTRY } from './features';
export { runtimeConfig, isFeatureActiveRuntime, requireFeatureRuntime } from './runtime';
export type { FeatureConfig } from './features';
export type { ConfigChangeEvent, FeatureOverride } from './runtime';
//# sourceMappingURL=index.d.ts.map