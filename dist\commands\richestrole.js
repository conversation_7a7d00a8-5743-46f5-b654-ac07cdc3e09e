"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const roleResolver_1 = require("../utils/roleResolver");
const User_1 = __importDefault(require("../models/User"));
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('richestrole')
        .setDescription('Assign a role to the user with the highest Phalanx Loyalty Coin balance (admin only)')
        .addRoleOption(option => option.setName('role')
        .setDescription('The role to assign to the richest member')
        .setRequired(true))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        // Check permissions
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError('You need Administrator permissions to use this command.');
        }
        const role = interaction.options.getRole('role', true);
        try {
            // Validate role permissions
            (0, roleResolver_1.validateRolePermissions)(interaction.guild, role);
            // Find the user with the highest balance
            const richestUser = await User_1.default.findOne().sort({ balance: -1 }).limit(1);
            if (!richestUser || richestUser.balance <= 0) {
                const embed = (0, embedBuilder_1.createErrorEmbed)('No Eligible Users')
                    .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **No Users Found**\n\nNo users with Phalanx Loyalty Coins found in the database.`)
                    .addFields({
                    name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} Suggestion`,
                    value: 'Users need to have earned at least 1 PLC to be eligible for this role.',
                    inline: false
                });
                await interaction.reply({ embeds: [embed], ephemeral: false });
                return;
            }
            // Check if the richest user is still in the server
            let richestMember;
            try {
                richestMember = await interaction.guild.members.fetch(richestUser.discordId);
            }
            catch (error) {
                // User not in server, find next richest user who is in server
                const allUsers = await User_1.default.find({ balance: { $gt: 0 } }).sort({ balance: -1 });
                let foundMember = null;
                let foundUser = null;
                for (const user of allUsers) {
                    try {
                        foundMember = await interaction.guild.members.fetch(user.discordId);
                        foundUser = user;
                        break;
                    }
                    catch {
                        // User not in server, continue to next
                        continue;
                    }
                }
                if (!foundMember || !foundUser) {
                    const embed = (0, embedBuilder_1.createErrorEmbed)('No Eligible Members')
                        .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **No Eligible Members**\n\nNo users with PLC balances are currently in this server.`)
                        .addFields({
                        name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Database Status`,
                        value: `Found ${allUsers.length} user(s) with PLC balances, but none are in this server.`,
                        inline: false
                    });
                    await interaction.reply({ embeds: [embed], ephemeral: false });
                    return;
                }
                richestMember = foundMember;
                richestUser.discordId = foundUser.discordId;
                richestUser.balance = foundUser.balance;
            }
            // Get current role holders
            const currentHolders = role.members;
            const previousHoldersList = currentHolders.map(member => member.displayName).slice(0, 5); // Limit to 5 for display
            // Remove role from all current holders
            const removePromises = currentHolders.map(member => member.roles.remove(role));
            await Promise.all(removePromises);
            // Assign role to the richest user
            await richestMember.roles.add(role);
            // Create success response
            const embed = (0, embedBuilder_1.createSuccessEmbed)('Richest Role Assigned')
                .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Role Updated!**\n\nThe "${role.name}" role has been assigned to the richest member.`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} New Role Holder`,
                value: `**${richestMember.displayName}**\nBalance: ${(0, embedBuilder_1.formatCoins)(richestUser.balance)}`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} Role Information`,
                value: `**Role:** ${role.name}\n**Previous Holders:** ${currentHolders.size}\n**Action:** Role reassigned`,
                inline: true
            });
            // Add previous holders info if any
            if (previousHoldersList.length > 0) {
                const previousHoldersText = previousHoldersList.length > 5
                    ? `${previousHoldersList.join(', ')} and ${currentHolders.size - 5} more`
                    : previousHoldersList.join(', ');
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Previous Holders`,
                    value: previousHoldersText || 'None',
                    inline: false
                });
            }
            // Add user thumbnail
            embed.setThumbnail(richestMember.displayAvatarURL({ size: 128 }));
            await interaction.reply({ embeds: [embed], ephemeral: false });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError || error instanceof errorHandler_1.PermissionError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(`Failed to assign richest role: ${error.message}`);
            }
            throw new errorHandler_1.DatabaseError('An unexpected error occurred while assigning the richest role.');
        }
    })
};
//# sourceMappingURL=richestrole.js.map