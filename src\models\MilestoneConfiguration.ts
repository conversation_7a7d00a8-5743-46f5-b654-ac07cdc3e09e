import { Schema, model, Document } from 'mongoose';

export interface IMilestoneConfiguration extends Document {
    guildId: string;
    category: 'time_based' | 'participation_diversity' | 'loyalty' | 'engagement';
    milestoneType: string; // e.g., 'login_streak', 'channel_diversity', 'server_anniversary', 'reaction_diversity'
    enabled: boolean;
    rewardAmount: number;
    maxRewardsPerWeek: number;
    maxRewardsPerDay: number;
    diminishingReturns: boolean;
    diminishingFactor: number; // Multiplier for subsequent rewards (e.g., 0.8 = 20% reduction)
    requirements: {
        threshold?: number; // Days for streaks, channels for diversity, etc.
        minMessageLength?: number; // For message quality
        minVoiceTime?: number; // Minutes for voice activity
        cooldownHours?: number; // Hours between same milestone type
    };
    createdAt: Date;
    updatedAt: Date;
}

const milestoneConfigurationSchema = new Schema<IMilestoneConfiguration>({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true
    },
    category: {
        type: String,
        enum: ['time_based', 'participation_diversity', 'loyalty', 'engagement'],
        required: [true, 'Category is required'],
        index: true
    },
    milestoneType: {
        type: String,
        required: [true, 'Milestone type is required'],
        index: true
    },
    enabled: {
        type: Boolean,
        default: true,
        index: true
    },
    rewardAmount: {
        type: Number,
        required: [true, 'Reward amount is required'],
        min: [1, 'Reward amount must be positive'],
        max: [100, 'Reward amount cannot exceed 100 PLC per milestone']
    },
    maxRewardsPerWeek: {
        type: Number,
        default: 10,
        min: [1, 'Must allow at least 1 reward per week'],
        max: [50, 'Cannot exceed 50 rewards per week']
    },
    maxRewardsPerDay: {
        type: Number,
        default: 3,
        min: [1, 'Must allow at least 1 reward per day'],
        max: [10, 'Cannot exceed 10 rewards per day']
    },
    diminishingReturns: {
        type: Boolean,
        default: true
    },
    diminishingFactor: {
        type: Number,
        default: 0.8,
        min: [0.1, 'Diminishing factor must be at least 0.1'],
        max: [1.0, 'Diminishing factor cannot exceed 1.0']
    },
    requirements: {
        threshold: {
            type: Number,
            min: [1, 'Threshold must be positive']
        },
        minMessageLength: {
            type: Number,
            default: 10,
            min: [1, 'Minimum message length must be positive']
        },
        minVoiceTime: {
            type: Number,
            default: 5,
            min: [1, 'Minimum voice time must be positive']
        },
        cooldownHours: {
            type: Number,
            default: 24,
            min: [1, 'Cooldown must be at least 1 hour'],
            max: [168, 'Cooldown cannot exceed 1 week']
        }
    }
}, {
    timestamps: true
});

// Compound indexes for efficient queries
milestoneConfigurationSchema.index({ guildId: 1, category: 1, enabled: 1 });
milestoneConfigurationSchema.index({ guildId: 1, milestoneType: 1 }, { unique: true });

export default model<IMilestoneConfiguration>('MilestoneConfiguration', milestoneConfigurationSchema);
