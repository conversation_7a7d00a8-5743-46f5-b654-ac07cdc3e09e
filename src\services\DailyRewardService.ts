import { findOrCreateUser } from '../utils/userProfile';
import { DAILY_REWARD_AMOUNT, DAILY_REWARD_COOLDOWN } from '../constants/economy';

export class DailyRewardService {
    /**
     * Grants a daily reward to a user if they are eligible.
     * @param userId The user's Discord ID.
     * @returns An object indicating success, the amount granted, and a message.
     */
    public async grantDailyReward(userId: string): Promise<{ success: boolean; amount: number; message: string }> {
        const userProfile = await findOrCreateUser(userId);

        if (userProfile.lastDaily) {
            const cooldown = Date.now() - userProfile.lastDaily.getTime();
            if (cooldown < DAILY_REWARD_COOLDOWN) {
                const timeLeft = DAILY_REWARD_COOLDOWN - cooldown;
                const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                return {
                    success: false,
                    amount: 0,
                    message: `You have already claimed your daily reward. Please wait ${hours}h ${minutes}m.`,
                };
            }
        }

        userProfile.balance += DAILY_REWARD_AMOUNT;
        userProfile.lastDaily = new Date();
        await userProfile.save();

        return { success: true, amount: DAILY_REWARD_AMOUNT, message: `You have claimed your daily reward of ${DAILY_REWARD_AMOUNT} coins!` };
    }
}