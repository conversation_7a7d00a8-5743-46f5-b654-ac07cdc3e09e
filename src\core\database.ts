/**
 * Database Connection Manager
 * Centralized database connection and management
 */

import mongoose from 'mongoose';
import { IDatabaseService, ILogger, DatabaseConfig } from './interfaces';
import { getDatabaseConfig } from '../config';
import { DATABASE } from '../config/constants';

/**
 * Database service implementation
 */
export class DatabaseService implements IDatabaseService {
  public readonly name = 'DatabaseService';
  private config: DatabaseConfig;
  private logger: ILogger;
  private connectionPromise: Promise<void> | null = null;

  constructor(logger: ILogger, config?: DatabaseConfig) {
    this.logger = logger;
    this.config = config || getDatabaseConfig();
    
    // Set mongoose options
    mongoose.set('strictQuery', false);
    
    // Setup connection event handlers
    this.setupEventHandlers();
  }

  /**
   * Initialize the database service
   */
  async initialize(): Promise<void> {
    this.logger.info('[Database] Initializing database service');
    await this.connect();
    await this.setupIndexes();
    await this.performCleanup();
  }

  /**
   * Connect to the database
   */
  async connect(): Promise<void> {
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    this.connectionPromise = this._connect();
    return this.connectionPromise;
  }

  /**
   * Internal connection method
   */
  private async _connect(): Promise<void> {
    try {
      this.logger.info('[Database] Connecting to MongoDB...');
      this.logger.debug('[Database] Connection URI:', this.config.uri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'));
      this.logger.debug('[Database] Connection options:', this.config.options);

      await mongoose.connect(this.config.uri, {
        ...this.config.options,
        serverSelectionTimeoutMS: DATABASE.CONNECTION_TIMEOUT_MS,
      });
      this.logger.info('[Database] Successfully connected to MongoDB');
    } catch (error: any) {
      // Print full error details for debugging
      console.error('RAW MONGOOSE ERROR:', error);
      this.logger.error('[Database] Failed to connect to MongoDB', {
        error: error?.message,
        stack: error?.stack,
        name: error?.name,
        code: error?.code,
        reason: error?.reason,
        errorObject: error,
        configUri: this.config.uri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'),
        configOptions: this.config.options
      });
      this.connectionPromise = null;
      throw error;
    }
  }

  /**
   * Disconnect from the database
   */
  async disconnect(): Promise<void> {
    try {
      this.logger.info('[Database] Disconnecting from MongoDB...');
      await mongoose.disconnect();
      this.connectionPromise = null;
      this.logger.info('[Database] Disconnected from MongoDB');
    } catch (error) {
      this.logger.error('[Database] Error disconnecting from MongoDB', { error });
      throw error;
    }
  }

  /**
   * Check if connected to database
   */
  isConnected(): boolean {
    return mongoose.connection.readyState === 1;
  }

  /**
   * Get connection status string
   */
  getConnectionStatus(): string {
    const states = ['disconnected', 'connected', 'connecting', 'disconnecting'];
    return states[mongoose.connection.readyState] || 'unknown';
  }

  /**
   * Shutdown the database service
   */
  async shutdown(): Promise<void> {
    this.logger.info('[Database] Shutting down database service');
    await this.disconnect();
  }

  /**
   * Setup database event handlers
   */
  private setupEventHandlers(): void {
    mongoose.connection.on('connected', () => {
      this.logger.info('[Database] Mongoose connected to MongoDB');
    });

    mongoose.connection.on('error', (error) => {
      this.logger.error('[Database] Mongoose connection error', {
        error: error?.message,
        stack: error?.stack,
        name: error?.name,
        code: error?.code,
        reason: error?.reason,
        errorObject: error
      });
    });

    mongoose.connection.on('disconnected', () => {
      this.logger.warn('[Database] Mongoose disconnected from MongoDB');
    });

    // Handle application termination
    process.on('SIGINT', async () => {
      await this.shutdown();
      process.exit(0);
    });
  }

  /**
   * Setup required database indexes
   */
  private async setupIndexes(): Promise<void> {
    try {
      this.logger.info('[Database] Setting up indexes...');
      
      const db = mongoose.connection.db;
      if (!db) {
        throw new Error('Database connection not established');
      }

      // Setup indexes from configuration
      for (const indexConfig of DATABASE.REQUIRED_INDEXES) {
        const collection = db.collection(indexConfig.collection);
        
        try {
          const indexOptions: any = { background: true };
          if ('unique' in indexConfig) {
            indexOptions.unique = indexConfig.unique;
          }
          await collection.createIndex(indexConfig.index, indexOptions);
          
          this.logger.debug(`[Database] Created index for ${indexConfig.collection}`, {
            index: indexConfig.index,
            unique: 'unique' in indexConfig ? indexConfig.unique : undefined,
          });
        } catch (error: any) {
          // Index might already exist
          if (error.code !== 11000) {
            this.logger.warn(`[Database] Failed to create index for ${indexConfig.collection}`, {
              error: error.message,
              index: indexConfig.index,
            });
          }
        }
      }
      
      this.logger.info('[Database] Index setup completed');
    } catch (error) {
      this.logger.error('[Database] Failed to setup indexes', { error });
      throw error;
    }
  }

  /**
   * Perform database cleanup operations
   */
  private async performCleanup(): Promise<void> {
    try {
      this.logger.info('[Database] Performing cleanup operations...');
      
      const db = mongoose.connection.db;
      if (!db) {
        throw new Error('Database connection not established');
      }

      const usersCollection = db.collection('users');

      // Remove old userId index if it exists
      try {
        await usersCollection.dropIndex('userId_1');
        this.logger.info('[Database] Dropped old userId_1 index');
      } catch (error) {
        // Index might not exist, which is fine
        this.logger.debug('[Database] userId_1 index not found (expected)');
      }

      // Clean up corrupted records
      const deleteResult = await usersCollection.deleteMany({
        $or: [
          { discordId: null },
          { discordId: { $exists: false } },
          { userId: { $exists: true } } // Remove old schema records
        ]
      });

      if (deleteResult.deletedCount > 0) {
        this.logger.info(`[Database] Cleaned up ${deleteResult.deletedCount} corrupted user records`);
      }

      this.logger.info('[Database] Cleanup operations completed');
    } catch (error) {
      this.logger.error('[Database] Failed to perform cleanup', { error });
      // Don't throw here as this is not critical for startup
    }
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<any> {
    try {
      const db = mongoose.connection.db;
      if (!db) {
        throw new Error('Database connection not established');
      }

      const stats = await db.stats();
      return {
        collections: stats.collections,
        dataSize: stats.dataSize,
        indexSize: stats.indexSize,
        objects: stats.objects,
        avgObjSize: stats.avgObjSize,
      };
    } catch (error) {
      this.logger.error('[Database] Failed to get stats', { error });
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected()) {
        return false;
      }

      // Perform a simple operation to verify connection
      const db = mongoose.connection.db;
      await db?.admin().ping();
      
      return true;
    } catch (error) {
      this.logger.error('[Database] Health check failed', { error });
      return false;
    }
  }
}
