"use strict";
/**
 * Base Event Handler
 * Abstract base class for all Discord event handlers
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.eventRegistry = exports.EventHandlerRegistry = exports.BaseEventHandler = void 0;
const logger_1 = require("../core/logger");
/**
 * Abstract base event handler class
 */
class BaseEventHandler {
    constructor(app, eventName) {
        this.once = false;
        this.app = app;
        this.logger = (0, logger_1.createLogger)(`event:${eventName}`);
    }
    /**
     * Handle errors in event execution
     */
    handleError(error, context) {
        this.logger.error(`[${this.name}] Event handler error`, {
            error,
            context,
            eventName: this.name,
        });
    }
    /**
     * Log event execution
     */
    logExecution(message, meta) {
        this.logger.debug(`[${this.name}] ${message}`, meta);
    }
    /**
     * Check if a feature is enabled before processing
     */
    isFeatureEnabled(featureName) {
        // Import here to avoid circular dependencies
        const { isFeatureActive } = require('../config');
        return isFeatureActive(featureName);
    }
}
exports.BaseEventHandler = BaseEventHandler;
/**
 * Event handler registry
 */
class EventHandlerRegistry {
    constructor() {
        this.handlers = new Map();
        this.logger = (0, logger_1.createLogger)('event-registry');
    }
    /**
     * Register an event handler
     */
    register(handler) {
        if (this.handlers.has(handler.name)) {
            this.logger.warn(`[EventRegistry] Handler already registered: ${handler.name}`);
            return;
        }
        this.handlers.set(handler.name, handler);
        this.logger.debug(`[EventRegistry] Registered handler: ${handler.name}`);
    }
    /**
     * Get all registered handlers
     */
    getHandlers() {
        return Array.from(this.handlers.values());
    }
    /**
     * Get handler by name
     */
    getHandler(name) {
        return this.handlers.get(name);
    }
    /**
     * Unregister a handler
     */
    unregister(name) {
        const removed = this.handlers.delete(name);
        if (removed) {
            this.logger.debug(`[EventRegistry] Unregistered handler: ${name}`);
        }
        return removed;
    }
    /**
     * Clear all handlers
     */
    clear() {
        this.handlers.clear();
        this.logger.debug('[EventRegistry] Cleared all handlers');
    }
}
exports.EventHandlerRegistry = EventHandlerRegistry;
/**
 * Global event registry instance
 */
exports.eventRegistry = new EventHandlerRegistry();
//# sourceMappingURL=base.js.map