{"version": 3, "file": "InteractionHandler.js", "sourceRoot": "", "sources": ["../../../src/legacy/events/InteractionHandler.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,2DAAiF;AACjF,2DAAkD;AAElD;;GAEG;AACH,MAAa,wBAAwB;IAGnC,YAAY,MAAc;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,WAAwB;QAC9C,IAAI,WAAW,CAAC,kBAAkB,EAAE,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC;YAClC,MAAM,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,WAAgB;QACnD,MAAM,OAAO,GAAI,IAAI,CAAC,MAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC3E,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,iCAAkB,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,WAA8B;QAClE,IAAI,CAAC;YACH,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC;YAEjC,IAAI,QAAQ,KAAK,eAAe,EAAE,CAAC;gBACjC,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACxD,CAAC;iBAAM,IAAI,QAAQ,KAAK,mBAAmB,EAAE,CAAC;gBAC5C,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAC5D,CAAC;iBAAM,IAAI,QAAQ,KAAK,aAAa,EAAE,CAAC;gBACtC,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC;iBAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAC9D,CAAC;iBAAM,IAAI,QAAQ,KAAK,kBAAkB,EAAE,CAAC;gBAC3C,MAAM,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC;iBAAM,IAAI,QAAQ,KAAK,iBAAiB,EAAE,CAAC;gBAC1C,MAAM,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;YACnD,CAAC;iBAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxC,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YACrD,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,CAAC,KAAK,CAAC;oBACtB,OAAO,EAAE,iDAAiD;oBAC1D,SAAS,EAAE,IAAI;iBAChB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,gCAAiB,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,WAA8B,EAAE,WAAmB;QAClF,MAAM,OAAO,GAAI,IAAI,CAAC,MAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC/D,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,MAAM,WAAW,CAAC,KAAK,CAAC;gBACtB,OAAO,EAAE,YAAY,WAAW,cAAc;gBAC9C,SAAS,EAAE,IAAI;aAChB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB,CAAC,WAA8B,EAAE,QAAgB;QACtF,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,WAAW,CAAC,KAAK,CAAC;YACtB,OAAO,EAAE,sIAAsI;YAC/I,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB,CAAC,WAA8B;QACpE,MAAM,oBAAoB,GAAI,MAAc,CAAC,oBAAoB,CAAC;QAClE,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,MAAM,WAAW,CAAC,KAAK,CAAC;gBACtB,OAAO,EAAE,+DAA+D;gBACxE,SAAS,EAAE,IAAI;aAChB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,qBAAqB,GAAG,WAAW,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,CAAC;QACnE,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAEzE,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,WAAW,CAAC,KAAK,CAAC;gBACtB,OAAO,EAAE,uEAAuE;gBAChF,SAAS,EAAE,IAAI;aAChB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,oCAAoC;QACpC,oBAAoB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;QAEnD,2BAA2B;QAC3B,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;QAChC,MAAM,cAAc,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC1D,MAAM,cAAc,CAAC,mBAAmB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,WAA8B;QACnE,MAAM,oBAAoB,GAAI,MAAc,CAAC,oBAAoB,CAAC;QAClE,MAAM,qBAAqB,GAAG,WAAW,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,CAAC;QAEnE,IAAI,oBAAoB,IAAI,qBAAqB,EAAE,CAAC;YAClD,oBAAoB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,WAAW,CAAC,MAAM,CAAC;YACvB,OAAO,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,OAAO,0BAA0B;YAC1D,MAAM,EAAE,EAAE;YACV,UAAU,EAAE,EAAE;SACf,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,WAA8B,EAAE,QAAgB;QAC7E,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAElD,yCAAyC;QACzC,MAAM,sBAAsB,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;QAE9E,IAAI,sBAAsB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACjD,+BAA+B;YAC/B,MAAM,OAAO,GAAI,IAAI,CAAC,MAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAC/D,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,CAAC,KAAK,CAAC;oBACtB,OAAO,EAAE,YAAY,WAAW,cAAc;oBAC9C,SAAS,EAAE,IAAI;iBAChB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,6CAA6C;YAC7C,MAAM,mBAAmB,GAA2B;gBAClD,KAAK,EAAE,4DAA4D;gBACnE,SAAS,EAAE,wEAAwE;gBACnF,UAAU,EAAE,kFAAkF;gBAC9F,YAAY,EAAE,4EAA4E;gBAC1F,MAAM,EAAE,oEAAoE;gBAC5E,MAAM,EAAE,wEAAwE;gBAChF,KAAK,EAAE,yHAAyH;gBAChI,gBAAgB,EAAE,2HAA2H;gBAC7I,mBAAmB,EAAE,8LAA8L;gBACnN,aAAa,EAAE,8LAA8L;gBAC7M,cAAc,EAAE,4EAA4E;gBAC5F,aAAa,EAAE,+GAA+G;aAC/H,CAAC;YAEF,MAAM,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC,IAAI,YAAY,WAAW,WAAW,CAAC;YAE3F,MAAM,WAAW,CAAC,KAAK,CAAC;gBACtB,OAAO,EAAE,MAAM,WAAW,OAAO,WAAW,EAAE;gBAC9C,SAAS,EAAE,IAAI;aAChB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AA1LD,4DA0LC;AAED,kBAAe,wBAAwB,CAAC"}