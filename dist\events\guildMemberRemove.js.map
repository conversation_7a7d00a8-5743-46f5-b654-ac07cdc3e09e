{"version": 3, "file": "guildMemberRemove.js", "sourceRoot": "", "sources": ["../../src/events/guildMemberRemove.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,iCAA0C;AAG1C;;GAEG;AACH,MAAa,6BAA8B,SAAQ,uBAAgB;IAGjE,YAAY,GAAwB;QAClC,KAAK,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;QAHlB,SAAI,GAAG,mBAAmB,CAAC;IAI3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,MAAwC;QACpD,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEvD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,IAAI,eAAe,CAAC;YACxD,MAAM,WAAW,GAAG,UAAU,EAAE,WAAW,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,IAAI,cAAc,CAAC;YAEvF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;gBAClF,OAAO;YACT,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,gBAAgB,WAAW,EAAE,EAAE;gBAC/C,MAAM;gBACN,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,EAAE;gBACzB,SAAS;aACV,CAAC,CAAC;YAEH,4BAA4B;YAC5B,IAAI,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAqB,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;YACvF,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;gBACtB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE;gBACvB,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,EAAE;gBACzB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ;aACzD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,MAAwC;QACrE,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC;gBACH,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;YAC9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAiD,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBAChF,6CAA6C;gBAC7C,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,MAAqB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,MAAmB,EACnB,MAAc,EACd,WAAmB,EACnB,SAAiB;QAEjB,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,EAAE,kBAAkB,EAAE,GAAG,wDAAa,gCAAgC,GAAC,CAAC;YAE9E,uDAAuD;YACvD,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAChE,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,gBAAgB,GAAG,CAAC,IAAI,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC;YAE5G,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,WAAW,KAAK,MAAM,cAAc,SAAS,oBAAoB,CAAC,CAAC;gBAC7H,OAAO;YACT,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,WAAW,SAAS,SAAS,kBAAkB,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,gBAAgB,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,mBAAmB,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAEhU,kBAAkB;YAClB,MAAM,aAAa,GAAG,MAAM,kBAAkB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAEvE,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;gBAC1B,MAAM,YAAY,GAAG,EAAE,CAAC;gBACxB,IAAI,aAAa,CAAC,eAAe;oBAAE,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACrE,IAAI,aAAa,CAAC,mBAAmB,GAAG,CAAC;oBAAE,YAAY,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,mBAAmB,eAAe,CAAC,CAAC;gBAClH,IAAI,aAAa,CAAC,sBAAsB,GAAG,CAAC;oBAAE,YAAY,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,sBAAsB,mBAAmB,CAAC,CAAC;gBAE5H,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wDAAwD,WAAW,KAAK,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,aAAa,CAAC,SAAS,KAAK,CAAC,CAAC;YACrJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,WAAW,EAAE,EAAE;oBAClF,MAAM,EAAE,aAAa,CAAC,MAAM;iBAC7B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE;gBACrE,KAAK;gBACL,MAAM;gBACN,WAAW;gBACX,SAAS;aACV,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AA5GD,sEA4GC"}