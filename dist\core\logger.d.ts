/**
 * Centralized Logging System
 * Winston-based logger with structured logging and multiple transports
 */
import winston from 'winston';
import { ILogger, LoggingConfig } from './interfaces';
/**
 * Logger implementation using Winston
 */
export declare class Logger implements ILogger {
    private winston;
    private config;
    constructor(config?: LoggingConfig);
    /**
     * Create Winston logger instance
     */
    private createLogger;
    /**
     * Log error message
     */
    error(message: string, meta?: any): void;
    /**
     * Log warning message
     */
    warn(message: string, meta?: any): void;
    /**
     * Log info message
     */
    info(message: string, meta?: any): void;
    /**
     * Log debug message
     */
    debug(message: string, meta?: any): void;
    /**
     * Log with specific category
     */
    logWithCategory(level: string, category: string, message: string, meta?: any): void;
    /**
     * Format metadata for logging
     */
    private formatMeta;
    /**
     * Create child logger with category
     */
    child(category: string): CategoryLogger;
    /**
     * Get Winston logger instance (for advanced usage)
     */
    getWinstonLogger(): winston.Logger;
    /**
     * Update log level
     */
    setLevel(level: string): void;
    /**
     * Close logger and cleanup resources
     */
    close(): void;
}
/**
 * Category-specific logger
 */
export declare class CategoryLogger implements ILogger {
    private parent;
    private category;
    constructor(parent: Logger, category: string);
    error(message: string, meta?: any): void;
    warn(message: string, meta?: any): void;
    info(message: string, meta?: any): void;
    debug(message: string, meta?: any): void;
}
/**
 * Get or create global logger
 */
export declare function getLogger(): Logger;
/**
 * Create logger with category
 */
export declare function createLogger(category: string): CategoryLogger;
/**
 * Logger factory for different categories
 */
export declare const loggers: {
    economy: () => CategoryLogger;
    milestone: () => CategoryLogger;
    dynasty: () => CategoryLogger;
    admin: () => CategoryLogger;
    database: () => CategoryLogger;
    discord: () => CategoryLogger;
};
/**
 * Shutdown logger
 */
export declare function shutdownLogger(): void;
export default Logger;
//# sourceMappingURL=logger.d.ts.map