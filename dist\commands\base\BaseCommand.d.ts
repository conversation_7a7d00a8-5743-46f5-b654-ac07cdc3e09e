/**
 * Base Command Class
 * Abstract base class for all Discord slash commands
 */
import { Slash<PERSON>ommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { ICommand, ILogger, CommandContext } from '../../core/interfaces';
/**
 * Command categories for organization
 */
export declare enum CommandCategory {
    ECONOMY = "economy",
    ADMIN = "admin",
    ROLE = "role",
    MILESTONE = "milestone",
    UTILITY = "utility",
    AUTOMATION = "automation"
}
/**
 * Command configuration interface
 */
export interface CommandConfig {
    name: string;
    description: string;
    category: CommandCategory;
    adminOnly?: boolean;
    guildOnly?: boolean;
    cooldown?: number;
    requiredFeatures?: string[];
    requiredPermissions?: (keyof typeof PermissionFlagsBits)[];
}
/**
 * Abstract base command class
 */
export declare abstract class BaseCommand implements ICommand {
    readonly data: SlashCommandBuilder;
    readonly category: string;
    readonly adminOnly: boolean;
    readonly guildOnly: boolean;
    readonly cooldown: number;
    readonly requiredFeatures: string[];
    protected logger: ILogger;
    private config;
    constructor(config: CommandConfig);
    /**
     * Build the slash command
     */
    private buildCommand;
    /**
     * Override in subclasses to customize the command builder
     */
    protected customizeCommand(command: SlashCommandBuilder): void;
    /**
     * Execute the command with error handling and validation
     */
    execute(interaction: ChatInputCommandInteraction): Promise<void>;
    /**
     * Abstract method for command implementation
     */
    protected abstract executeCommand(context: CommandContext): Promise<void>;
    /**
     * Validate command execution requirements
     */
    private validateExecution;
    /**
     * Log command execution
     */
    private logExecution;
    /**
     * Check if a feature is enabled
     */
    protected isFeatureEnabled(featureName: string): boolean;
    /**
     * Get command configuration
     */
    getConfig(): CommandConfig;
    /**
     * Get command metadata
     */
    getMetadata(): {
        name: string;
        description: string;
        category: string;
        adminOnly: boolean;
        guildOnly: boolean;
        cooldown: number;
        requiredFeatures: string[];
    };
}
/**
 * Command registry for managing command instances
 */
export declare class CommandRegistry {
    private commands;
    private categories;
    private logger;
    constructor();
    /**
     * Register a command
     */
    register(command: BaseCommand): void;
    /**
     * Get command by name
     */
    get(name: string): BaseCommand | undefined;
    /**
     * Get all commands
     */
    getAll(): BaseCommand[];
    /**
     * Get commands by category
     */
    getByCategory(category: CommandCategory): BaseCommand[];
    /**
     * Get all categories
     */
    getCategories(): CommandCategory[];
    /**
     * Get command count
     */
    getCount(): number;
    /**
     * Clear all commands
     */
    clear(): void;
}
/**
 * Global command registry instance
 */
export declare const commandRegistry: CommandRegistry;
//# sourceMappingURL=BaseCommand.d.ts.map