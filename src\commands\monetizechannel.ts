import { Slash<PERSON>ommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { MonetizedChannel } from '../models/MonetizedChannel';
import { withErrorHandler, ValidationError, DatabaseError, PermissionError } from '../utils/errorHandler';
import { createAdminEmbed, createSuccessEmbed, EMOJIS, COLORS } from '../utils/embedBuilder';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('monetizechannel')
        .setDescription('Enable or disable reaction rewards for the current channel (admin only)')
        .addStringOption(option =>
            option.setName('status')
                .setDescription('Enable or disable reaction rewards')
                .setRequired(true)
                .addChoices(
                    { name: 'Enable', value: 'on' },
                    { name: 'Disable', value: 'off' }
                )
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError();
        }

        const status = interaction.options.getString('status', true);
        const channelId = interaction.channelId;

        if (!channelId) {
            throw new ValidationError('This command must be used in a channel.');
        }

        // Validate status input
        if (status !== 'on' && status !== 'off') {
            throw new ValidationError('Status must be either "on" or "off".');
        }

        const enabled = status === 'on';

        try {
            // Update or create monetized channel record
            const monetizedChannel = await MonetizedChannel.findOneAndUpdate(
                { channelId },
                {
                    channelId,
                    enabled
                },
                {
                    new: true,
                    upsert: true,
                    runValidators: true
                }
            );

            // Create rich admin embed
            const statusText = enabled ? 'enabled' : 'disabled';
            const statusEmoji = enabled ? EMOJIS.SUCCESS.CHECK : EMOJIS.ADMIN.WARNING;
            const channelMention = `<#${channelId}>`;

            const embed = createAdminEmbed(`Reaction Rewards ${enabled ? 'Enabled' : 'Disabled'}`)
                .setDescription(
                    `${statusEmoji} **Channel Monetization Updated**\n\n` +
                    `Reaction rewards have been **${statusText}** for ${channelMention}`
                )
                .addFields(
                    {
                        name: `${EMOJIS.MISC.SCROLL} Channel`,
                        value: channelMention,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.ADMIN.TOOLS} Status`,
                        value: enabled ?
                            `${EMOJIS.SUCCESS.CHECK} **Active**` :
                            `${EMOJIS.ADMIN.WARNING} **Inactive**`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.ADMIN.KEY} Administrator`,
                        value: `**${interaction.user.displayName}**`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.ECONOMY.COINS} Reward Details`,
                        value: enabled ?
                            `${EMOJIS.SUCCESS.THUMBS_UP} Users earn **5 Phalanx Loyalty Coins** for their first reaction on each message\n` +
                            `${EMOJIS.ACTIONS.LIGHTNING} Anti-exploitation measures active\n` +
                            `${EMOJIS.MISC.CLOCK} 30-second rate limit between rewards` :
                            `${EMOJIS.ADMIN.LOCK} No coins will be awarded for reactions\n` +
                            `${EMOJIS.MISC.MAGNIFYING} Users can still react normally`,
                        inline: false
                    },
                    {
                        name: `${EMOJIS.MISC.CLOCK} Updated`,
                        value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                        inline: false
                    }
                )
                .setColor(enabled ? COLORS.SUCCESS : COLORS.WARNING)
                .setFooter({
                    text: 'Reaction rewards system - Administrative action logged'
                });

            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });

            // Log the change
            console.log(`[Reaction Rewards] Channel ${channelId} monetization ${statusText} by ${interaction.user.tag}`);

        } catch (error: unknown) {
            if (error instanceof Error && error.name === 'ValidationError') {
                throw new ValidationError('Invalid channel data format');
            } else if (error instanceof Error && error.name === 'MongoServerError' && (error as any).code === 11000) {
                throw new DatabaseError('Channel monetization record conflict');
            } else if (error instanceof Error) {
                throw new DatabaseError('Failed to update channel monetization settings', error);
            }
            throw new DatabaseError('Unexpected error while updating channel settings');
        }
    })
};
