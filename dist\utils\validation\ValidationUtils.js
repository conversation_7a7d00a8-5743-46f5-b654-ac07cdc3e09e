"use strict";
/**
 * Validation Utilities
 * Centralized validation functions with type safety
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationUtils = exports.RateLimitValidator = exports.FeatureValidator = exports.PermissionValidator = exports.TextValidator = exports.AmountValidator = exports.DiscordValidator = void 0;
const errorHandler_1 = require("../errorHandler");
const constants_1 = require("../../config/constants");
/**
 * Discord ID validation
 */
class DiscordValidator {
    /**
     * Validate Discord ID format
     */
    static validateDiscordId(id) {
        if (!id || typeof id !== 'string') {
            throw new errorHandler_1.ValidationError('Discord ID must be a non-empty string');
        }
        const trimmedId = id.trim();
        if (!trimmedId) {
            throw new errorHandler_1.ValidationError('Discord ID cannot be empty');
        }
        if (!constants_1.VALIDATION.DISCORD_ID_REGEX.test(trimmedId)) {
            throw new errorHandler_1.ValidationError('Invalid Discord ID format');
        }
    }
    /**
     * Validate and sanitize Discord ID
     */
    static sanitizeDiscordId(id) {
        this.validateDiscordId(id);
        return id.trim();
    }
}
exports.DiscordValidator = DiscordValidator;
/**
 * Amount validation for transactions
 */
class AmountValidator {
    /**
     * Validate transaction amount
     */
    static validateAmount(amount, context) {
        if (typeof amount !== 'number' || isNaN(amount)) {
            throw new errorHandler_1.ValidationError(`Invalid amount: must be a number${context ? ` for ${context}` : ''}`);
        }
        if (amount < constants_1.VALIDATION.MIN_TRANSACTION_AMOUNT) {
            throw new errorHandler_1.ValidationError(`Amount must be at least ${constants_1.VALIDATION.MIN_TRANSACTION_AMOUNT}${context ? ` for ${context}` : ''}`);
        }
        if (amount > constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT) {
            throw new errorHandler_1.ValidationError(`Amount cannot exceed ${constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT}${context ? ` for ${context}` : ''}`);
        }
        if (!Number.isInteger(amount)) {
            throw new errorHandler_1.ValidationError(`Amount must be a whole number${context ? ` for ${context}` : ''}`);
        }
    }
    /**
     * Validate positive amount
     */
    static validatePositiveAmount(amount, context) {
        this.validateAmount(amount, context);
        if (amount <= 0) {
            throw new errorHandler_1.ValidationError(`Amount must be greater than 0${context ? ` for ${context}` : ''}`);
        }
    }
    /**
     * Validate balance sufficiency
     */
    static validateSufficientBalance(currentBalance, requiredAmount, context) {
        if (currentBalance < requiredAmount) {
            throw new errorHandler_1.ValidationError(`Insufficient balance${context ? ` for ${context}` : ''}. ` +
                `Required: ${requiredAmount}, Available: ${currentBalance}`);
        }
    }
}
exports.AmountValidator = AmountValidator;
/**
 * Text validation
 */
class TextValidator {
    /**
     * Validate text length
     */
    static validateLength(text, maxLength, fieldName) {
        if (!text) {
            throw new errorHandler_1.ValidationError(`${fieldName} cannot be empty`);
        }
        if (text.length > maxLength) {
            throw new errorHandler_1.ValidationError(`${fieldName} cannot exceed ${maxLength} characters`);
        }
    }
    /**
     * Validate description text
     */
    static validateDescription(description) {
        this.validateLength(description, constants_1.VALIDATION.MAX_DESCRIPTION_LENGTH, 'Description');
    }
    /**
     * Validate reason text
     */
    static validateReason(reason) {
        this.validateLength(reason, constants_1.VALIDATION.MAX_REASON_LENGTH, 'Reason');
    }
    /**
     * Validate name text
     */
    static validateName(name) {
        this.validateLength(name, constants_1.VALIDATION.MAX_NAME_LENGTH, 'Name');
    }
    /**
     * Sanitize text input
     */
    static sanitizeText(text) {
        return text.trim().replace(/\s+/g, ' ');
    }
}
exports.TextValidator = TextValidator;
/**
 * Permission validation
 */
class PermissionValidator {
    /**
     * Validate admin permissions
     */
    static validateAdminPermissions(member) {
        if (!member) {
            throw new errorHandler_1.ValidationError('Member information not available');
        }
        if (!member.permissions?.has('Administrator')) {
            throw new errorHandler_1.ValidationError('This command requires administrator permissions');
        }
    }
    /**
     * Validate guild context
     */
    static validateGuildContext(guild) {
        if (!guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server');
        }
    }
    /**
     * Validate user permissions for role management
     */
    static validateRoleManagementPermissions(member) {
        if (!member) {
            throw new errorHandler_1.ValidationError('Member information not available');
        }
        const hasPermission = member.permissions?.has('Administrator') ||
            member.permissions?.has('ManageRoles');
        if (!hasPermission) {
            throw new errorHandler_1.ValidationError('This command requires Administrator or Manage Roles permissions');
        }
    }
}
exports.PermissionValidator = PermissionValidator;
/**
 * Feature validation
 */
class FeatureValidator {
    /**
     * Validate feature availability
     */
    static validateFeature(featureName, isEnabled) {
        if (!isEnabled) {
            throw new errorHandler_1.ValidationError(`The ${featureName} feature is not enabled on this server`);
        }
    }
    /**
     * Validate multiple features
     */
    static validateFeatures(features) {
        const disabledFeatures = features.filter(f => !f.enabled).map(f => f.name);
        if (disabledFeatures.length > 0) {
            throw new errorHandler_1.ValidationError(`The following required features are not enabled: ${disabledFeatures.join(', ')}`);
        }
    }
}
exports.FeatureValidator = FeatureValidator;
/**
 * Rate limiting validation
 */
class RateLimitValidator {
    /**
     * Validate rate limit
     */
    static validateRateLimit(userId, commandName, cooldownSeconds) {
        const key = `${userId}:${commandName}`;
        const now = Date.now();
        const lastUsed = this.lastUsage.get(key) || 0;
        const timeSinceLastUse = now - lastUsed;
        const cooldownMs = cooldownSeconds * 1000;
        if (timeSinceLastUse < cooldownMs) {
            const remainingSeconds = Math.ceil((cooldownMs - timeSinceLastUse) / 1000);
            throw new errorHandler_1.ValidationError(`Please wait ${remainingSeconds} second(s) before using this command again`);
        }
        this.lastUsage.set(key, now);
    }
    /**
     * Clear rate limit for user/command
     */
    static clearRateLimit(userId, commandName) {
        const key = `${userId}:${commandName}`;
        this.lastUsage.delete(key);
    }
    /**
     * Clear all rate limits
     */
    static clearAllRateLimits() {
        this.lastUsage.clear();
    }
}
exports.RateLimitValidator = RateLimitValidator;
RateLimitValidator.lastUsage = new Map();
/**
 * Comprehensive validation utility
 */
class ValidationUtils {
    /**
     * Validate common command parameters
     */
    static validateCommandParams(params) {
        if (params.userId) {
            this.discord.validateDiscordId(params.userId);
        }
        if (params.amount !== undefined) {
            this.amount.validatePositiveAmount(params.amount);
        }
        if (params.text) {
            this.text.validateDescription(params.text);
        }
        if (params.adminRequired && params.member) {
            this.permission.validateAdminPermissions(params.member);
        }
        if (params.guild) {
            this.permission.validateGuildContext(params.guild);
        }
        if (params.features) {
            // This would need to be implemented with actual feature checking
            // this.feature.validateFeatures(params.features.map(f => ({ name: f, enabled: isFeatureActive(f) })));
        }
    }
}
exports.ValidationUtils = ValidationUtils;
ValidationUtils.discord = DiscordValidator;
ValidationUtils.amount = AmountValidator;
ValidationUtils.text = TextValidator;
ValidationUtils.permission = PermissionValidator;
ValidationUtils.feature = FeatureValidator;
ValidationUtils.rateLimit = RateLimitValidator;
exports.default = ValidationUtils;
//# sourceMappingURL=ValidationUtils.js.map