"use strict";
/**
 * Ready Event Handler
 * Handles Discord client ready event and initializes scheduled tasks
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadyEventHandler = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const base_1 = require("./base");
const constants_1 = require("../config/constants");
/**
 * Ready event handler
 */
class ReadyEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'ready');
        this.name = 'ready';
        this.once = true;
    }
    /**
     * Execute ready event
     */
    async execute() {
        try {
            this.logger.info(`[Ready] Bot logged in as ${this.app.client.user?.tag}`);
            // Initialize scheduled tasks
            await this.initializeScheduledTasks();
            this.logger.info('[Ready] Bot is ready and all systems initialized');
        }
        catch (error) {
            this.handleError(error, { event: 'ready' });
        }
    }
    /**
     * Initialize scheduled cron jobs
     */
    async initializeScheduledTasks() {
        try {
            // Tax collection cron job
            if (this.isFeatureEnabled('TAX_SYSTEM')) {
                this.initializeTaxCollection();
            }
            // Milestone tracking cron job
            if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
                this.initializeMilestoneTracking();
            }
            // User cleanup cron job
            if (this.isFeatureEnabled('USER_CLEANUP')) {
                this.initializeUserCleanup();
            }
            // Audit cleanup cron job
            if (this.isFeatureEnabled('AUDIT_LOGGING')) {
                this.initializeAuditCleanup();
            }
            this.logger.info('[Ready] Scheduled tasks initialized');
        }
        catch (error) {
            this.logger.error('[Ready] Failed to initialize scheduled tasks', { error });
        }
    }
    /**
     * Initialize tax collection cron job
     */
    initializeTaxCollection() {
        node_cron_1.default.schedule(constants_1.SCHEDULES.TAX_COLLECTION, async () => {
            this.logger.info('[Tax Collection] Running scheduled tax collection check...');
            try {
                // Import here to avoid circular dependencies
                const { processTaxCollection } = await Promise.resolve().then(() => __importStar(require('../services/taxService')));
                // Process tax collection for all guilds the bot is in
                for (const [guildId, guild] of this.app.client.guilds.cache) {
                    try {
                        const result = await processTaxCollection(this.app.client, guildId);
                        if (result.totalProcessed > 0) {
                            this.logger.info(`[Tax Collection] Guild ${guild.name}: Processed ${result.totalProcessed}, Taxed ${result.totalTaxed}, Roles Removed ${result.totalRolesRemoved}`);
                            if (result.errors.length > 0) {
                                this.logger.error(`[Tax Collection] Guild ${guild.name} errors:`, result.errors);
                            }
                        }
                    }
                    catch (error) {
                        this.logger.error(`[Tax Collection] Failed for guild ${guild.name}:`, error);
                    }
                }
            }
            catch (error) {
                this.logger.error('[Tax Collection] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.logger.info('[Tax Collection] Cron job initialized - running every hour');
    }
    /**
     * Initialize milestone tracking cron job
     */
    initializeMilestoneTracking() {
        node_cron_1.default.schedule(constants_1.SCHEDULES.MILESTONE_RESET, async () => {
            this.logger.info('[Milestone Tracking] Running daily milestone check...');
            try {
                // Process login streak tracking for all guilds
                for (const [guildId, guild] of this.app.client.guilds.cache) {
                    try {
                        // This will be handled by the milestone service when users are active
                        // The cron job mainly serves as a daily reset trigger
                        this.logger.debug(`[Milestone Tracking] Daily reset processed for guild ${guild.name}`);
                    }
                    catch (error) {
                        this.logger.error(`[Milestone Tracking] Failed for guild ${guild.name}:`, error);
                    }
                }
            }
            catch (error) {
                this.logger.error('[Milestone Tracking] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.logger.info('[Milestone Tracking] Daily cron job initialized - running at midnight UTC');
    }
    /**
     * Initialize user cleanup cron job
     */
    initializeUserCleanup() {
        node_cron_1.default.schedule(constants_1.SCHEDULES.USER_CLEANUP, async () => {
            this.logger.info('[User Cleanup] Running scheduled cleanup check...');
            try {
                // Import here to avoid circular dependencies
                const { UserCleanupService } = await Promise.resolve().then(() => __importStar(require('../services/userCleanupService')));
                // Perform maintenance cleanup tasks
                const stats = await UserCleanupService.cleanupOrphanedData();
                this.logger.info('[User Cleanup] Orphaned data cleanup completed', stats);
            }
            catch (error) {
                this.logger.error('[User Cleanup] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.logger.info('[User Cleanup] Cron job initialized - running daily at 2 AM UTC');
    }
    /**
     * Initialize audit cleanup cron job
     */
    initializeAuditCleanup() {
        node_cron_1.default.schedule(constants_1.SCHEDULES.AUDIT_CLEANUP, async () => {
            this.logger.info('[Audit Cleanup] Running scheduled audit cleanup...');
            try {
                // Import here to avoid circular dependencies
                const { MilestoneAuditService } = await Promise.resolve().then(() => __importStar(require('../services/milestoneAuditService')));
                // Clean up old audit logs (older than 90 days)
                const cutoffDate = new Date();
                cutoffDate.setDate(cutoffDate.getDate() - 90);
                const cleanupStats = await MilestoneAuditService.cleanupOldLogs(cutoffDate.getTime());
                this.logger.info('[Audit Cleanup] Cleanup completed', cleanupStats);
            }
            catch (error) {
                this.logger.error('[Audit Cleanup] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.logger.info('[Audit Cleanup] Cron job initialized - running weekly on Sunday at 3 AM UTC');
    }
}
exports.ReadyEventHandler = ReadyEventHandler;
//# sourceMappingURL=ready.js.map