import mongoose from 'mongoose';
import { Client } from 'discord.js';
export declare function adjustBalance(discordId: string, amount: number, type: 'give' | 'fine' | 'pay' | 'role_achievement' | 'reaction' | 'tax' | 'starter_balance' | 'content_submission' | 'content_reward' | 'milestone', details?: string, client?: Client, guildId?: string): Promise<void>;
export declare function getLeaderboard(limit?: number): Promise<(mongoose.Document<unknown, {}, import("../models/User").IUser, {}> & import("../models/User").IUser & Required<{
    _id: unknown;
}> & {
    __v: number;
})[]>;
export declare function getTransactionHistory(discordId: string, limit?: number): Promise<(mongoose.Document<unknown, {}, import("../models/Transaction").ITransaction, {}> & import("../models/Transaction").ITransaction & Required<{
    _id: unknown;
}> & {
    __v: number;
})[]>;
export declare function ensureUser(discordId: string): Promise<mongoose.Document<unknown, {}, import("../models/User").IUser, {}> & import("../models/User").IUser & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
//# sourceMappingURL=economyService.d.ts.map