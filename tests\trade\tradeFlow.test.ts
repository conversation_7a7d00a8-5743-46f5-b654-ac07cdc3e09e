/**
 * Trade Flow Integration Tests
 * Tests for complete trade workflows from creation to completion
 */

import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from '@jest/jest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { TradeService } from '../../src/services/trade/TradeService';
import { User, Transaction, Trade, UserTradeStats } from '../../src/models';
import { TRADE } from '../../src/config/constants';

describe('Trade Flow Integration Tests', () => {
  let mongoServer: MongoMemoryServer;
  let tradeService: TradeService;
  let mockApp: any;

  // Test data
  const testGuildId = '123456789012345678';
  const testSellerId = '111111111111111111';
  const testBuyerId = '222222222222222222';
  const testTradeAmount = 1000;
  const testItemDescription = 'Rare Diamond Sword';

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear database
    await User.deleteMany({});
    await Transaction.deleteMany({});
    await Trade.deleteMany({});
    await UserTradeStats.deleteMany({});

    // Create mock app with all required services
    mockApp = {
      logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
        debug: jest.fn()
      },
      getService: jest.fn()
    };

    tradeService = new TradeService(mockApp);
    await tradeService.onInitialize();

    // Create test users with sufficient balances
    await User.create([
      { discordId: testSellerId, balance: 5000 },
      { discordId: testBuyerId, balance: 5000 }
    ]);

    // Create user trade stats
    await UserTradeStats.create([
      {
        discordId: testSellerId,
        guildId: testGuildId,
        totalTrades: 0,
        successfulTrades: 0,
        cancelledTrades: 0,
        expiredTrades: 0,
        disputedTrades: 0,
        tradesAsSeller: 0,
        tradesAsBuyer: 0,
        totalVolumeTraded: 0,
        averageTradeValue: 0,
        largestTrade: 0,
        reputationScore: 50,
        disputeRatio: 0,
        completionRate: 0,
        averageCompletionTime: 0,
        fastestCompletion: 0,
        activeTrades: 0,
        isRestricted: false,
        dailyTradeCount: 0,
        lastTradeDate: new Date(),
        lastResetDate: new Date(),
        lastUpdated: new Date(),
        warningsReceived: 0,
        violationHistory: []
      },
      {
        discordId: testBuyerId,
        guildId: testGuildId,
        totalTrades: 0,
        successfulTrades: 0,
        cancelledTrades: 0,
        expiredTrades: 0,
        disputedTrades: 0,
        tradesAsSeller: 0,
        tradesAsBuyer: 0,
        totalVolumeTraded: 0,
        averageTradeValue: 0,
        largestTrade: 0,
        reputationScore: 50,
        disputeRatio: 0,
        completionRate: 0,
        averageCompletionTime: 0,
        fastestCompletion: 0,
        activeTrades: 0,
        isRestricted: false,
        dailyTradeCount: 0,
        lastTradeDate: new Date(),
        lastResetDate: new Date(),
        lastUpdated: new Date(),
        warningsReceived: 0,
        violationHistory: []
      }
    ]);
  });

  afterEach(async () => {
    await User.deleteMany({});
    await Transaction.deleteMany({});
    await Trade.deleteMany({});
    await UserTradeStats.deleteMany({});
  });

  describe('Complete Trade Flow - Seller Initiated', () => {
    it('should complete a full trade flow from proposal to completion', async () => {
      // Step 1: Create trade proposal
      const trade = await tradeService.createTrade({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: testItemDescription,
        initiatedBy: 'SELLER'
      });

      expect(trade.state).toBe(TRADE.STATES.PROPOSED);
      expect(trade.sellerId).toBe(testSellerId);
      expect(trade.buyerId).toBe(testBuyerId);
      expect(trade.amount).toBe(testTradeAmount);
      expect(trade.escrowLocked).toBe(false);

      // Verify user stats updated
      let sellerStats = await UserTradeStats.findOne({ discordId: testSellerId, guildId: testGuildId });
      let buyerStats = await UserTradeStats.findOne({ discordId: testBuyerId, guildId: testGuildId });
      expect(sellerStats?.dailyTradeCount).toBe(1);
      expect(buyerStats?.dailyTradeCount).toBe(1);

      // Step 2: Buyer accepts trade
      const acceptedTrade = await tradeService.acceptTrade(trade.tradeId, testBuyerId);

      expect(acceptedTrade.state).toBe(TRADE.STATES.ACTIVE);
      expect(acceptedTrade.escrowLocked).toBe(true);
      expect(acceptedTrade.escrowAmount).toBe(testTradeAmount);
      expect(acceptedTrade.acceptedAt).toBeTruthy();

      // Verify buyer balance was deducted
      const buyerAfterAccept = await User.findOne({ discordId: testBuyerId });
      expect(buyerAfterAccept?.balance).toBe(5000 - testTradeAmount);

      // Verify escrow transaction was created
      const escrowTransaction = await Transaction.findOne({ 
        discordId: testBuyerId, 
        type: 'trade_escrow' 
      });
      expect(escrowTransaction).toBeTruthy();
      expect(escrowTransaction?.amount).toBe(-testTradeAmount);

      // Step 3: Seller confirms completion
      const sellerConfirmResult = await tradeService.confirmTrade(trade.tradeId, testSellerId);

      expect(sellerConfirmResult.completed).toBe(false);
      expect(sellerConfirmResult.trade.sellerConfirmed).toBe(true);
      expect(sellerConfirmResult.trade.buyerConfirmed).toBe(false);

      // Step 4: Buyer confirms completion
      const buyerConfirmResult = await tradeService.confirmTrade(trade.tradeId, testBuyerId);

      expect(buyerConfirmResult.completed).toBe(true);
      expect(buyerConfirmResult.trade.sellerConfirmed).toBe(true);
      expect(buyerConfirmResult.trade.buyerConfirmed).toBe(true);
      expect(buyerConfirmResult.trade.state).toBe(TRADE.STATES.COMPLETED);
      expect(buyerConfirmResult.trade.completedAt).toBeTruthy();

      // Verify seller received payment
      const sellerAfterCompletion = await User.findOne({ discordId: testSellerId });
      expect(sellerAfterCompletion?.balance).toBe(5000 + testTradeAmount);

      // Verify release transaction was created
      const releaseTransaction = await Transaction.findOne({ 
        discordId: testSellerId, 
        type: 'trade_release' 
      });
      expect(releaseTransaction).toBeTruthy();
      expect(releaseTransaction?.amount).toBe(testTradeAmount);

      // Verify final trade state
      const finalTrade = await Trade.findOne({ tradeId: trade.tradeId });
      expect(finalTrade?.state).toBe(TRADE.STATES.COMPLETED);
      expect(finalTrade?.escrowLocked).toBe(false);

      // Verify user stats were updated
      sellerStats = await UserTradeStats.findOne({ discordId: testSellerId, guildId: testGuildId });
      buyerStats = await UserTradeStats.findOne({ discordId: testBuyerId, guildId: testGuildId });
      
      expect(sellerStats?.totalTrades).toBe(1);
      expect(sellerStats?.successfulTrades).toBe(1);
      expect(sellerStats?.tradesAsSeller).toBe(1);
      expect(sellerStats?.totalVolumeTraded).toBe(testTradeAmount);
      expect(sellerStats?.activeTrades).toBe(0);

      expect(buyerStats?.totalTrades).toBe(1);
      expect(buyerStats?.successfulTrades).toBe(1);
      expect(buyerStats?.tradesAsBuyer).toBe(1);
      expect(buyerStats?.totalVolumeTraded).toBe(testTradeAmount);
      expect(buyerStats?.activeTrades).toBe(0);
    });
  });

  describe('Trade Cancellation Flow', () => {
    it('should handle trade cancellation with escrow refund', async () => {
      // Create and accept trade
      const trade = await tradeService.createTrade({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: testItemDescription,
        initiatedBy: 'SELLER'
      });

      await tradeService.acceptTrade(trade.tradeId, testBuyerId);

      // Verify escrow is locked
      const activeTradeCheck = await Trade.findOne({ tradeId: trade.tradeId });
      expect(activeTradeCheck?.escrowLocked).toBe(true);

      const buyerBeforeCancel = await User.findOne({ discordId: testBuyerId });
      expect(buyerBeforeCancel?.balance).toBe(5000 - testTradeAmount);

      // Cancel trade
      const cancelledTrade = await tradeService.cancelTrade(
        trade.tradeId, 
        testSellerId, 
        'Changed my mind'
      );

      expect(cancelledTrade.state).toBe(TRADE.STATES.CANCELLED);
      expect(cancelledTrade.escrowLocked).toBe(false);

      // Verify buyer got refund
      const buyerAfterCancel = await User.findOne({ discordId: testBuyerId });
      expect(buyerAfterCancel?.balance).toBe(5000); // Original balance restored

      // Verify refund transaction
      const refundTransaction = await Transaction.findOne({ 
        discordId: testBuyerId, 
        type: 'trade_refund' 
      });
      expect(refundTransaction).toBeTruthy();
      expect(refundTransaction?.amount).toBe(testTradeAmount);

      // Verify user stats
      const sellerStats = await UserTradeStats.findOne({ discordId: testSellerId, guildId: testGuildId });
      const buyerStats = await UserTradeStats.findOne({ discordId: testBuyerId, guildId: testGuildId });
      
      expect(sellerStats?.cancelledTrades).toBe(1);
      expect(buyerStats?.cancelledTrades).toBe(1);
      expect(sellerStats?.activeTrades).toBe(0);
      expect(buyerStats?.activeTrades).toBe(0);
    });
  });

  describe('Trade Expiration Handling', () => {
    it('should handle expired trades correctly', async () => {
      // Create trade with past expiration
      const expiredDate = new Date(Date.now() - 1000); // 1 second ago
      
      const trade = await Trade.create({
        tradeId: 'expired_trade_001',
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: testItemDescription,
        state: TRADE.STATES.PROPOSED,
        initiatedBy: 'SELLER',
        expiresAt: expiredDate,
        escrowLocked: false,
        escrowAmount: 0,
        sellerConfirmed: false,
        buyerConfirmed: false,
        warningsSent: 0,
        extensionGranted: false
      });

      // Try to accept expired trade
      await expect(tradeService.acceptTrade(trade.tradeId, testBuyerId))
        .rejects.toThrow('Trade has expired');

      // Verify trade state unchanged
      const unchangedTrade = await Trade.findOne({ tradeId: trade.tradeId });
      expect(unchangedTrade?.state).toBe(TRADE.STATES.PROPOSED);
    });
  });

  describe('Buyer Initiated Trade Flow', () => {
    it('should complete a buyer-initiated trade', async () => {
      // Buyer creates trade proposal
      const trade = await tradeService.createTrade({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: testItemDescription,
        initiatedBy: 'BUYER'
      });

      expect(trade.initiatedBy).toBe('BUYER');
      expect(trade.state).toBe(TRADE.STATES.PROPOSED);

      // Seller accepts
      const acceptedTrade = await tradeService.acceptTrade(trade.tradeId, testSellerId);

      expect(acceptedTrade.state).toBe(TRADE.STATES.ACTIVE);
      expect(acceptedTrade.escrowLocked).toBe(true);

      // Complete the trade
      await tradeService.confirmTrade(trade.tradeId, testSellerId);
      const result = await tradeService.confirmTrade(trade.tradeId, testBuyerId);

      expect(result.completed).toBe(true);
      expect(result.trade.state).toBe(TRADE.STATES.COMPLETED);

      // Verify final balances
      const seller = await User.findOne({ discordId: testSellerId });
      const buyer = await User.findOne({ discordId: testBuyerId });
      
      expect(seller?.balance).toBe(5000 + testTradeAmount);
      expect(buyer?.balance).toBe(5000 - testTradeAmount);
    });
  });

  describe('Error Handling', () => {
    it('should prevent self-trading', async () => {
      await expect(tradeService.createTrade({
        sellerId: testSellerId,
        buyerId: testSellerId, // Same user
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: testItemDescription,
        initiatedBy: 'SELLER'
      })).rejects.toThrow('You cannot trade with yourself');
    });

    it('should prevent trading with insufficient balance', async () => {
      // Set buyer balance to insufficient amount
      await User.updateOne(
        { discordId: testBuyerId },
        { balance: testTradeAmount - 100 }
      );

      const trade = await tradeService.createTrade({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: testItemDescription,
        initiatedBy: 'SELLER'
      });

      await expect(tradeService.acceptTrade(trade.tradeId, testBuyerId))
        .rejects.toThrow('Insufficient balance');
    });

    it('should prevent double confirmation', async () => {
      const trade = await tradeService.createTrade({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: testItemDescription,
        initiatedBy: 'SELLER'
      });

      await tradeService.acceptTrade(trade.tradeId, testBuyerId);
      await tradeService.confirmTrade(trade.tradeId, testSellerId);

      // Try to confirm again
      await expect(tradeService.confirmTrade(trade.tradeId, testSellerId))
        .rejects.toThrow('You have already confirmed this trade');
    });
  });
});
