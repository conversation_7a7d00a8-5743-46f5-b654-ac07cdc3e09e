import { performance } from 'perf_hooks';

interface MemoryStats {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
  timestamp: number;
}

class MemoryManager {
  private static instance: MemoryManager;
  private memoryStats: MemoryStats[] = [];
  private readonly MAX_MEMORY_STATS = 100; // Keep last 100 memory readings
  private readonly MEMORY_CHECK_INTERVAL = 5 * 60 * 1000; // Check every 5 minutes
  private readonly MAX_HEAP_USAGE_MB = 80; // Alert if heap usage exceeds 80MB
  private memoryCheckTimer?: NodeJS.Timeout;

  private constructor() {
    this.startMemoryMonitoring();
  }

  public static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  /**
   * Start periodic memory monitoring
   */
  private startMemoryMonitoring(): void {
    this.memoryCheckTimer = setInterval(() => {
      this.recordMemoryStats();
      this.checkMemoryUsage();
    }, this.MEMORY_CHECK_INTERVAL);

    console.log('[Memory Manager] Started memory monitoring');
  }

  /**
   * Stop memory monitoring
   */
  public stopMemoryMonitoring(): void {
    if (this.memoryCheckTimer) {
      clearInterval(this.memoryCheckTimer);
      this.memoryCheckTimer = undefined;
    }
    console.log('[Memory Manager] Stopped memory monitoring');
  }

  /**
   * Record current memory statistics
   */
  private recordMemoryStats(): void {
    const memUsage = process.memoryUsage();
    const stats: MemoryStats = {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
      timestamp: Date.now()
    };

    this.memoryStats.push(stats);

    // Keep only the last MAX_MEMORY_STATS readings
    if (this.memoryStats.length > this.MAX_MEMORY_STATS) {
      this.memoryStats = this.memoryStats.slice(-this.MAX_MEMORY_STATS);
    }
  }

  /**
   * Check if memory usage is concerning
   */
  private checkMemoryUsage(): void {
    const current = process.memoryUsage();
    const heapUsedMB = current.heapUsed / 1024 / 1024;
    const rssMB = current.rss / 1024 / 1024;

    if (heapUsedMB > this.MAX_HEAP_USAGE_MB) {
      console.warn(`[Memory Manager] High heap usage detected: ${heapUsedMB.toFixed(2)}MB`);
      this.performEmergencyCleanup();
    }

    // Log memory stats every hour
    if (this.memoryStats.length > 0 && this.memoryStats.length % 12 === 0) {
      console.log(`[Memory Manager] Memory stats - Heap: ${heapUsedMB.toFixed(2)}MB, RSS: ${rssMB.toFixed(2)}MB`);
    }
  }

  /**
   * Perform emergency cleanup when memory usage is high
   */
  private performEmergencyCleanup(): void {
    console.log('[Memory Manager] Performing emergency cleanup');

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    console.log('[Memory Manager] Emergency cleanup completed');
  }



  /**
   * Get current memory statistics
   */
  public getMemoryStats(): {
    current: MemoryStats;
    average: Partial<MemoryStats>;
  } {
    const current = process.memoryUsage();
    const currentStats: MemoryStats = {
      heapUsed: current.heapUsed,
      heapTotal: current.heapTotal,
      external: current.external,
      rss: current.rss,
      timestamp: Date.now()
    };

    // Calculate averages from recent stats
    const recentStats = this.memoryStats.slice(-10); // Last 10 readings
    const average: Partial<MemoryStats> = {};

    if (recentStats.length > 0) {
      average.heapUsed = recentStats.reduce((sum, stat) => sum + stat.heapUsed, 0) / recentStats.length;
      average.heapTotal = recentStats.reduce((sum, stat) => sum + stat.heapTotal, 0) / recentStats.length;
      average.rss = recentStats.reduce((sum, stat) => sum + stat.rss, 0) / recentStats.length;
    }

    return {
      current: currentStats,
      average
    };
  }
}

export default MemoryManager;
