/**
 * Guild Member Update Event Handler
 * Handles Discord guild member update events for starter balance and role change messages
 */
import { GuildMember, PartialGuildMember } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';
/**
 * Guild member update event handler
 */
export declare class GuildMemberUpdateEventHandler extends BaseEventHandler {
    readonly name = "guildMemberUpdate";
    constructor(app: IApplicationContext);
    /**
     * Execute guild member update event
     */
    execute(oldMember: GuildMember | PartialGuildMember, newMember: GuildMember): Promise<void>;
    /**
     * Ensure member is fully fetched
     */
    private ensureFullMember;
    /**
     * Process added roles
     */
    private processAddedRoles;
    /**
     * Process removed roles
     */
    private processRemovedRoles;
    /**
     * Process starter balance for new role
     */
    private processStarterBalance;
    /**
     * Process role add message
     */
    private processRoleAddMessage;
    /**
     * Process role remove message
     */
    private processRoleRemoveMessage;
}
//# sourceMappingURL=guildMemberUpdate.d.ts.map