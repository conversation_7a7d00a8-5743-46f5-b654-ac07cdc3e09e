{"version": 3, "file": "editrole.js", "sourceRoot": "", "sources": ["../../src/commands/editrole.ts"], "names": [], "mappings": ";;AAAA,2CAAmG;AACnG,yCAA6C;AAC7C,wDAA0G;AAC1G,wDAA6E;AAE7E,MAAM,CAAC,OAAO,GAAG;IACb,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC1B,OAAO,CAAC,UAAU,CAAC;SACnB,cAAc,CAAC,sCAAsC,CAAC;SACtD,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACrG,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,sCAAsC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC3H,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC3H,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACzH,2BAA2B,CAAC,gCAAmB,CAAC,aAAa,CAAC;IACnE,OAAO,EAAE,IAAA,+BAAgB,EAAC,KAAK,EAAE,WAAwC,EAAE,EAAE;QACzE,mBAAmB;QACnB,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,gCAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,8BAAe,EAAE,CAAC;QAChC,CAAC;QAED,MAAM,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACtD,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEpE,aAAa;QACb,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,8BAAe,CAAC,kCAAkC,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACrB,MAAM,IAAI,8BAAe,CAAC,4CAA4C,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC;YACD,iCAAiC;YACjC,MAAM,cAAc,GAAG,MAAM,IAAA,0BAAW,EAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YACvE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,cAAc,CAAC;YAExD,4BAA4B;YAC5B,IAAA,sCAAuB,EAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAEjD,sBAAsB;YACtB,MAAM,MAAM,GAAwB,EAAE,CAAC;YACvC,IAAI,OAAO;gBAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC;YACnC,IAAI,QAAQ,KAAK,IAAI;gBAAE,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC;YAC/C,IAAI,cAAc,KAAK,IAAI;gBAAE,MAAM,CAAC,WAAW,GAAG,cAAc,CAAC;YAEjE,iCAAiC;YACjC,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,8BAAe,CAAC,oEAAoE,CAAC,CAAC;YACpG,CAAC;YAED,0BAA0B;YAC1B,MAAM,OAAO,GAAG,MAAM,kBAAW,CAAC,gBAAgB,CAC9C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EACnB,MAAM,EACN,EAAE,GAAG,EAAE,IAAI,EAAE,CAChB,CAAC;YAEF,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,MAAM,IAAI,8BAAe,CAAC,qBAAqB,IAAI,CAAC,IAAI,cAAc,CAAC,CAAC;YAC5E,CAAC;YAED,8CAA8C;YAC9C,IAAI,cAAc,GAAG,sBAAsB,OAAO,CAAC,IAAI,6BAA6B,CAAC;YAErF,IAAI,UAAU,KAAK,YAAY,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;gBAClD,cAAc,IAAI,sBAAsB,SAAS,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC;YAC5E,CAAC;YAED,MAAM,WAAW,CAAC,KAAK,CAAC;gBACpB,OAAO,EAAE,cAAc;gBACvB,SAAS,EAAE,KAAK;aACnB,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACtB,IAAI,KAAK,YAAY,8BAAe,EAAE,CAAC;gBACnC,MAAM,KAAK,CAAC;YAChB,CAAC;YACD,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBACzB,MAAM,IAAI,4BAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,4BAAa,CAAC,oCAAoC,CAAC,CAAC;YAClE,CAAC;QACL,CAAC;IACL,CAAC,CAAC;CACL,CAAC"}