import { IIncomeGuide } from '../models/IncomeGuide';
/**
 * Get the income guide text for a guild
 * @param guildId Discord guild ID
 * @returns Custom income guide text or default if none set
 */
export declare function getIncomeGuideText(guildId: string): Promise<string>;
/**
 * Set custom income guide text for a guild
 * @param guildId Discord guild ID
 * @param customText Custom income guide text
 * @returns Updated income guide document
 */
export declare function setIncomeGuideText(guildId: string, customText: string): Promise<IIncomeGuide>;
/**
 * Check if a guild has custom income guide text
 * @param guildId Discord guild ID
 * @returns True if custom text exists, false otherwise
 */
export declare function hasCustomIncomeGuide(guildId: string): Promise<boolean>;
/**
 * Remove custom income guide text for a guild (revert to default)
 * @param guildId Discord guild ID
 * @returns True if removed, false if none existed
 */
export declare function removeCustomIncomeGuide(guildId: string): Promise<boolean>;
/**
 * Get the default income guide text
 * @returns Default income guide text
 */
export declare function getDefaultIncomeGuideText(): string;
//# sourceMappingURL=incomeGuideService.d.ts.map