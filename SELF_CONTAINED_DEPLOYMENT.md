# Self-Contained Deployment Guide

This document explains the automated build and deployment system that makes your Discord bot fully self-contained for hosting platforms like Discloud.

## Overview

The bot now automatically handles all build and deployment tasks when it starts up, eliminating the need for manual Node.js commands. This includes:

- ✅ Automatic TypeScript compilation
- ✅ Automatic slash command deployment to Discord
- ✅ Automatic role command deployment
- ✅ Environment validation
- ✅ Dependency checking
- ✅ Comprehensive error handling

## How It Works

### 1. Startup Process

When the bot starts (`npm start`), it now follows this sequence:

1. **Environment Validation** - Checks for required environment variables
2. **Dependency Check** - Verifies all required dependencies are available
3. **Build Detection** - Determines if TypeScript compilation is needed
4. **Automatic Build** - Compiles TypeScript to JavaScript if needed
5. **Command Deployment** - Deploys all slash commands to Discord
6. **Bot Initialization** - Starts the Discord bot normally

### 2. Key Files

- **`src/utils/auto-deployment.ts`** - Core automation utilities
- **`src/main.ts`** - Modified startup process with auto-deployment
- **`scripts/test-auto-deployment.ts`** - Test suite for validation
- **`package.json`** - Updated scripts for self-contained operation
- **`discloud.config`** - Updated Discloud configuration

### 3. Environment Detection

The system automatically detects production environments using:

- `NODE_ENV=production`
- `DISCLOUD_ENVIRONMENT=true`
- `DISCLOUD=true`

In production, the system is more strict about failures, while in development it's more forgiving.

## Required Environment Variables

Ensure these environment variables are set in your hosting platform:

```env
BOT_TOKEN=your_discord_bot_token
CLIENT_ID=your_discord_application_id
MONGODB_URI=your_mongodb_connection_string
```

## Deployment Instructions

### For Discloud

1. **Upload your source code** (including the `src/` directory)
2. **Set environment variables** in Discloud dashboard
3. **Deploy** - The bot will automatically build and deploy commands

The `discloud.config` is already configured to use the new startup process.

### For Other Platforms

1. **Set environment variables** as required
2. **Use `npm start`** as the startup command
3. **Ensure Node.js and npm are available** on the platform

## Testing the Deployment

### Basic Test (Safe)
```bash
npm run test:auto-deploy
```

This tests environment validation, dependency checking, and detection logic without making changes.

### Full Test (Caution)
```bash
npm run test:auto-deploy:full
```

This performs a complete test including build and command deployment. **Use with caution** as it will:
- Rebuild the project
- Deploy commands to Discord

## Manual Override

If you need to disable auto-deployment for any reason:

1. **Skip build**: Set `SKIP_AUTO_BUILD=true`
2. **Skip command deployment**: Set `SKIP_AUTO_DEPLOY=true`
3. **Use compiled version**: Use `npm run start:compiled` instead of `npm start`

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check TypeScript compiler is installed: `npm install typescript`
   - Verify source files exist in `src/` directory
   - Check for TypeScript compilation errors

2. **Command Deployment Failures**
   - Verify `BOT_TOKEN` and `CLIENT_ID` are correct
   - Check bot has `applications.commands` scope
   - Ensure bot is added to your Discord server

3. **Environment Variable Issues**
   - Double-check all required variables are set
   - Verify no typos in variable names
   - Check hosting platform's environment variable interface

### Debug Mode

For detailed logging, set:
```env
DEBUG=true
LOG_LEVEL=debug
```

### Error Codes

- **Build Error**: TypeScript compilation failed
- **Deploy Error 50001**: Missing access permissions
- **Deploy Error 50013**: Missing bot permissions
- **Deploy Error 401**: Invalid bot token
- **Deploy Error 429**: Rate limited

## Benefits

### ✅ Advantages

- **No manual intervention required** - Just upload and deploy
- **Consistent deployments** - Same process every time
- **Error handling** - Graceful failure with detailed logging
- **Environment awareness** - Different behavior for dev/prod
- **Validation** - Checks everything before starting

### ⚠️ Considerations

- **Startup time** - Initial deployment takes longer due to build process
- **Resource usage** - TypeScript compilation requires memory
- **Network calls** - Command deployment requires Discord API access

## Migration from Manual Process

If you were previously using manual commands:

### Old Process
```bash
npm run build
npm run deploy-commands
npm start
```

### New Process
```bash
npm start
```

That's it! Everything is now automated.

## Advanced Configuration

### Custom Build Process

To customize the build process, modify `src/utils/auto-deployment.ts`:

```typescript
export async function runBuild(): Promise<boolean> {
  // Your custom build logic here
}
```

### Custom Command Deployment

To modify command deployment, update the `deployCommands` function in the same file.

### Environment-Specific Behavior

The system automatically adjusts behavior based on environment:

- **Development**: More forgiving, continues on non-critical failures
- **Production**: Strict, fails fast on critical issues

## Support

If you encounter issues with the self-contained deployment:

1. Run the test suite: `npm run test:auto-deploy`
2. Check the logs for detailed error messages
3. Verify all environment variables are set correctly
4. Ensure your hosting platform supports Node.js and npm

The system is designed to be robust and provide clear error messages to help diagnose any issues.
