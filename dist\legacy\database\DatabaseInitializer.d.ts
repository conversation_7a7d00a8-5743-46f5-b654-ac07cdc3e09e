/**
 * Legacy Database Initializer
 * Extracted from monolithic index.ts for backward compatibility
 */
/**
 * Database initialization and cleanup
 */
export declare class LegacyDatabaseInitializer {
    /**
     * Initialize database connection and perform cleanup
     */
    static initialize(): Promise<void>;
    /**
     * Database cleanup and initialization
     */
    private static initializeDatabase;
    /**
     * Get database connection status
     */
    static getConnectionStatus(): string;
    /**
     * Close database connection
     */
    static close(): Promise<void>;
}
export default LegacyDatabaseInitializer;
//# sourceMappingURL=DatabaseInitializer.d.ts.map