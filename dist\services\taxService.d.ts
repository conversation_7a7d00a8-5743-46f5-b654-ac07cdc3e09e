import { Client } from 'discord.js';
interface TaxCollectionResult {
    totalProcessed: number;
    totalTaxed: number;
    totalRolesRemoved: number;
    errors: string[];
}
/**
 * Processes tax collection for a specific guild
 */
export declare function processTaxCollection(client: Client, guildId: string): Promise<TaxCollectionResult>;
/**
 * Gets the next scheduled tax collection date for a guild
 */
export declare function getNextTaxDate(guildId: string): Promise<Date | null>;
/**
 * Checks if tax collection is enabled for a guild
 */
export declare function isTaxEnabled(guildId: string): Promise<boolean>;
export {};
//# sourceMappingURL=taxService.d.ts.map