{"version": 3, "file": "richestrole.js", "sourceRoot": "", "sources": ["../../src/commands/richestrole.ts"], "names": [], "mappings": ";;;;;AAAA,2CAAsH;AACtH,wDAA0G;AAC1G,wDAAoH;AACpH,wDAAgE;AAChE,0DAAkC;AAElC,MAAM,CAAC,OAAO,GAAG;IACb,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC1B,OAAO,CAAC,aAAa,CAAC;SACtB,cAAc,CAAC,sFAAsF,CAAC;SACtG,aAAa,CAAC,MAAM,CAAC,EAAE,CACpB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;SACjB,cAAc,CAAC,0CAA0C,CAAC;SAC1D,WAAW,CAAC,IAAI,CAAC,CACzB;SACA,2BAA2B,CAAC,gCAAmB,CAAC,aAAa,CAAC;IAEnE,OAAO,EAAE,IAAA,+BAAgB,EAAC,KAAK,EAAE,WAAwC,EAAE,EAAE;QACzE,oBAAoB;QACpB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACrB,MAAM,IAAI,8BAAe,CAAC,4CAA4C,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,gCAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,8BAAe,CAAC,yDAAyD,CAAC,CAAC;QACzF,CAAC;QAED,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAS,CAAC;QAE/D,IAAI,CAAC;YACD,4BAA4B;YAC5B,IAAA,sCAAuB,EAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAEjD,yCAAyC;YACzC,MAAM,WAAW,GAAG,MAAM,cAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAExE,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;gBAC3C,MAAM,KAAK,GAAG,IAAA,+BAAgB,EAAC,mBAAmB,CAAC;qBAC9C,cAAc,CAAC,GAAG,qBAAM,CAAC,KAAK,CAAC,OAAO,mFAAmF,CAAC;qBAC1H,SAAS,CAAC;oBACP,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,SAAS,aAAa;oBAC3C,KAAK,EAAE,wEAAwE;oBAC/E,MAAM,EAAE,KAAK;iBAChB,CAAC,CAAC;gBAEP,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC/D,OAAO;YACX,CAAC;YAED,mDAAmD;YACnD,IAAI,aAA0B,CAAC;YAC/B,IAAI,CAAC;gBACD,aAAa,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACjF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,8DAA8D;gBAC9D,MAAM,QAAQ,GAAG,MAAM,cAAI,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;gBAChF,IAAI,WAAW,GAAuB,IAAI,CAAC;gBAC3C,IAAI,SAAS,GAAG,IAAI,CAAC;gBAErB,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;oBAC1B,IAAI,CAAC;wBACD,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACpE,SAAS,GAAG,IAAI,CAAC;wBACjB,MAAM;oBACV,CAAC;oBAAC,MAAM,CAAC;wBACL,uCAAuC;wBACvC,SAAS;oBACb,CAAC;gBACL,CAAC;gBAED,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC7B,MAAM,KAAK,GAAG,IAAA,+BAAgB,EAAC,qBAAqB,CAAC;yBAChD,cAAc,CAAC,GAAG,qBAAM,CAAC,KAAK,CAAC,OAAO,sFAAsF,CAAC;yBAC7H,SAAS,CAAC;wBACP,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,IAAI,kBAAkB;wBAC5C,KAAK,EAAE,SAAS,QAAQ,CAAC,MAAM,0DAA0D;wBACzF,MAAM,EAAE,KAAK;qBAChB,CAAC,CAAC;oBAEP,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;oBAC/D,OAAO;gBACX,CAAC;gBAED,aAAa,GAAG,WAAW,CAAC;gBAC5B,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;gBAC5C,WAAW,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAC5C,CAAC;YAED,2BAA2B;YAC3B,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC;YACpC,MAAM,mBAAmB,GAAG,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,yBAAyB;YAEnH,uCAAuC;YACvC,MAAM,cAAc,GAAG,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/E,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAElC,kCAAkC;YAClC,MAAM,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEpC,0BAA0B;YAC1B,MAAM,KAAK,GAAG,IAAA,iCAAkB,EAAC,uBAAuB,CAAC;iBACpD,cAAc,CAAC,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,8BAA8B,IAAI,CAAC,IAAI,iDAAiD,CAAC;iBAC/H,SAAS,CACN;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,kBAAkB;gBAC/C,KAAK,EAAE,KAAK,aAAa,CAAC,WAAW,gBAAgB,IAAA,0BAAW,EAAC,WAAW,CAAC,OAAO,CAAC,EAAE;gBACvF,MAAM,EAAE,IAAI;aACf,EACD;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,QAAQ,mBAAmB;gBACjD,KAAK,EAAE,aAAa,IAAI,CAAC,IAAI,2BAA2B,cAAc,CAAC,IAAI,+BAA+B;gBAC1G,MAAM,EAAE,IAAI;aACf,CACJ,CAAC;YAEN,mCAAmC;YACnC,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,MAAM,GAAG,CAAC;oBACtD,CAAC,CAAC,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,cAAc,CAAC,IAAI,GAAG,CAAC,OAAO;oBACzE,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAErC,KAAK,CAAC,SAAS,CAAC;oBACZ,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,IAAI,mBAAmB;oBAC7C,KAAK,EAAE,mBAAmB,IAAI,MAAM;oBACpC,MAAM,EAAE,KAAK;iBAChB,CAAC,CAAC;YACP,CAAC;YAED,qBAAqB;YACrB,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAElE,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QAEnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,KAAK,YAAY,8BAAe,IAAI,KAAK,YAAY,8BAAe,EAAE,CAAC;gBACvE,MAAM,KAAK,CAAC;YAChB,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBACzB,MAAM,IAAI,4BAAa,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/E,CAAC;YAED,MAAM,IAAI,4BAAa,CAAC,gEAAgE,CAAC,CAAC;QAC9F,CAAC;IACL,CAAC,CAAC;CACL,CAAC"}