#!/usr/bin/env ts-node

/**
 * Test Command Integration
 * Verifies that trade commands are properly integrated
 */

import { CommandManager } from '../src/commands/CommandManager';
import { createLogger } from '../src/core/logger';
import { featureManager } from '../src/config/features';

const logger = createLogger('command-integration-test');

/**
 * Mock application context for testing
 */
class MockApplicationContext {
  private services = new Map<string, any>();

  registerService(service: any, name: string) {
    this.services.set(name, service);
  }

  getService(name: string) {
    const service = this.services.get(name);
    if (!service) {
      throw new Error(`Service ${name} not found`);
    }
    return service;
  }
}

/**
 * Mock services for testing
 */
class MockTradeService {
  constructor() {}
  async initialize() {}
}

class MockDisputeService {
  constructor() {}
  async initialize() {}
}

class MockTradeBackgroundService {
  constructor() {}
  async initialize() {}
}

/**
 * Test command integration
 */
async function testCommandIntegration() {
  try {
    console.log('🧪 Testing Trade Command Integration...\n');

    // 1. Test feature flag
    console.log('1. Testing Feature Flags:');
    const tradeSystemEnabled = featureManager.isEnabled('TRADE_SYSTEM');
    console.log(`   ✅ TRADE_SYSTEM enabled: ${tradeSystemEnabled}`);
    
    if (!tradeSystemEnabled) {
      console.log('   ❌ TRADE_SYSTEM feature is disabled!');
      return false;
    }

    // 2. Test command manager initialization
    console.log('\n2. Testing CommandManager:');
    const commandManager = new CommandManager();
    console.log('   ✅ CommandManager created');

    // 3. Test mock application context
    console.log('\n3. Testing Service Injection:');
    const mockApp = new MockApplicationContext();
    
    // Register mock services
    mockApp.registerService(new MockTradeService(), 'TradeService');
    mockApp.registerService(new MockDisputeService(), 'DisputeService');
    mockApp.registerService(new MockTradeBackgroundService(), 'TradeBackgroundService');
    
    commandManager.setApplicationContext(mockApp);
    console.log('   ✅ Application context set');

    // 4. Test command loading
    console.log('\n4. Testing Command Loading:');
    const stats = await commandManager.loadCommands();
    console.log(`   ✅ Commands loaded: ${stats.totalLoaded}`);
    console.log(`   ✅ New architecture: ${stats.newArchitecture}`);
    console.log(`   ✅ Failed loads: ${stats.failedLoads}`);

    // 5. Test command registration
    console.log('\n5. Testing Command Registration:');
    const discordCommands = commandManager.getDiscordCommands();
    
    const expectedCommands = ['trade', 'tradeadmin', 'trademonitor'];
    const foundCommands: string[] = [];
    
    for (const [name, command] of discordCommands) {
      if (expectedCommands.includes(name)) {
        foundCommands.push(name);
        console.log(`   ✅ Found command: /${name}`);
        
        // Test command metadata
        const metadata = command.getMetadata();
        console.log(`      - Category: ${metadata.category}`);
        console.log(`      - Admin Only: ${metadata.adminOnly}`);
        console.log(`      - Required Features: ${metadata.requiredFeatures.join(', ')}`);
      }
    }

    // 6. Check for missing commands
    console.log('\n6. Checking for Missing Commands:');
    const missingCommands = expectedCommands.filter(cmd => !foundCommands.includes(cmd));
    
    if (missingCommands.length > 0) {
      console.log(`   ❌ Missing commands: ${missingCommands.join(', ')}`);
      return false;
    } else {
      console.log('   ✅ All trade commands found');
    }

    // 7. Test command data structure
    console.log('\n7. Testing Command Data Structure:');
    for (const commandName of foundCommands) {
      const command = discordCommands.get(commandName);
      if (command && command.data) {
        console.log(`   ✅ /${commandName} has valid data structure`);
        console.log(`      - Name: ${command.data.name}`);
        console.log(`      - Description: ${command.data.description}`);
        
        // Check for subcommands (trade command should have them)
        if (commandName === 'trade' && command.data.options) {
          const subcommands = command.data.options.filter((opt: any) => opt.type === 1); // SUB_COMMAND
          console.log(`      - Subcommands: ${subcommands.length}`);
          subcommands.forEach((sub: any) => {
            console.log(`        • ${sub.name}: ${sub.description}`);
          });
        }
      } else {
        console.log(`   ❌ /${commandName} missing data structure`);
        return false;
      }
    }

    console.log('\n🎉 All integration tests passed!');
    console.log('\n📋 Summary:');
    console.log(`   • Feature flags: ✅ Working`);
    console.log(`   • Service injection: ✅ Working`);
    console.log(`   • Command loading: ✅ Working`);
    console.log(`   • Command registration: ✅ Working`);
    console.log(`   • Data structures: ✅ Working`);
    
    return true;

  } catch (error) {
    console.error('\n❌ Integration test failed:', error);
    return false;
  }
}

/**
 * Test command deployment readiness
 */
async function testDeploymentReadiness() {
  console.log('\n🚀 Testing Deployment Readiness...\n');

  try {
    // Test that commands can be serialized for Discord API
    const commandManager = new CommandManager();
    const mockApp = new MockApplicationContext();
    
    mockApp.registerService(new MockTradeService(), 'TradeService');
    mockApp.registerService(new MockDisputeService(), 'DisputeService');
    mockApp.registerService(new MockTradeBackgroundService(), 'TradeBackgroundService');
    
    commandManager.setApplicationContext(mockApp);
    await commandManager.loadCommands();
    
    const discordCommands = commandManager.getDiscordCommands();
    const deployableCommands: any[] = [];
    
    console.log('1. Testing Command Serialization:');
    for (const [name, command] of discordCommands) {
      if (['trade', 'tradeadmin', 'trademonitor'].includes(name)) {
        try {
          const serialized = command.data.toJSON();
          deployableCommands.push(serialized);
          console.log(`   ✅ /${name} serializes correctly`);
        } catch (error) {
          console.log(`   ❌ /${name} serialization failed:`, error);
          return false;
        }
      }
    }

    console.log('\n2. Testing Permission Structure:');
    for (const cmd of deployableCommands) {
      console.log(`   • /${cmd.name}:`);
      console.log(`     - Default permissions: ${cmd.default_member_permissions || 'None'}`);
      console.log(`     - Guild only: ${cmd.dm_permission === false ? 'Yes' : 'No'}`);
    }

    console.log('\n✅ Commands are ready for deployment!');
    return true;

  } catch (error) {
    console.error('\n❌ Deployment readiness test failed:', error);
    return false;
  }
}

/**
 * Main test runner
 */
async function main() {
  console.log('🔧 Trade System Command Integration Test\n');
  console.log('=' .repeat(50));

  const integrationPassed = await testCommandIntegration();
  
  if (integrationPassed) {
    const deploymentPassed = await testDeploymentReadiness();
    
    if (deploymentPassed) {
      console.log('\n🎉 ALL TESTS PASSED! Trade commands are ready for deployment.');
      process.exit(0);
    }
  }

  console.log('\n❌ TESTS FAILED! Please fix the issues before deployment.');
  process.exit(1);
}

// Handle errors
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled rejection:', reason);
  process.exit(1);
});

// Run tests
main().catch((error) => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
