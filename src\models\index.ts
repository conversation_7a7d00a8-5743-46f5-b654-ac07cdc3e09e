/**
 * Models Index
 * Centralized exports for all database models
 */

// Core Models
export { default as User, IUser } from './User';
export { default as Transaction, ITransaction } from './Transaction';
export { default as UserActivity, IUserActivity } from './UserActivity';
export { default as StarterBalance, IStarterBalance } from './StarterBalance';

// Trade System Models
export { default as Trade, ITrade } from './Trade';
export { default as EscrowTransaction, IEscrowTransaction } from './EscrowTransaction';
export { default as DisputeCase, IDisputeCase } from './DisputeCase';
export { default as TradeConfirmation, ITradeConfirmation } from './TradeConfirmation';
export { default as UserTradeStats, IUserTradeStats } from './UserTradeStats';

// Dynasty Models (if they exist)
// export { default as Dynasty, IDynasty } from './Dynasty';
// export { default as DynastyTransaction, IDynastyTransaction } from './DynastyTransaction';

// Milestone Models (if they exist)
// export { default as MilestoneConfiguration, IMilestoneConfiguration } from './MilestoneConfiguration';
// export { default as UserMilestone, IUserMilestone } from './UserMilestone';

// Role Models (if they exist)
// export { default as RoleForSale, IRoleForSale } from './RoleForSale';
// export { default as RolePrefix, IRolePrefix } from './RolePrefix';

/**
 * Trade System Type Definitions
 */
export type TradeState = 'PROPOSED' | 'ACCEPTED' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'EXPIRED' | 'DISPUTED';
export type EscrowTransactionType = 'LOCK' | 'RELEASE' | 'REFUND' | 'DISPUTE_HOLD';
export type DisputeStatus = 'OPEN' | 'EVIDENCE_COLLECTION' | 'UNDER_REVIEW' | 'RESOLVED' | 'APPEALED' | 'CLOSED';
export type DisputeResolution = 'FAVOR_INITIATOR' | 'FAVOR_RESPONDENT' | 'SPLIT_ESCROW' | 'FULL_REFUND' | 'CUSTOM';
export type ConfirmationType = 'TRADE_ACCEPTANCE' | 'ITEM_RECEIVED' | 'TRADE_COMPLETION' | 'DISPUTE_ACKNOWLEDGMENT';

/**
 * Transaction Type Definitions
 */
export type TransactionType = 
  | 'pay' 
  | 'role_achievement' 
  | 'give' 
  | 'fine' 
  | 'reaction' 
  | 'tax' 
  | 'starter_balance' 
  | 'content_submission' 
  | 'content_reward' 
  | 'milestone'
  | 'trade_escrow'
  | 'trade_release'
  | 'trade_refund';

/**
 * Model Initialization
 * Call this function to ensure all models are properly registered
 */
export async function initializeModels(): Promise<void> {
  // Import all models to ensure they are registered with Mongoose
  await Promise.all([
    import('./User'),
    import('./Transaction'),
    import('./UserActivity'),
    import('./StarterBalance'),
    import('./Trade'),
    import('./EscrowTransaction'),
    import('./DisputeCase'),
    import('./TradeConfirmation'),
    import('./UserTradeStats'),
  ]);
}
