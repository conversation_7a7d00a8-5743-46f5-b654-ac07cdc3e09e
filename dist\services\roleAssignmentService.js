"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkAndAssignRoles = checkAndAssignRoles;
exports.sendRoleAchievementNotifications = sendRoleAchievementNotifications;
exports.getUserAchievementRoles = getUserAchievementRoles;
const User_1 = require("../models/User");
const economyService_1 = require("./economyService");
const embedBuilder_1 = require("../utils/embedBuilder");
const errorHandler_1 = require("../utils/errorHandler");
/**
 * Checks if a user qualifies for any new role achievements and assigns them automatically
 * @param client Discord client instance
 * @param discordId User's Discord ID
 * @param guildId Guild ID where roles should be assigned
 * @param newBalance User's new PLC balance
 * @returns Promise<RoleAssignmentResult | null> - Result of role assignments or null if no roles assigned
 */
async function checkAndAssignRoles(client, discordId, guildId, newBalance) {
    try {
        // Get the guild
        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            console.warn(`Guild ${guildId} not found for role assignment`);
            return null;
        }
        // Get the member
        let member;
        try {
            member = await guild.members.fetch(discordId);
        }
        catch (error) {
            console.warn(`Member ${discordId} not found in guild ${guildId}`);
            return null;
        }
        // Get all available role achievements sorted by price (lowest first)
        const availableRoles = await User_1.RoleForSale.find().sort({ price: 1 });
        if (!availableRoles.length) {
            return null;
        }
        // Find roles the user qualifies for but doesn't have yet
        const rolesToAssign = [];
        for (const roleForSale of availableRoles) {
            // Check if user's balance meets the requirement
            if (newBalance >= roleForSale.price) {
                // Check if user doesn't already have this role
                if (!member.roles.cache.has(roleForSale.roleId)) {
                    // Verify the role exists in the guild
                    const discordRole = guild.roles.cache.get(roleForSale.roleId);
                    if (discordRole) {
                        rolesToAssign.push({
                            roleId: roleForSale.roleId,
                            roleName: roleForSale.name,
                            price: roleForSale.price,
                            description: roleForSale.description,
                            discordRole: discordRole
                        });
                    }
                    else {
                        console.warn(`Role ${roleForSale.roleId} (${roleForSale.name}) not found in guild ${guildId}`);
                    }
                }
            }
        }
        // If no roles to assign, return null
        if (rolesToAssign.length === 0) {
            return null;
        }
        // Assign all qualifying roles
        const assignedRoles = [];
        for (const roleInfo of rolesToAssign) {
            try {
                await member.roles.add(roleInfo.discordRole);
                // Record the achievement unlock
                await (0, economyService_1.adjustBalance)(discordId, 0, 'role_achievement', `Achievement unlocked: ${roleInfo.roleName} (Required: ${roleInfo.price} PLC)`);
                assignedRoles.push({
                    roleId: roleInfo.roleId,
                    roleName: roleInfo.roleName,
                    price: roleInfo.price,
                    description: roleInfo.description
                });
                console.log(`Assigned role ${roleInfo.roleName} to user ${discordId} in guild ${guildId}`);
            }
            catch (error) {
                console.error(`Failed to assign role ${roleInfo.roleName} to user ${discordId}:`, error);
            }
        }
        // Return result if any roles were assigned
        if (assignedRoles.length > 0) {
            return {
                rolesAssigned: assignedRoles,
                member: member,
                newBalance: newBalance
            };
        }
        return null;
    }
    catch (error) {
        console.error('Error in checkAndAssignRoles:', error);
        throw new errorHandler_1.DatabaseError('Failed to check and assign roles');
    }
}
/**
 * Sends congratulatory messages to users when they unlock role achievements
 * @param result Role assignment result
 * @param client Discord client instance
 */
async function sendRoleAchievementNotifications(result, client) {
    try {
        const { rolesAssigned, member, newBalance } = result;
        // Create different messages based on number of roles assigned
        if (rolesAssigned.length === 1) {
            // Single role achievement
            const role = rolesAssigned[0];
            const embed = (0, embedBuilder_1.createSuccessEmbed)('🎉 Achievement Unlocked!')
                .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Congratulations!**\n\n` +
                `You have automatically unlocked the **${role.roleName}** role achievement!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} Achievement Unlocked`,
                value: `**${role.roleName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Required Balance`,
                value: (0, embedBuilder_1.formatCoins)(role.price),
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.MONEY} Your Balance`,
                value: (0, embedBuilder_1.formatCoins)(newBalance),
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Achievement Details`,
                value: role.description || 'Congratulations on your achievement!',
                inline: false
            })
                .setFooter({
                text: 'Role achievements are automatically unlocked when you reach the required PLC balance!'
            });
            (0, embedBuilder_1.addUserInfo)(embed, member.user);
            // Send DM to user
            try {
                await member.send({ embeds: [embed] });
            }
            catch (error) {
                console.warn(`Could not send DM to user ${member.id}:`, error);
            }
        }
        else {
            // Multiple role achievements
            const embed = (0, embedBuilder_1.createSuccessEmbed)('🎉 Multiple Achievements Unlocked!')
                .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Congratulations!**\n\n` +
                `You have automatically unlocked **${rolesAssigned.length}** role achievements!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ECONOMY.MONEY} Your Balance`,
                value: (0, embedBuilder_1.formatCoins)(newBalance),
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} Achievements Unlocked`,
                value: rolesAssigned.map(role => `• **${role.roleName}** (${(0, embedBuilder_1.formatCoins)(role.price)})`).join('\n'),
                inline: false
            })
                .setFooter({
                text: 'Role achievements are automatically unlocked when you reach the required PLC balance!'
            });
            (0, embedBuilder_1.addUserInfo)(embed, member.user);
            // Send DM to user
            try {
                await member.send({ embeds: [embed] });
            }
            catch (error) {
                console.warn(`Could not send DM to user ${member.id}:`, error);
            }
        }
    }
    catch (error) {
        console.error('Error sending role achievement notifications:', error);
    }
}
/**
 * Gets all roles a user currently has that are achievements
 * @param member Guild member
 * @returns Array of role achievement info
 */
async function getUserAchievementRoles(member) {
    try {
        const availableRoles = await User_1.RoleForSale.find();
        const userAchievements = [];
        for (const roleForSale of availableRoles) {
            if (member.roles.cache.has(roleForSale.roleId)) {
                userAchievements.push({
                    roleId: roleForSale.roleId,
                    roleName: roleForSale.name,
                    price: roleForSale.price,
                    description: roleForSale.description
                });
            }
        }
        // Sort by price (lowest to highest)
        return userAchievements.sort((a, b) => a.price - b.price);
    }
    catch (error) {
        console.error('Error getting user achievement roles:', error);
        return [];
    }
}
//# sourceMappingURL=roleAssignmentService.js.map