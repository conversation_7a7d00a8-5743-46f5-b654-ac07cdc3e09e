import { Slash<PERSON>ommandB<PERSON>er, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { withError<PERSON>and<PERSON>, ValidationError, PermissionError } from '../utils/errorHandler';
import { createAdminEmbed, createSuc<PERSON>Embed, createErrorEmbed, EMOJIS } from '../utils/embedBuilder';
import { UserCleanupService } from '../services/userCleanupService';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('testcleanup')
        .setDescription('Test user data cleanup functionality (admin only)')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('User to check cleanup data for')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('action')
                .setDescription('Action to perform')
                .setRequired(true)
                .addChoices(
                    { name: 'Check Data', value: 'check' },
                    { name: 'Simulate Cleanup', value: 'simulate' }
                ))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Check permissions
        if (!interaction.guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError('You need Administrator permissions to use this command.');
        }

        const targetUser = interaction.options.getUser('user', true);
        const action = interaction.options.getString('action', true);

        try {
            if (action === 'check') {
                // Check what data exists for the user
                const userData = await UserCleanupService.checkUserData(targetUser.id);
                
                const embed = createAdminEmbed('User Data Check')
                    .setDescription(
                        `${EMOJIS.ADMIN.INFO} **Data Check for ${targetUser.username}**\n\n` +
                        `Checking what data would be cleaned up if this user left the server.`
                    )
                    .addFields(
                        {
                            name: `${EMOJIS.ECONOMY.COINS} User Balance Record`,
                            value: userData.hasUserRecord ? '✅ Found' : '❌ None',
                            inline: true
                        },
                        {
                            name: `${EMOJIS.ADMIN.LIST} Transaction History`,
                            value: userData.transactionCount > 0 ? `✅ ${userData.transactionCount} records` : '❌ None',
                            inline: true
                        },
                        {
                            name: `${EMOJIS.SUCCESS.PARTY} Reaction Rewards`,
                            value: userData.reactionRewardCount > 0 ? `✅ ${userData.reactionRewardCount} records` : '❌ None',
                            inline: true
                        }
                    );

                const totalRecords = (userData.hasUserRecord ? 1 : 0) + userData.transactionCount + userData.reactionRewardCount;
                
                if (totalRecords > 0) {
                    embed.addFields({
                        name: `${EMOJIS.ADMIN.WARNING} Cleanup Impact`,
                        value: `If this user leaves the server, **${totalRecords} database records** would be automatically removed after a 5-second grace period.`,
                        inline: false
                    });
                } else {
                    embed.addFields({
                        name: `${EMOJIS.SUCCESS.CHECK} No Cleanup Needed`,
                        value: 'This user has no data that would require cleanup.',
                        inline: false
                    });
                }

                await interaction.reply({ embeds: [embed], ephemeral: true });

            } else if (action === 'simulate') {
                // Simulate what would happen during cleanup (without actually doing it)
                const userData = await UserCleanupService.checkUserData(targetUser.id);
                const totalRecords = (userData.hasUserRecord ? 1 : 0) + userData.transactionCount + userData.reactionRewardCount;

                if (totalRecords === 0) {
                    const embed = createAdminEmbed('Cleanup Simulation')
                        .setDescription(
                            `${EMOJIS.ADMIN.INFO} **Cleanup Simulation for ${targetUser.username}**\n\n` +
                            `No data found for this user. If they left the server, no cleanup would be performed.`
                        );
                    
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                    return;
                }

                const embed = createAdminEmbed('Cleanup Simulation')
                    .setDescription(
                        `${EMOJIS.ADMIN.WARNING} **Cleanup Simulation for ${targetUser.username}**\n\n` +
                        `This shows what would happen if this user left the server:`
                    )
                    .addFields(
                        {
                            name: `${EMOJIS.ADMIN.TOOLS} Cleanup Process`,
                            value: 
                                `1. **Grace Period**: 5-second delay to prevent accidental cleanup\n` +
                                `2. **Data Removal**: Delete user records in database transaction\n` +
                                `3. **Logging**: Record cleanup results and timing\n` +
                                `4. **Error Handling**: Rollback if any step fails`,
                            inline: false
                        },
                        {
                            name: `${EMOJIS.ADMIN.LIST} Records to Remove`,
                            value: 
                                `${userData.hasUserRecord ? '• User balance record\n' : ''}` +
                                `${userData.transactionCount > 0 ? `• ${userData.transactionCount} transaction records\n` : ''}` +
                                `${userData.reactionRewardCount > 0 ? `• ${userData.reactionRewardCount} reaction reward records\n` : ''}` +
                                `\n**Total: ${totalRecords} records**`,
                            inline: false
                        },
                        {
                            name: `${EMOJIS.ADMIN.INFO} Important Notes`,
                            value: 
                                `• This is a **simulation only** - no data is actually removed\n` +
                                `• Cleanup only happens when users actually leave the server\n` +
                                `• The process is automatic and cannot be undone\n` +
                                `• Guild-specific configurations are not affected`,
                            inline: false
                        }
                    );

                await interaction.reply({ embeds: [embed], ephemeral: true });
            }

        } catch (error) {
            console.error('[Test Cleanup] Error:', error);
            const embed = createErrorEmbed('Test Failed', 'An error occurred while testing the cleanup functionality.');
            await interaction.reply({ embeds: [embed], ephemeral: true });
        }
    })
};
