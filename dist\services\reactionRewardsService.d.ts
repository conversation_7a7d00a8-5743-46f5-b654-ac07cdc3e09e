import { MessageReaction, User } from 'discord.js';
/**
 * Processes a reaction addition and awards coins if eligible
 */
export declare function processReactionReward(reaction: MessageReaction, user: User): Promise<void>;
/**
 * Checks if a channel is monetized for reaction rewards
 */
export declare function isChannelMonetized(channelId: string): Promise<boolean>;
/**
 * Gets reaction reward statistics for a user
 */
export declare function getUserReactionStats(userId: string, days?: number): Promise<{
    totalRewards: number;
    totalCoins: number;
    recentRewards: number;
}>;
//# sourceMappingURL=reactionRewardsService.d.ts.map