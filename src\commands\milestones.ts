import { SlashCommandBuilder, ChatInputCommandInteraction, User } from 'discord.js';
import MilestoneConfiguration from '../models/MilestoneConfiguration';
import UserActivity from '../models/UserActivity';
import MilestoneAchievement from '../models/MilestoneAchievement';
import { withErrorHandler, DatabaseError } from '../utils/errorHandler';
import { createEconomyEmbed, createErrorEmbed, addUserInfo, formatCoins, EMOJIS } from '../utils/embedBuilder';
import { getUserMilestoneStats } from '../services/milestoneService';

module.exports = {
  data: new SlashCommandBuilder()
    .setName('milestones')
    .setDescription('View your milestone progress and achievements')
    .addSubcommand(subcommand =>
      subcommand
        .setName('progress')
        .setDescription('View your current milestone progress and streaks')
    )
    .addSubcommand(subcommand =>
      subcommand
        .setName('achievements')
        .setDescription('View your recent milestone achievements')
        .addIntegerOption(option =>
          option
            .setName('limit')
            .setDescription('Number of recent achievements to show (1-20)')
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(20)
        )
    )
    .addSubcommand(subcommand =>
      subcommand
        .setName('available')
        .setDescription('View available milestones you can achieve')
    )
    .addSubcommand(subcommand =>
      subcommand
        .setName('stats')
        .setDescription('View your overall milestone statistics')
    ),
  execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
    const subcommand = interaction.options.getSubcommand();
    const guildId = interaction.guild!.id;
    const userId = interaction.user.id;

    switch (subcommand) {
      case 'progress':
        await handleProgress(interaction, guildId, userId);
        break;
      case 'achievements':
        await handleAchievements(interaction, guildId, userId);
        break;
      case 'available':
        await handleAvailable(interaction, guildId, userId);
        break;
      case 'stats':
        await handleStats(interaction, guildId, userId);
        break;
      default:
        throw new Error('Unknown subcommand');
    }
  })
};

async function handleProgress(interaction: ChatInputCommandInteraction, guildId: string, userId: string) {
  const userActivity = await UserActivity.findOne({ discordId: userId, guildId });

  if (!userActivity) {
    const embed = createErrorEmbed(
      'No Activity Data',
      'You haven\'t been active enough to track milestone progress yet. Start participating in the server to begin earning milestones!'
    );
    await interaction.reply({ embeds: [embed], ephemeral: true });
    return;
  }

  const embed = createEconomyEmbed(
    `${EMOJIS.MILESTONE.PROGRESS} Your Milestone Progress`,
    'Current activity streaks and progress towards milestones'
  );

  addUserInfo(embed, interaction.user);

  // Login streak information
  embed.addFields({
    name: `${EMOJIS.MILESTONE.STREAK} Login Streak`,
    value: `**Current Streak:** ${userActivity.loginStreak} days\n**Longest Streak:** ${userActivity.longestLoginStreak} days\n**Total Active Days:** ${userActivity.totalDaysActive} days`,
    inline: true
  });

  // Daily activity
  const dailyActivity = `**Messages:** ${userActivity.dailyMessageCount}\n**Voice Minutes:** ${userActivity.dailyVoiceMinutes}\n**Reactions:** ${userActivity.dailyReactionCount}\n**Channels Used:** ${userActivity.uniqueChannelsToday.length}`;
  
  embed.addFields({
    name: `${EMOJIS.MILESTONE.MESSAGE} Today's Activity`,
    value: dailyActivity,
    inline: true
  });

  // Weekly activity
  const weeklyActivity = `**Messages:** ${userActivity.weeklyMessageCount}\n**Voice Minutes:** ${userActivity.weeklyVoiceMinutes}\n**Reactions:** ${userActivity.weeklyReactionCount}\n**Channels Used:** ${userActivity.uniqueChannelsThisWeek.length}`;
  
  embed.addFields({
    name: `${EMOJIS.MILESTONE.DIVERSITY} This Week's Activity`,
    value: weeklyActivity,
    inline: true
  });

  // Server anniversary
  const daysSinceJoin = Math.floor((Date.now() - userActivity.serverJoinDate.getTime()) / (1000 * 60 * 60 * 24));
  const monthsInServer = Math.floor(daysSinceJoin / 30);
  
  embed.addFields({
    name: `${EMOJIS.MILESTONE.ANNIVERSARY} Server Membership`,
    value: `**Days in Server:** ${daysSinceJoin}\n**Monthly Milestones:** ${monthsInServer}\n**Joined:** <t:${Math.floor(userActivity.serverJoinDate.getTime() / 1000)}:R>`,
    inline: false
  });

  await interaction.reply({ embeds: [embed] });
}

async function handleAchievements(interaction: ChatInputCommandInteraction, guildId: string, userId: string) {
  const limit = interaction.options.getInteger('limit') || 10;

  const achievements = await MilestoneAchievement.find({ discordId: userId, guildId })
    .sort({ timestamp: -1 })
    .limit(limit);

  if (achievements.length === 0) {
    const embed = createErrorEmbed(
      'No Achievements Yet',
      'You haven\'t earned any milestone achievements yet. Keep being active in the server to start earning rewards!'
    );
    await interaction.reply({ embeds: [embed], ephemeral: true });
    return;
  }

  const embed = createEconomyEmbed(
    `${EMOJIS.MILESTONE.ACHIEVEMENT} Your Recent Achievements`,
    `Your last ${achievements.length} milestone achievements`
  );

  addUserInfo(embed, interaction.user);

  let achievementText = '';
  let totalRewards = 0;

  for (const achievement of achievements) {
    const emoji = getMilestoneEmoji(achievement.milestoneType);
    const timeAgo = `<t:${Math.floor(achievement.timestamp.getTime() / 1000)}:R>`;
    achievementText += `${emoji} **${achievement.details}**\n${formatCoins(achievement.rewardAmount)} • ${timeAgo}\n\n`;
    totalRewards += achievement.rewardAmount;
  }

  embed.addFields({
    name: 'Recent Achievements',
    value: achievementText || 'No achievements found.',
    inline: false
  });

  embed.addFields({
    name: `${EMOJIS.ECONOMY.COINS} Total Rewards Shown`,
    value: formatCoins(totalRewards),
    inline: true
  });

  await interaction.reply({ embeds: [embed] });
}

async function handleAvailable(interaction: ChatInputCommandInteraction, guildId: string, userId: string) {
  const configs = await MilestoneConfiguration.find({ guildId, enabled: true }).sort({ category: 1, milestoneType: 1 });

  if (configs.length === 0) {
    const embed = createErrorEmbed(
      'No Milestones Available',
      'No milestone configurations are currently enabled for this server. Contact an administrator to set up milestones.'
    );
    await interaction.reply({ embeds: [embed], ephemeral: true });
    return;
  }

  const embed = createEconomyEmbed(
    `${EMOJIS.MILESTONE.STAR} Available Milestones`,
    'Milestones you can achieve through server activity'
  );

  addUserInfo(embed, interaction.user);

  const categories = ['time_based', 'participation_diversity', 'loyalty', 'engagement'];
  
  for (const category of categories) {
    const categoryConfigs = configs.filter(c => c.category === category);
    if (categoryConfigs.length === 0) continue;

    const categoryName = category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    let fieldValue = '';

    for (const config of categoryConfigs) {
      const emoji = getMilestoneEmoji(config.milestoneType);
      const typeName = config.milestoneType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      const threshold = config.requirements.threshold || 'N/A';
      fieldValue += `${emoji} **${typeName}**\n${formatCoins(config.rewardAmount)} • Threshold: ${threshold}\n\n`;
    }

    embed.addFields({
      name: `${EMOJIS.MILESTONE.MEDAL} ${categoryName}`,
      value: fieldValue,
      inline: false
    });
  }

  embed.addFields({
    name: `${EMOJIS.MISC.LIGHTBULB} Tips`,
    value: `• Milestones are automatically awarded when you meet the requirements\n• Some milestones have daily/weekly limits\n• Repeated achievements may have diminishing returns\n• Use \`/milestones progress\` to track your current activity`,
    inline: false
  });

  await interaction.reply({ embeds: [embed] });
}

async function handleStats(interaction: ChatInputCommandInteraction, guildId: string, userId: string) {
  try {
    const stats = await getUserMilestoneStats(userId, guildId);

    const embed = createEconomyEmbed(
      `${EMOJIS.MILESTONE.TROPHY} Your Milestone Statistics`,
      'Overall milestone achievement statistics'
    );

    addUserInfo(embed, interaction.user);

    embed.addFields(
      {
        name: `${EMOJIS.MILESTONE.ACHIEVEMENT} Total Achievements`,
        value: `**${stats.totalAchievements}** milestones earned`,
        inline: true
      },
      {
        name: `${EMOJIS.ECONOMY.COINS} Total Rewards`,
        value: formatCoins(stats.totalRewards),
        inline: true
      },
      {
        name: `${EMOJIS.MILESTONE.STREAK} Current Streaks`,
        value: `**Login Streak:** ${stats.currentStreaks.loginStreak} days\n**Best Streak:** ${stats.currentStreaks.longestLoginStreak} days`,
        inline: true
      },
      {
        name: `${EMOJIS.MILESTONE.PROGRESS} This Week`,
        value: `**Achievements:** ${stats.weeklyProgress.achievementsThisWeek}/${stats.weeklyProgress.maxWeeklyAchievements}\n**Progress:** ${Math.round((stats.weeklyProgress.achievementsThisWeek / stats.weeklyProgress.maxWeeklyAchievements) * 100)}%`,
        inline: true
      }
    );

    if (stats.recentAchievements.length > 0) {
      const lastAchievement = stats.recentAchievements[0];
      const timeAgo = `<t:${Math.floor(lastAchievement.timestamp.getTime() / 1000)}:R>`;
      
      embed.addFields({
        name: `${EMOJIS.MILESTONE.STAR} Latest Achievement`,
        value: `${lastAchievement.details}\n${formatCoins(lastAchievement.rewardAmount)} • ${timeAgo}`,
        inline: false
      });
    }

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('[Milestones Command] Error getting user stats:', error);
    throw new DatabaseError('Failed to retrieve milestone statistics');
  }
}

function getMilestoneEmoji(milestoneType: string): string {
  const emojiMap: Record<string, string> = {
    'login_streak': EMOJIS.MILESTONE.STREAK,
    'channel_diversity_daily': EMOJIS.MILESTONE.DIVERSITY,
    'channel_diversity_weekly': EMOJIS.MILESTONE.DIVERSITY,
    'voice_diversity_daily': EMOJIS.MILESTONE.VOICE,
    'voice_diversity_weekly': EMOJIS.MILESTONE.VOICE,
    'reaction_diversity_daily': '😄',
    'reaction_diversity_weekly': '😄',
    'server_anniversary': EMOJIS.MILESTONE.ANNIVERSARY,
    'total_activity_milestone': EMOJIS.MILESTONE.PROGRESS,
    'voice_time_daily': EMOJIS.MILESTONE.VOICE,
    'voice_time_weekly': EMOJIS.MILESTONE.VOICE,
    'message_count_daily': EMOJIS.MILESTONE.MESSAGE,
    'message_count_weekly': EMOJIS.MILESTONE.MESSAGE
  };

  return emojiMap[milestoneType] || EMOJIS.MILESTONE.STAR;
}
