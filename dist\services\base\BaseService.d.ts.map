{"version": 3, "file": "BaseService.d.ts", "sourceRoot": "", "sources": ["../../../src/services/base/BaseService.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAG/E;;GAEG;AACH,8BAAsB,WAAY,YAAW,QAAQ;IACnD,kBAAyB,IAAI,EAAE,MAAM,CAAC;IAEtC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC;IAC1B,SAAS,CAAC,GAAG,CAAC,EAAE,mBAAmB,CAAC;gBAExB,GAAG,CAAC,EAAE,mBAAmB;IAKrC;;OAEG;IACG,UAAU,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC;IAMlC;;OAEG;IACG,QAAQ,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC;IAMhC;;OAEG;cACa,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC;IAI7C;;OAEG;cACa,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAI3C;;OAEG;IACH,SAAS,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO;IAMxD;;OAEG;IACH,SAAS,CAAC,UAAU,CAAC,CAAC,SAAS,QAAQ,EAAE,WAAW,EAAE,MAAM,GAAG,CAAC;IAOhE;;OAEG;IACH,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,IAAI;IAYtD;;OAEG;IACH,SAAS,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,IAAI;IAI9D;;OAEG;IACH,SAAS,CAAC,oBAAoB,CAAC,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI;IAc5D;;OAEG;IACH,SAAS,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO;CAGtD;AAED;;GAEG;AACH,qBAAa,eAAe;IAC1B,OAAO,CAAC,QAAQ,CAA+B;IAC/C,OAAO,CAAC,MAAM,CAAU;;IAMxB;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,QAAQ,GAAG,IAAI;IAUjC;;OAEG;IACH,GAAG,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC;IAQxC;;OAEG;IACH,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAI1B;;OAEG;IACH,MAAM,IAAI,QAAQ,EAAE;IAIpB;;OAEG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAQjC;;OAEG;IACH,KAAK,IAAI,IAAI;CAId;AAED;;GAEG;AACH,eAAO,MAAM,eAAe,iBAAwB,CAAC"}