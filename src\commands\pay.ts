import { SlashCommandBuilder, ChatInputCommandInteraction } from 'discord.js';
import User from '../models/User';
import Transaction from '../models/Transaction';
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, ValidationError, DatabaseError, InsufficientFundsError } from '../utils/errorHandler';
import { createSuccessEmbed, addUserInfo, formatCoins, EMOJIS } from '../utils/embedBuilder';
import mongoose from 'mongoose';

function logDatabaseOperation(operation: string, details: any) {
    console.log(`[Database Operation] ${operation}:`, JSON.stringify(details, null, 2));
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('pay')
        .setDescription('Pay another user Phalanx Loyalty Coins')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to pay')
                .setRequired(true))
        .addIntegerOption(option =>
            option.setName('amount')
                .setDescription('Amount to pay')
                .setRequired(true)),
    execute: with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(async (interaction: ChatInputCommandInteraction) => {
        // DB connection check
        if (mongoose.connection.readyState !== 1) {
            await interaction.reply({
                content: 'Database is not connected. Please try again in a moment.',
                ephemeral: true
            });
            throw new Error('Attempted to use /pay command before MongoDB connection was established.');
        }
        const senderId = interaction.user.id;
        const recipient = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);

        // Input validation
        if (recipient.id === senderId) {
            throw new ValidationError('You cannot pay yourself.', 'recipient', 'must be a different user');
        }
        if (amount <= 0) {
            throw new ValidationError('Amount must be greater than zero.', 'amount', 'must be a positive number');
        }
        if (recipient.bot) {
            throw new ValidationError('You cannot pay a bot.', 'recipient', 'must be a real user, not a bot');
        }

        const session = await mongoose.startSession();
        logDatabaseOperation('Starting Payment Transaction', {
            senderId,
            recipientId: recipient.id,
            amount
        });

        try {
            await session.withTransaction(async () => {
                // Ensure sender exists and has sufficient balance
                const sender = await User.findOneAndUpdate(
                    { discordId: senderId },
                    { $setOnInsert: { discordId: senderId, balance: 0 } },
                    { upsert: true, new: true, session }
                );

                if (sender.balance < amount) {
                    throw new InsufficientFundsError(amount, sender.balance);
                }

                // Deduct from sender
                await User.findOneAndUpdate(
                    { discordId: senderId },
                    { $inc: { balance: -amount } },
                    { session }
                );

                // Add to recipient (create if doesn't exist)
                await User.findOneAndUpdate(
                    { discordId: recipient.id },
                    {
                        $inc: { balance: amount },
                        $setOnInsert: { discordId: recipient.id }
                    },
                    { upsert: true, session }
                );

                // Create transaction records for both users
                await Transaction.create([
                    {
                        discordId: senderId,
                        type: 'pay',
                        amount: -amount,
                        details: `Paid to ${recipient.tag}`,
                        timestamp: new Date()
                    },
                    {
                        discordId: recipient.id,
                        type: 'pay',
                        amount: amount,
                        details: `Received from ${interaction.user.tag}`,
                        timestamp: new Date()
                    }
                ], { session });

                logDatabaseOperation('Payment Transaction Complete', {
                    senderId,
                    recipientId: recipient.id,
                    amount
                });
            });

            // Create rich success embed
            const embed = createSuccessEmbed('Payment Successful!')
                .setDescription(
                    `${EMOJIS.ECONOMY.MONEY} **Transaction Complete**\n\n` +
                    `${formatCoins(amount)} has been transferred to **${recipient.displayName}**!`
                )
                .addFields(
                    {
                        name: `${EMOJIS.ACTIONS.LIGHTNING} From`,
                        value: `**${interaction.user.displayName}**`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.ACTIONS.TARGET} To`,
                        value: `**${recipient.displayName}**`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.ECONOMY.COINS} Amount`,
                        value: formatCoins(amount),
                        inline: true
                    },
                    {
                        name: `${EMOJIS.MISC.CLOCK} Transaction Time`,
                        value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                        inline: false
                    }
                )
                .setFooter({
                    text: 'Thank you for using the Phalanx economy system!'
                });

            // Add sender's avatar to embed
            addUserInfo(embed, interaction.user);

            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
        } catch (error: unknown) {
            if (error instanceof InsufficientFundsError) {
                throw error;
            } else if (error instanceof Error && error.name === 'ValidationError') {
                throw new DatabaseError('Transaction validation failed', error);
            } else if (error instanceof Error && error.name === 'MongoServerError') {
                const err = error as any;
                if (err.code === 11000) {
                    throw new DatabaseError('Transaction conflict detected', error);
                }
                throw new DatabaseError('Database operation failed', error);
            } else if (error instanceof Error) {
                throw new DatabaseError('Payment processing failed', error);
            }
            throw new DatabaseError('Unexpected error during payment');
        } finally {
            await session.endSession();
        }
    })
};
