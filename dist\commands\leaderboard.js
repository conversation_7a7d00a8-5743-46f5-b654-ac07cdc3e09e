"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const economyService_1 = require("../services/economyService");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('leaderboard')
        .setDescription('Show the top users by balance'),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        try {
            const users = await (0, economyService_1.getLeaderboard)(10);
            if (users.length === 0) {
                const embed = (0, embedBuilder_1.createEconomyEmbed)('Leaderboard')
                    .setDescription(`${embedBuilder_1.EMOJIS.MISC.MAGNIFYING} No users found yet!\n\nBe the first to earn some coins and claim the top spot!`)
                    .setColor(embedBuilder_1.COLORS.INFO);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            // Filter users to only include current guild members and build leaderboard entries
            const currentMemberEntries = [];
            let position = 1;
            for (const user of users) {
                try {
                    // Check if user is still a member of the guild
                    if (!interaction.guild) {
                        console.warn('[Leaderboard] No guild context available');
                        continue;
                    }
                    // First check cache, then fetch if needed
                    let guildMember;
                    try {
                        guildMember = interaction.guild.members.cache.get(user.discordId);
                        if (!guildMember) {
                            // Try to fetch the member if not in cache
                            guildMember = await interaction.guild.members.fetch(user.discordId);
                        }
                    }
                    catch (fetchError) {
                        // User is not in the guild anymore, skip them
                        continue;
                    }
                    // If we get here, the user is still in the guild
                    // Try to get their display name
                    const displayName = guildMember.displayName || guildMember.user.username;
                    // Add special emojis for top positions
                    let positionEmoji = '';
                    if (position === 1)
                        positionEmoji = '🥇';
                    else if (position === 2)
                        positionEmoji = '🥈';
                    else if (position === 3)
                        positionEmoji = '🥉';
                    else
                        positionEmoji = `${embedBuilder_1.EMOJIS.ROLES.MEDAL}`;
                    currentMemberEntries.push(`${positionEmoji} **#${position}** ${displayName} — ${(0, embedBuilder_1.formatCoins)(user.balance)}`);
                    position++;
                    // Stop if we have enough entries for display
                    if (currentMemberEntries.length >= 10) {
                        break;
                    }
                }
                catch (error) {
                    // Log error but continue processing other users
                    console.error(`[Leaderboard] Error processing user ${user.discordId}:`, error);
                    continue;
                }
            }
            // Check if we have any current members to display
            if (currentMemberEntries.length === 0) {
                const embed = (0, embedBuilder_1.createEconomyEmbed)('Leaderboard')
                    .setDescription(`${embedBuilder_1.EMOJIS.MISC.MAGNIFYING} No current members found on the leaderboard!\n\nBe the first to earn some coins and claim the top spot!`)
                    .setColor(embedBuilder_1.COLORS.INFO);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            // Create rich leaderboard embed
            const embed = (0, embedBuilder_1.createEconomyEmbed)('Phalanx Loyalty Coins Leaderboard')
                .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.TROPHY} **Top ${currentMemberEntries.length} Current Members**\n\n` +
                currentMemberEntries.join('\n') +
                `\n\n${embedBuilder_1.EMOJIS.ECONOMY.SPARKLES} *Keep earning to climb higher!*`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.CALENDAR} Last Updated`,
                value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
                inline: true
            })
                .setFooter({
                text: 'Use /balance to check your current position!'
            })
                .setThumbnail('https://cdn.discordapp.com/emojis/1234567890123456789.png'); // You can add a custom leaderboard icon
            // Create quick action buttons
            const actionButtons = (0, embedBuilder_1.createQuickActionButtons)();
            await interaction.reply({
                embeds: [embed],
                components: [actionButtons],
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            throw new errorHandler_1.DatabaseError('Failed to fetch leaderboard data.');
        }
    })
};
//# sourceMappingURL=leaderboard.js.map