/**
 * Core Interfaces and Types
 * Shared interfaces used throughout the application
 */
import { Client, Guild, GuildMember, ChatInputCommandInteraction, ButtonInteraction } from 'discord.js';
/**
 * Base service interface
 */
export interface IService {
    name: string;
    initialize?(): Promise<void>;
    shutdown?(): Promise<void>;
}
/**
 * Database service interface
 */
export interface IDatabaseService extends IService {
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    isConnected(): boolean;
    getConnectionStatus(): string;
}
/**
 * Logger interface
 */
export interface ILogger {
    error(message: string, meta?: any): void;
    warn(message: string, meta?: any): void;
    info(message: string, meta?: any): void;
    debug(message: string, meta?: any): void;
}
/**
 * Command interface
 */
export interface ICommand {
    data: any;
    category: string;
    adminOnly?: boolean;
    guildOnly?: boolean;
    cooldown?: number;
    execute(interaction: ChatInputCommandInteraction): Promise<void>;
}
/**
 * Event handler interface
 */
export interface IEventHandler {
    name: string;
    once?: boolean;
    execute(...args: any[]): Promise<void>;
}
/**
 * Economy service interface
 */
export interface IEconomyService extends IService {
    adjustBalance(discordId: string, amount: number, type: TransactionType, details?: string, client?: Client, guildId?: string, dynastyId?: string): Promise<void>;
    getBalance(discordId: string): Promise<number>;
    getLeaderboard(guildId?: string, limit?: number): Promise<LeaderboardEntry[]>;
    getTransactionHistory(discordId: string, limit?: number): Promise<TransactionRecord[]>;
}
/**
 * Role service interface
 */
export interface IRoleService extends IService {
    checkAndAssignRoles(client: Client, discordId: string, guildId: string, balance: number): Promise<RoleAssignmentResult | null>;
    getUserAchievementRoles(member: GuildMember): Promise<UserRoleInfo>;
}
/**
 * Milestone service interface
 */
export interface IMilestoneService extends IService {
    checkAndProcessMilestones(client: Client, discordId: string, guildId: string, activityType: ActivityType, activityData?: any): Promise<MilestoneCheckResult[]>;
    createDefaultConfigurations(guildId: string): Promise<void>;
}
/**
 * Transaction types
 */
export type TransactionType = 'give' | 'fine' | 'pay' | 'role_achievement' | 'reaction' | 'tax' | 'starter_balance' | 'content_submission' | 'content_reward' | 'milestone' | 'dynasty_bonus';
/**
 * Activity types for milestone tracking
 */
export type ActivityType = 'message' | 'voice' | 'reaction' | 'login';
/**
 * Leaderboard entry
 */
export interface LeaderboardEntry {
    discordId: string;
    balance: number;
    rank: number;
    displayName?: string;
    dynastyId?: string;
    dynastyName?: string;
}
/**
 * Transaction record
 */
export interface TransactionRecord {
    id: string;
    discordId: string;
    type: TransactionType;
    amount: number;
    details?: string;
    dynastyId?: string;
    timestamp: Date;
}
/**
 * Role assignment result
 */
export interface RoleAssignmentResult {
    rolesAssigned: Array<{
        roleId: string;
        roleName: string;
        price: number;
        description?: string;
    }>;
    member: GuildMember;
    newBalance: number;
}
/**
 * User role information
 */
export interface UserRoleInfo {
    currentRoles: Array<{
        roleId: string;
        roleName: string;
        price: number;
        description?: string;
    }>;
    availableRoles: Array<{
        roleId: string;
        roleName: string;
        price: number;
        description?: string;
        canAfford: boolean;
    }>;
    userBalance: number;
}
/**
 * Milestone check result
 */
export interface MilestoneCheckResult {
    milestoneType: string;
    rewardAmount: number;
    achievementDetails: string;
    timestamp: Date;
}
/**
 * Application context
 */
export interface IApplicationContext {
    client: Client;
    logger: ILogger;
    services: Map<string, IService>;
    getService<T extends IService>(name: string): T;
    registerService(service: IService): void;
    shutdown(): Promise<void>;
}
/**
 * Error context for enhanced error handling
 */
export interface ErrorContext {
    errorId: string;
    timestamp: Date;
    userId?: string;
    guildId?: string;
    channelId?: string;
    commandName?: string;
    parameters?: Record<string, any>;
    stackTrace?: string;
}
/**
 * Configuration interfaces
 */
export interface DatabaseConfig {
    uri: string;
    options: {
        maxPoolSize: number;
        serverSelectionTimeoutMS: number;
        socketTimeoutMS: number;
    };
}
export interface DiscordConfig {
    token: string;
    clientId: string;
    intents: string[];
}
export interface LoggingConfig {
    level: string;
    filePath?: string;
    console: boolean;
    file: boolean;
    format: 'json' | 'simple';
}
/**
 * Service registration options
 */
export interface ServiceOptions {
    autoStart?: boolean;
    dependencies?: string[];
    priority?: number;
}
/**
 * Command execution context
 */
export interface CommandContext {
    interaction: ChatInputCommandInteraction;
    client: Client;
    guild: Guild | null;
    member: GuildMember | null;
    logger: ILogger;
}
/**
 * Button interaction context
 */
export interface ButtonContext {
    interaction: ButtonInteraction;
    client: Client;
    guild: Guild | null;
    member: GuildMember | null;
    logger: ILogger;
}
//# sourceMappingURL=interfaces.d.ts.map