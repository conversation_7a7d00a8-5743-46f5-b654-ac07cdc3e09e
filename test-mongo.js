const { MongoClient } = require('mongodb');

const uri = "mongodb+srv://Bot:<EMAIL>/?retryWrites=true&w=majority&appName=phalanxdata";

async function run() {
  try {
    const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
    await client.connect();
    console.log("Connected successfully to MongoDB!");
    await client.close();
  } catch (err) {
    console.error("Connection failed:", err);
  }
}

run();
