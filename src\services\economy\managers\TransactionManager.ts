/**
 * Transaction Manager
 * Handles transaction history and record management
 */

import { TransactionRecord, TransactionType, ILogger } from '../../../core/interfaces';
import { DatabaseError } from '../../../utils/errorHandler';
import { requireFeature } from '../../../config/features';
import Transaction from '../../../models/Transaction';

/**
 * Transaction management operations
 */
export class TransactionManager {
  private logger: ILogger;

  constructor(logger: ILogger) {
    this.logger = logger;
  }

  /**
   * Get transaction history for a user
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getTransactionHistory(discordId: string, limit: number = 20): Promise<TransactionRecord[]> {
    try {
      this.validateTransactionParams(discordId, limit);

      const transactions = await Transaction.find({ discordId })
        .sort({ timestamp: -1 })
        .limit(limit)
        .lean();

      const history = transactions.map(tx => ({
        id: tx._id.toString(),
        discordId: tx.discordId,
        type: tx.type,
        amount: tx.amount,
        details: tx.details,
        timestamp: tx.timestamp,
      }));

      this.logOperation('Transaction history retrieved', { 
        discordId, 
        limit, 
        transactionsCount: history.length 
      });

      return history;
    } catch (error) {
      this.handleError(error, { discordId, limit });
      throw new DatabaseError(`Failed to get transaction history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get transactions by type
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getTransactionsByType(
    discordId: string, 
    type: TransactionType, 
    limit: number = 20
  ): Promise<TransactionRecord[]> {
    try {
      this.validateTransactionParams(discordId, limit);

      const transactions = await Transaction.find({ 
        discordId, 
        type 
      })
        .sort({ timestamp: -1 })
        .limit(limit)
        .lean();

      const history = transactions.map(tx => ({
        id: tx._id.toString(),
        discordId: tx.discordId,
        type: tx.type,
        amount: tx.amount,
        details: tx.details,
        timestamp: tx.timestamp,
      }));

      this.logOperation('Transactions by type retrieved', { 
        discordId, 
        type, 
        limit, 
        transactionsCount: history.length 
      });

      return history;
    } catch (error) {
      this.handleError(error, { discordId, type, limit });
      throw new DatabaseError(`Failed to get transactions by type: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get transaction statistics for a user
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getTransactionStats(discordId: string): Promise<any> {
    try {
      if (!discordId || typeof discordId !== 'string') {
        throw new DatabaseError('Invalid Discord ID provided');
      }

      const trimmedDiscordId = discordId.trim();

      const stats = await Transaction.aggregate([
        { $match: { discordId: trimmedDiscordId } },
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 },
            totalAmount: { $sum: '$amount' },
            averageAmount: { $avg: '$amount' },
            lastTransaction: { $max: '$timestamp' }
          }
        }
      ]);

      const result = {
        totalTransactions: stats.reduce((sum, stat) => sum + stat.count, 0),
        byType: stats.reduce((acc, stat) => {
          acc[stat._id] = {
            count: stat.count,
            totalAmount: stat.totalAmount,
            averageAmount: stat.averageAmount,
            lastTransaction: stat.lastTransaction
          };
          return acc;
        }, {} as Record<string, any>)
      };

      this.logOperation('Transaction stats calculated', { 
        discordId: trimmedDiscordId, 
        totalTransactions: result.totalTransactions 
      });

      return result;
    } catch (error) {
      this.handleError(error, { discordId });
      throw new DatabaseError(`Failed to get transaction stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get recent transactions across all users
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getRecentTransactions(limit: number = 50): Promise<TransactionRecord[]> {
    try {
      if (typeof limit !== 'number' || limit < 1 || limit > 100) {
        throw new DatabaseError('Limit must be a number between 1 and 100');
      }

      const transactions = await Transaction.find({})
        .sort({ timestamp: -1 })
        .limit(limit)
        .lean();

      const history = transactions.map(tx => ({
        id: tx._id.toString(),
        discordId: tx.discordId,
        type: tx.type,
        amount: tx.amount,
        details: tx.details,
        timestamp: tx.timestamp,
      }));

      this.logOperation('Recent transactions retrieved', { 
        limit, 
        transactionsCount: history.length 
      });

      return history;
    } catch (error) {
      this.handleError(error, { limit });
      throw new DatabaseError(`Failed to get recent transactions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete old transactions (cleanup)
   */
  @requireFeature('ECONOMY_SYSTEM')
  async deleteOldTransactions(olderThanDays: number): Promise<number> {
    try {
      if (typeof olderThanDays !== 'number' || olderThanDays < 1) {
        throw new DatabaseError('Days must be a positive number');
      }

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const result = await Transaction.deleteMany({
        timestamp: { $lt: cutoffDate }
      });

      this.logOperation('Old transactions deleted', { 
        olderThanDays, 
        cutoffDate, 
        deletedCount: result.deletedCount 
      });

      return result.deletedCount;
    } catch (error) {
      this.handleError(error, { olderThanDays });
      throw new DatabaseError(`Failed to delete old transactions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate transaction parameters
   */
  private validateTransactionParams(discordId: string, limit: number): void {
    if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
      throw new DatabaseError('Invalid Discord ID provided');
    }

    if (typeof limit !== 'number' || limit < 1 || limit > 100) {
      throw new DatabaseError('Limit must be a number between 1 and 100');
    }
  }

  /**
   * Log operation
   */
  private logOperation(operation: string, details?: any): void {
    this.logger.debug(`[TransactionManager] ${operation}`, details);
  }

  /**
   * Handle errors
   */
  private handleError(error: any, context?: any): void {
    this.logger.error('[TransactionManager] Error', {
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
      context,
    });
  }
}
