{"version": 3, "file": "roleAssignmentService.js", "sourceRoot": "", "sources": ["../../src/services/roleAssignmentService.ts"], "names": [], "mappings": ";;AAyBA,kDAoGC;AAOD,4EAqFC;AAOD,0DA2BC;AA1PD,yCAA6C;AAC7C,qDAAiD;AACjD,wDAA6F;AAC7F,wDAAsD;AAatD;;;;;;;GAOG;AACI,KAAK,UAAU,mBAAmB,CACrC,MAAc,EACd,SAAiB,EACjB,OAAe,EACf,UAAkB;IAElB,IAAI,CAAC;QACD,gBAAgB;QAChB,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO,CAAC,IAAI,CAAC,SAAS,OAAO,gCAAgC,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,iBAAiB;QACjB,IAAI,MAAmB,CAAC;QACxB,IAAI,CAAC;YACD,MAAM,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,IAAI,CAAC,UAAU,SAAS,uBAAuB,OAAO,EAAE,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,qEAAqE;QACrE,MAAM,cAAc,GAAG,MAAM,kBAAW,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QACnE,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,yDAAyD;QACzD,MAAM,aAAa,GAAG,EAAE,CAAC;QAEzB,KAAK,MAAM,WAAW,IAAI,cAAc,EAAE,CAAC;YACvC,gDAAgD;YAChD,IAAI,UAAU,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;gBAClC,+CAA+C;gBAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC9C,sCAAsC;oBACtC,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;oBAC9D,IAAI,WAAW,EAAE,CAAC;wBACd,aAAa,CAAC,IAAI,CAAC;4BACf,MAAM,EAAE,WAAW,CAAC,MAAM;4BAC1B,QAAQ,EAAE,WAAW,CAAC,IAAI;4BAC1B,KAAK,EAAE,WAAW,CAAC,KAAK;4BACxB,WAAW,EAAE,WAAW,CAAC,WAAW;4BACpC,WAAW,EAAE,WAAW;yBAC3B,CAAC,CAAC;oBACP,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,IAAI,CAAC,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,wBAAwB,OAAO,EAAE,CAAC,CAAC;oBACnG,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,qCAAqC;QACrC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,8BAA8B;QAC9B,MAAM,aAAa,GAAG,EAAE,CAAC;QACzB,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC;gBACD,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAE7C,gCAAgC;gBAChC,MAAM,IAAA,8BAAa,EACf,SAAS,EACT,CAAC,EACD,kBAAkB,EAClB,yBAAyB,QAAQ,CAAC,QAAQ,eAAe,QAAQ,CAAC,KAAK,OAAO,CACjF,CAAC;gBAEF,aAAa,CAAC,IAAI,CAAC;oBACf,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,WAAW,EAAE,QAAQ,CAAC,WAAW;iBACpC,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CAAC,iBAAiB,QAAQ,CAAC,QAAQ,YAAY,SAAS,aAAa,OAAO,EAAE,CAAC,CAAC;YAC/F,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,yBAAyB,QAAQ,CAAC,QAAQ,YAAY,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7F,CAAC;QACL,CAAC;QAED,2CAA2C;QAC3C,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO;gBACH,aAAa,EAAE,aAAa;gBAC5B,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,UAAU;aACzB,CAAC;QACN,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACtD,MAAM,IAAI,4BAAa,CAAC,kCAAkC,CAAC,CAAC;IAChE,CAAC;AACL,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,gCAAgC,CAClD,MAA4B,EAC5B,MAAc;IAEd,IAAI,CAAC;QACD,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAErD,8DAA8D;QAC9D,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,0BAA0B;YAC1B,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,KAAK,GAAG,IAAA,iCAAkB,EAAC,0BAA0B,CAAC;iBACvD,cAAc,CACX,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,2BAA2B;gBAClD,yCAAyC,IAAI,CAAC,QAAQ,sBAAsB,CAC/E;iBACA,SAAS,CACN;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,KAAK,uBAAuB;gBAClD,KAAK,EAAE,KAAK,IAAI,CAAC,QAAQ,IAAI;gBAC7B,MAAM,EAAE,IAAI;aACf,EACD;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,mBAAmB;gBAChD,KAAK,EAAE,IAAA,0BAAW,EAAC,IAAI,CAAC,KAAK,CAAC;gBAC9B,MAAM,EAAE,IAAI;aACf,EACD;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,eAAe;gBAC5C,KAAK,EAAE,IAAA,0BAAW,EAAC,UAAU,CAAC;gBAC9B,MAAM,EAAE,IAAI;aACf,EACD;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,MAAM,sBAAsB;gBACjD,KAAK,EAAE,IAAI,CAAC,WAAW,IAAI,sCAAsC;gBACjE,MAAM,EAAE,KAAK;aAChB,CACJ;iBACA,SAAS,CAAC;gBACP,IAAI,EAAE,uFAAuF;aAChG,CAAC,CAAC;YAEP,IAAA,0BAAW,EAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YAEhC,kBAAkB;YAClB,IAAI,CAAC;gBACD,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC3C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC,6BAA6B,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,6BAA6B;YAC7B,MAAM,KAAK,GAAG,IAAA,iCAAkB,EAAC,oCAAoC,CAAC;iBACjE,cAAc,CACX,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,2BAA2B;gBAClD,qCAAqC,aAAa,CAAC,MAAM,uBAAuB,CACnF;iBACA,SAAS,CACN;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,eAAe;gBAC5C,KAAK,EAAE,IAAA,0BAAW,EAAC,UAAU,CAAC;gBAC9B,MAAM,EAAE,IAAI;aACf,EACD;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,KAAK,wBAAwB;gBACnD,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,CAAC,QAAQ,OAAO,IAAA,0BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;gBAClG,MAAM,EAAE,KAAK;aAChB,CACJ;iBACA,SAAS,CAAC;gBACP,IAAI,EAAE,uFAAuF;aAChG,CAAC,CAAC;YAEP,IAAA,0BAAW,EAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YAEhC,kBAAkB;YAClB,IAAI,CAAC;gBACD,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC3C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC,6BAA6B,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;QACL,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;IAC1E,CAAC;AACL,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,uBAAuB,CAAC,MAAmB;IAM7D,IAAI,CAAC;QACD,MAAM,cAAc,GAAG,MAAM,kBAAW,CAAC,IAAI,EAAE,CAAC;QAChD,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAE5B,KAAK,MAAM,WAAW,IAAI,cAAc,EAAE,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7C,gBAAgB,CAAC,IAAI,CAAC;oBAClB,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,QAAQ,EAAE,WAAW,CAAC,IAAI;oBAC1B,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,WAAW,EAAE,WAAW,CAAC,WAAW;iBACvC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,oCAAoC;QACpC,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC9D,OAAO,EAAE,CAAC;IACd,CAAC;AACL,CAAC"}