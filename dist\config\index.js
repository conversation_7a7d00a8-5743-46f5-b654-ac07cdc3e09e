"use strict";
/**
 * Configuration Module Index
 * Centralized exports for all configuration modules
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireFeatureRuntime = exports.isFeatureActiveRuntime = exports.runtimeConfig = exports.FEATURE_REGISTRY = exports.requireFeature = exports.isGuildSpecificFeature = exports.requiresAdminPermission = exports.isFeatureActive = exports.featureManager = exports.SCHEDULES = exports.VALIDATION = exports.FEATURES = exports.LOGGING = exports.ERROR_HANDLING = exports.DATABASE = exports.DISCORD = exports.DYNASTY = exports.MILESTONES = exports.ECONOMY = exports.getLoggingConfig = exports.getDiscordConfig = exports.getDatabaseConfig = exports.isTest = exports.isProduction = exports.isDevelopment = exports.ENV = void 0;
// Environment configuration
var environment_1 = require("./environment");
Object.defineProperty(exports, "ENV", { enumerable: true, get: function () { return environment_1.ENV; } });
Object.defineProperty(exports, "isDevelopment", { enumerable: true, get: function () { return environment_1.isDevelopment; } });
Object.defineProperty(exports, "isProduction", { enumerable: true, get: function () { return environment_1.isProduction; } });
Object.defineProperty(exports, "isTest", { enumerable: true, get: function () { return environment_1.isTest; } });
Object.defineProperty(exports, "getDatabaseConfig", { enumerable: true, get: function () { return environment_1.getDatabaseConfig; } });
Object.defineProperty(exports, "getDiscordConfig", { enumerable: true, get: function () { return environment_1.getDiscordConfig; } });
Object.defineProperty(exports, "getLoggingConfig", { enumerable: true, get: function () { return environment_1.getLoggingConfig; } });
// Constants
var constants_1 = require("./constants");
Object.defineProperty(exports, "ECONOMY", { enumerable: true, get: function () { return constants_1.ECONOMY; } });
Object.defineProperty(exports, "MILESTONES", { enumerable: true, get: function () { return constants_1.MILESTONES; } });
Object.defineProperty(exports, "DYNASTY", { enumerable: true, get: function () { return constants_1.DYNASTY; } });
Object.defineProperty(exports, "DISCORD", { enumerable: true, get: function () { return constants_1.DISCORD; } });
Object.defineProperty(exports, "DATABASE", { enumerable: true, get: function () { return constants_1.DATABASE; } });
Object.defineProperty(exports, "ERROR_HANDLING", { enumerable: true, get: function () { return constants_1.ERROR_HANDLING; } });
Object.defineProperty(exports, "LOGGING", { enumerable: true, get: function () { return constants_1.LOGGING; } });
Object.defineProperty(exports, "FEATURES", { enumerable: true, get: function () { return constants_1.FEATURES; } });
Object.defineProperty(exports, "VALIDATION", { enumerable: true, get: function () { return constants_1.VALIDATION; } });
Object.defineProperty(exports, "SCHEDULES", { enumerable: true, get: function () { return constants_1.SCHEDULES; } });
// Feature management
var features_1 = require("./features");
Object.defineProperty(exports, "featureManager", { enumerable: true, get: function () { return features_1.featureManager; } });
Object.defineProperty(exports, "isFeatureActive", { enumerable: true, get: function () { return features_1.isFeatureActive; } });
Object.defineProperty(exports, "requiresAdminPermission", { enumerable: true, get: function () { return features_1.requiresAdminPermission; } });
Object.defineProperty(exports, "isGuildSpecificFeature", { enumerable: true, get: function () { return features_1.isGuildSpecificFeature; } });
Object.defineProperty(exports, "requireFeature", { enumerable: true, get: function () { return features_1.requireFeature; } });
Object.defineProperty(exports, "FEATURE_REGISTRY", { enumerable: true, get: function () { return features_1.FEATURE_REGISTRY; } });
// Runtime configuration
var runtime_1 = require("./runtime");
Object.defineProperty(exports, "runtimeConfig", { enumerable: true, get: function () { return runtime_1.runtimeConfig; } });
Object.defineProperty(exports, "isFeatureActiveRuntime", { enumerable: true, get: function () { return runtime_1.isFeatureActiveRuntime; } });
Object.defineProperty(exports, "requireFeatureRuntime", { enumerable: true, get: function () { return runtime_1.requireFeatureRuntime; } });
/**
 * Configuration validation on module load
 */
const environment_2 = require("./environment");
const features_2 = require("./features");
const runtime_2 = require("./runtime");
// Validate configuration on startup
console.log('[Config] Environment:', environment_2.ENV.NODE_ENV);
console.log('[Config] Features enabled:', features_2.featureManager.getEnabledFeatures().length);
// Start runtime config cleanup schedule
runtime_2.runtimeConfig.startCleanupSchedule();
if (environment_2.ENV.DEBUG_MODE) {
    console.log('[Config] Debug mode enabled');
    console.log('[Config] Enabled features:', features_2.featureManager.getEnabledFeatures());
}
//# sourceMappingURL=index.js.map