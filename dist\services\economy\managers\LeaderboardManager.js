"use strict";
/**
 * Leaderboard Manager
 * Handles leaderboard operations and rankings
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderboardManager = void 0;
const errorHandler_1 = require("../../../utils/errorHandler");
const features_1 = require("../../../config/features");
const User_1 = __importDefault(require("../../../models/User"));
/**
 * Leaderboard management operations
 */
class LeaderboardManager {
    constructor(logger) {
        this.logger = logger;
    }
    /**
     * Get leaderboard
     */
    async getLeaderboard(guildId, limit = 10) {
        try {
            this.validateLeaderboardParams(limit);
            const users = await User_1.default.find({})
                .sort({ balance: -1 })
                .limit(limit)
                .lean();
            const leaderboard = users.map((user, index) => ({
                discordId: user.discordId,
                balance: user.balance,
                rank: index + 1,
            }));
            this.logOperation('Leaderboard retrieved', {
                guildId,
                limit,
                entriesCount: leaderboard.length
            });
            return leaderboard;
        }
        catch (error) {
            this.handleError(error, { guildId, limit });
            throw new errorHandler_1.DatabaseError(`Failed to get leaderboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get user rank
     */
    async getUserRank(discordId) {
        try {
            if (!discordId || typeof discordId !== 'string') {
                throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
            }
            const trimmedDiscordId = discordId.trim();
            // Get user's balance
            const user = await User_1.default.findOne({ discordId: trimmedDiscordId }).lean();
            if (!user) {
                throw new errorHandler_1.DatabaseError('User not found');
            }
            // Count users with higher balance
            const higherBalanceCount = await User_1.default.countDocuments({
                balance: { $gt: user.balance }
            });
            const rank = higherBalanceCount + 1;
            this.logOperation('User rank calculated', {
                discordId: trimmedDiscordId,
                balance: user.balance,
                rank
            });
            return rank;
        }
        catch (error) {
            this.handleError(error, { discordId });
            throw new errorHandler_1.DatabaseError(`Failed to get user rank: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get top users by balance
     */
    async getTopUsers(limit = 10) {
        try {
            this.validateLeaderboardParams(limit);
            const users = await User_1.default.find({})
                .sort({ balance: -1 })
                .limit(limit)
                .select('discordId balance')
                .lean();
            const topUsers = users.map((user, index) => ({
                discordId: user.discordId,
                balance: user.balance,
                rank: index + 1,
            }));
            this.logOperation('Top users retrieved', {
                limit,
                usersCount: topUsers.length
            });
            return topUsers;
        }
        catch (error) {
            this.handleError(error, { limit });
            throw new errorHandler_1.DatabaseError(`Failed to get top users: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get leaderboard around user
     */
    async getLeaderboardAroundUser(discordId, range = 5) {
        try {
            if (!discordId || typeof discordId !== 'string') {
                throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
            }
            const trimmedDiscordId = discordId.trim();
            const userRank = await this.getUserRank(trimmedDiscordId);
            // Calculate range bounds
            const startRank = Math.max(1, userRank - range);
            const endRank = userRank + range;
            const skip = startRank - 1;
            const limit = endRank - startRank + 1;
            const users = await User_1.default.find({})
                .sort({ balance: -1 })
                .skip(skip)
                .limit(limit)
                .lean();
            const leaderboard = users.map((user, index) => ({
                discordId: user.discordId,
                balance: user.balance,
                rank: startRank + index,
            }));
            this.logOperation('Leaderboard around user retrieved', {
                discordId: trimmedDiscordId,
                userRank,
                range,
                entriesCount: leaderboard.length
            });
            return leaderboard;
        }
        catch (error) {
            this.handleError(error, { discordId, range });
            throw new errorHandler_1.DatabaseError(`Failed to get leaderboard around user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get leaderboard statistics
     */
    async getLeaderboardStats() {
        try {
            const stats = await User_1.default.aggregate([
                {
                    $group: {
                        _id: null,
                        totalUsers: { $sum: 1 },
                        totalBalance: { $sum: '$balance' },
                        averageBalance: { $avg: '$balance' },
                        maxBalance: { $max: '$balance' },
                        minBalance: { $min: '$balance' }
                    }
                }
            ]);
            const result = stats[0] || {
                totalUsers: 0,
                totalBalance: 0,
                averageBalance: 0,
                maxBalance: 0,
                minBalance: 0
            };
            this.logOperation('Leaderboard stats calculated', result);
            return result;
        }
        catch (error) {
            this.handleError(error);
            throw new errorHandler_1.DatabaseError(`Failed to get leaderboard stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Validate leaderboard parameters
     */
    validateLeaderboardParams(limit) {
        if (typeof limit !== 'number' || limit < 1 || limit > 100) {
            throw new errorHandler_1.DatabaseError('Limit must be a number between 1 and 100');
        }
    }
    /**
     * Log operation
     */
    logOperation(operation, details) {
        this.logger.debug(`[LeaderboardManager] ${operation}`, details);
    }
    /**
     * Handle errors
     */
    handleError(error, context) {
        this.logger.error('[LeaderboardManager] Error', {
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
            } : error,
            context,
        });
    }
}
exports.LeaderboardManager = LeaderboardManager;
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getLeaderboard", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getUserRank", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getTopUsers", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getLeaderboardAroundUser", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getLeaderboardStats", null);
//# sourceMappingURL=LeaderboardManager.js.map