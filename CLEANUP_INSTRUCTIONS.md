# Node.js Cleanup Instructions

This document provides step-by-step instructions for cleaning up potential Node.js conflicts and preparing for a fresh installation.

## 🚨 IMPORTANT: Backup First

Before proceeding, ensure you have:
- ✅ Committed all source code to version control (Git)
- ✅ Backed up your .env file with environment variables
- ✅ Saved the DEPLOYMENT_CONFIGURATION_BACKUP.md file

## 🧹 Step 1: Remove Node.js Cache and Dependencies

### Files/Directories to Remove

#### 1. Node Modules Directory
```bash
# Remove the entire node_modules directory
rm -rf node_modules/
# OR on Windows:
rmdir /s node_modules
```

#### 2. Package Lock Files
```bash
# Remove package-lock.json (npm)
rm package-lock.json
# OR on Windows:
del package-lock.json

# Remove yarn.lock if it exists (Yarn)
rm yarn.lock
# OR on Windows:
del yarn.lock
```

#### 3. Compiled JavaScript Files
```bash
# Remove the entire dist directory
rm -rf dist/
# OR on Windows:
rmdir /s dist
```

#### 4. NPM Cache Directories
```bash
# Clear npm cache
npm cache clean --force

# Remove .npm directory if it exists in project
rm -rf .npm/
# OR on Windows:
rmdir /s .npm
```

#### 5. Other Cache Directories
```bash
# Remove .cache directory if it exists
rm -rf .cache/
# OR on Windows:
rmdir /s .cache

# Remove .nyc_output if it exists (test coverage)
rm -rf .nyc_output/
# OR on Windows:
rmdir /s .nyc_output
```

## 🔍 Step 2: Identify Additional Conflict Sources

### Check for Hidden Node.js Files
Look for and remove these files if they exist:
- `.node_version`
- `.nvmrc` (if not intentionally set)
- `.npmrc` (check contents, may contain conflicting settings)
- `npm-debug.log`
- `yarn-debug.log`
- `yarn-error.log`

### Check Global npm Configuration
```bash
# Check global npm configuration
npm config list

# Check for conflicting global packages
npm list -g --depth=0

# Clear global npm cache if needed
npm cache clean --force -g
```

## 🔧 Step 3: Clean Project Structure

### Files to Keep (DO NOT DELETE)
```
✅ src/ (entire directory)
✅ scripts/ (entire directory)
✅ migrations/ (entire directory)
✅ tests/ (entire directory)
✅ package.json
✅ tsconfig.json
✅ discloud.config
✅ .env
✅ *.md files (documentation)
✅ .gitignore
✅ jest.config.js
✅ fly.toml
```

### Files to Remove
```
❌ node_modules/
❌ dist/
❌ package-lock.json
❌ yarn.lock
❌ .npm/
❌ .cache/
❌ .nyc_output/
❌ npm-debug.log
❌ yarn-debug.log
❌ yarn-error.log
```

## 🔄 Step 4: Prepare for Clean Reinstallation

### Option A: Local Development Setup

#### 1. Install/Update Node.js
```bash
# Check current Node.js version
node --version
npm --version

# If Node.js is not installed or outdated:
# Download from https://nodejs.org/
# Install LTS version (recommended)
```

#### 2. Verify Installation
```bash
# Should show version 18.x or higher
node --version

# Should show version 9.x or higher
npm --version
```

#### 3. Clean npm Configuration
```bash
# Reset npm configuration to defaults
npm config delete prefix
npm config delete cache
npm config delete tmp

# Verify clean configuration
npm config list
```

#### 4. Install Dependencies
```bash
# Install all dependencies fresh
npm install

# Verify installation
npm list --depth=0
```

#### 5. Build Project
```bash
# Compile TypeScript
npm run build

# Verify build output
ls dist/
```

#### 6. Test Basic Functionality
```bash
# Run tests
npm test

# Test TypeScript compilation
npm run build

# Test bot startup (development mode)
npm run start:dev
```

### Option B: Discloud-Only Deployment

If you don't want to install Node.js locally:

#### 1. Verify File Structure
Ensure your project contains:
```
project/
├── src/
├── scripts/
├── package.json
├── tsconfig.json
├── discloud.config
└── .env (with your environment variables)
```

#### 2. Package for Upload
Create a zip file containing:
- All source code (src/ directory)
- Configuration files (package.json, tsconfig.json, discloud.config)
- Documentation files
- **DO NOT INCLUDE**: node_modules/, dist/, package-lock.json

#### 3. Upload to Discloud
- Upload the clean project files
- Set environment variables in Discloud dashboard
- Deploy

## 🎯 Step 5: Verification Steps

### After Local Installation
```bash
# 1. Check Node.js works
node --version

# 2. Check npm works
npm --version

# 3. Check dependencies installed
npm list --depth=0

# 4. Check TypeScript compilation
npm run build

# 5. Check if dist/ was created
ls dist/

# 6. Test bot startup
npm run start:dev
```

### After Discloud Deployment
1. Check Discloud logs for successful startup
2. Verify bot appears online in Discord
3. Test basic commands (like /balance)
4. Check MongoDB connection in logs

## 🚨 Troubleshooting Common Issues

### Issue: "npm command not found"
**Solution**: Node.js not installed or not in PATH
```bash
# Reinstall Node.js from https://nodejs.org/
# Restart terminal/command prompt
```

### Issue: "Permission denied" errors
**Solution**: Fix npm permissions
```bash
# On macOS/Linux:
sudo chown -R $(whoami) ~/.npm

# On Windows: Run command prompt as Administrator
```

### Issue: "Cannot find module" errors
**Solution**: Clean reinstall
```bash
rm -rf node_modules package-lock.json
npm install
```

### Issue: TypeScript compilation errors
**Solution**: Check tsconfig.json and dependencies
```bash
# Verify TypeScript is installed
npm list typescript

# Reinstall if missing
npm install --save-dev typescript
```

## 📋 Cleanup Checklist

Before attempting deployment again:

- [ ] Removed node_modules/ directory
- [ ] Removed dist/ directory  
- [ ] Removed package-lock.json
- [ ] Removed yarn.lock (if existed)
- [ ] Cleared npm cache
- [ ] Verified package.json is correct
- [ ] Backed up all source code
- [ ] Documented current configuration
- [ ] Tested local installation (if doing local development)
- [ ] Prepared clean files for Discloud upload

## 🔄 Re-enabling Auto-Deployment (Future)

After successful basic deployment:
1. Refer to DEPLOYMENT_CONFIGURATION_BACKUP.md
2. Uncomment auto-deployment code in src/main.ts
3. Update package.json and discloud.config as documented
4. Test locally before redeploying to Discloud

## 📞 Next Steps

1. **Complete the cleanup** using the instructions above
2. **Test basic deployment** without auto-deployment features
3. **Verify bot functionality** with manual command deployment
4. **Re-implement auto-deployment** once basic deployment works
5. **Document any additional issues** for future reference
