{"name": "economy-bot", "version": "2.0.0", "main": "dist/main.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:trade": "jest tests/trade", "build": "tsc", "build:watch": "tsc --watch", "start": "node dist/main.js", "start:dev": "ts-node src/main.ts", "start:compiled": "node dist/main.js", "start:legacy": "node dist/index.js", "dev": "ts-node src/main.ts", "dev:legacy": "ts-node src/index.ts", "deploy-commands": "ts-node src/deploy-commands.ts", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "format": "prettier --write src/**/*.ts", "migrate": "ts-node scripts/migrate.ts", "migrate:status": "npm run migrate status", "migrate:up": "npm run migrate up", "migrate:down": "npm run migrate down", "migrate:create": "npm run migrate create", "deploy": "ts-node scripts/deploy.ts", "deploy:dev": "npm run deploy deploy -e development", "deploy:staging": "npm run deploy deploy -e staging", "deploy:prod": "npm run deploy deploy -e production", "deploy:validate": "npm run deploy validate", "deploy:rollback": "npm run deploy rollback", "test:integration": "ts-node scripts/test-command-integration.ts", "test:auto-deploy": "ts-node scripts/test-auto-deployment.ts", "test:auto-deploy:full": "TEST_BUILD=true TEST_DEPLOY=true ts-node scripts/test-auto-deployment.ts", "verify:deployment": "ts-node scripts/verify-deployment.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@discordjs/rest": "^2.5.0", "discord-api-types": "^0.38.8", "discord.js": "^14.19.3", "dotenv": "^16.6.1", "mongodb": "^6.17.0", "mongoose": "^8.15.0", "node-cron": "^4.0.7", "winston": "^3.17.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^22.15.0", "commander": "^12.0.0", "jest": "^29.7.0", "mongodb-memory-server": "^9.1.6", "nodemon": "^3.1.10", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}