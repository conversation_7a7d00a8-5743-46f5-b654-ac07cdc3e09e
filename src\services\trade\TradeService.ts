/**
 * Trade Service
 * Core service for managing secure trades between users
 */

import mongoose from 'mongoose';
import { Client } from 'discord.js';
import { BaseService } from '../base/BaseService';
import { requireFeature } from '../../core/decorators';
import { DatabaseError, ValidationError } from '../../utils/errorHandler';
import { TRADE, VALIDATION } from '../../config/constants';
import { 
  Trade, 
  ITrade, 
  EscrowTransaction, 
  IEscrowTransaction,
  UserTradeStats,
  IUserTradeStats,
  TradeConfirmation,
  ITradeConfirmation,
  User,
  Transaction,
  TransactionType
} from '../../models';
import { EscrowManager } from './managers/EscrowManager';
import { TradeValidator } from './managers/TradeValidator';
import { TradeNotificationManager } from './managers/TradeNotificationManager';
import { TradeSecurityService } from './TradeSecurityService';

export interface TradeCreationParams {
  sellerId: string;
  buyerId: string;
  guildId: string;
  amount: number;
  itemDescription: string;
  notes?: string;
  initiatedBy: 'SELLER' | 'BUYER';
}

export interface TradeStateTransition {
  tradeId: string;
  fromState: string;
  toState: string;
  triggeredBy: string;
  reason?: string;
}

/**
 * Main Trade Service Class
 */
export class TradeService extends BaseService {
  private escrowManager: EscrowManager;
  private validator: TradeValidator;
  private notificationManager: TradeNotificationManager;
  private securityService: TradeSecurityService;

  constructor(app: any) {
    super('TradeService', app);
    this.escrowManager = new EscrowManager(app);
    this.validator = new TradeValidator(app);
    this.notificationManager = new TradeNotificationManager(app);
    this.securityService = new TradeSecurityService(app);
  }

  /**
   * Initialize the trade service
   */
  @requireFeature('TRADE_SYSTEM')
  async onInitialize(): Promise<void> {
    this.logger.info('[TradeService] Trade system initialized');
    
    // Initialize sub-managers
    await this.escrowManager.initialize();
    await this.validator.initialize();
    await this.notificationManager.initialize();
    await this.securityService.initialize();
  }

  /**
   * Create a new trade proposal
   */
  @requireFeature('TRADE_SYSTEM')
  async createTrade(params: TradeCreationParams, client?: Client): Promise<ITrade> {
    this.logOperation('Creating new trade', params);

    // Check rate limits and security
    const rateLimitCheck = await this.securityService.checkTradeRateLimit(params.sellerId, params.guildId);
    if (!rateLimitCheck.allowed) {
      throw new ValidationError(rateLimitCheck.reason || 'Rate limit exceeded');
    }

    const buyerRateLimitCheck = await this.securityService.checkTradeRateLimit(params.buyerId, params.guildId);
    if (!buyerRateLimitCheck.allowed) {
      throw new ValidationError(`Buyer ${buyerRateLimitCheck.reason || 'rate limit exceeded'}`);
    }

    // Validate trade creation
    await this.validator.validateTradeCreation(params);

    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Generate unique trade ID
        const tradeId = this.generateTradeId();
        
        // Calculate expiration time
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + TRADE.TRADE_EXPIRATION_HOURS);

        // Create trade record
        const trade = await Trade.create([{
          tradeId,
          sellerId: params.sellerId,
          buyerId: params.buyerId,
          guildId: params.guildId,
          amount: params.amount,
          itemDescription: params.itemDescription,
          notes: params.notes,
          state: TRADE.STATES.PROPOSED,
          initiatedBy: params.initiatedBy,
          expiresAt,
          escrowLocked: false,
          escrowAmount: 0,
          sellerConfirmed: false,
          buyerConfirmed: false,
          warningsSent: 0,
          extensionGranted: false
        }], { session });

        // Update user trade stats
        await this.updateUserTradeStats(params.sellerId, params.guildId, 'TRADE_INITIATED', session);
        await this.updateUserTradeStats(params.buyerId, params.guildId, 'TRADE_INITIATED', session);

        // Perform security check on the created trade
        const securityCheck = await this.securityService.performSecurityCheck(trade[0]);
        if (!securityCheck.passed) {
          this.logger.warn('Trade failed security check', {
            tradeId,
            violations: securityCheck.violations,
            riskLevel: securityCheck.riskLevel
          });

          // For now, we'll log but not block. In production, you might want to:
          // - Block CRITICAL risk trades
          // - Flag HIGH risk trades for manual review
          // - Apply additional restrictions
        }

        this.logOperation('Trade created successfully', { tradeId, state: 'PROPOSED' });

        // Send notifications (outside transaction)
        if (client) {
          setImmediate(async () => {
            try {
              await this.notificationManager.sendTradeProposal(trade[0], client);
            } catch (error) {
              this.handleError(error, { operation: 'trade_proposal_notification' });
            }
          });
        }

        return trade[0];
      });
    } catch (error) {
      this.handleError(error, { operation: 'create_trade', params });
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Accept a trade proposal
   */
  @requireFeature('TRADE_SYSTEM')
  async acceptTrade(tradeId: string, acceptingUserId: string, client?: Client): Promise<ITrade> {
    this.logOperation('Accepting trade', { tradeId, acceptingUserId });

    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Get and validate trade
        const trade = await Trade.findOne({ tradeId }).session(session);
        if (!trade) {
          throw new ValidationError('Trade not found');
        }

        // Validate acceptance
        await this.validator.validateTradeAcceptance(trade, acceptingUserId);

        // Check buyer has sufficient balance
        const buyer = await User.findOne({ discordId: trade.buyerId }).session(session);
        if (!buyer || buyer.balance < trade.amount) {
          throw new ValidationError('Insufficient balance to accept trade');
        }

        // Lock escrow funds
        await this.escrowManager.lockEscrow(trade, session);

        // Update trade state
        trade.state = TRADE.STATES.ACCEPTED;
        trade.acceptedAt = new Date();
        await trade.save({ session });

        // Create acceptance confirmation
        await TradeConfirmation.create([{
          confirmationId: this.generateConfirmationId(),
          tradeId: trade.tradeId,
          discordId: acceptingUserId,
          guildId: trade.guildId,
          confirmationType: 'TRADE_ACCEPTANCE',
          confirmed: true,
          confirmedAt: new Date()
        }], { session });

        // Transition to ACTIVE state
        await this.transitionToActive(trade, session);

        this.logOperation('Trade accepted and activated', { tradeId, state: 'ACTIVE' });

        // Send notifications (outside transaction)
        if (client) {
          setImmediate(async () => {
            try {
              await this.notificationManager.sendTradeAccepted(trade, client);
            } catch (error) {
              this.handleError(error, { operation: 'trade_accepted_notification' });
            }
          });
        }

        return trade;
      });
    } catch (error) {
      this.handleError(error, { operation: 'accept_trade', tradeId, acceptingUserId });
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Confirm trade completion by a party
   */
  @requireFeature('TRADE_SYSTEM')
  async confirmTrade(tradeId: string, confirmingUserId: string, client?: Client): Promise<{ trade: ITrade; completed: boolean }> {
    this.logOperation('Confirming trade', { tradeId, confirmingUserId });

    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Get and validate trade
        const trade = await Trade.findOne({ tradeId }).session(session);
        if (!trade) {
          throw new ValidationError('Trade not found');
        }

        // Validate confirmation
        await this.validator.validateTradeConfirmation(trade, confirmingUserId);

        // Update confirmation status
        if (trade.sellerId === confirmingUserId) {
          trade.sellerConfirmed = true;
          trade.sellerConfirmedAt = new Date();
        } else {
          trade.buyerConfirmed = true;
          trade.buyerConfirmedAt = new Date();
        }

        // Create confirmation record
        await TradeConfirmation.create([{
          confirmationId: this.generateConfirmationId(),
          tradeId: trade.tradeId,
          discordId: confirmingUserId,
          guildId: trade.guildId,
          confirmationType: 'TRADE_COMPLETION',
          confirmed: true,
          confirmedAt: new Date()
        }], { session });

        let completed = false;

        // Check if both parties confirmed
        if (trade.sellerConfirmed && trade.buyerConfirmed) {
          // Complete the trade
          await this.completeTrade(trade, session);
          completed = true;
        } else {
          // Save partial confirmation
          await trade.save({ session });
          
          // Extend expiration if only one party confirmed
          if (!trade.extensionGranted) {
            const newExpiration = new Date();
            newExpiration.setHours(newExpiration.getHours() + TRADE.PARTIAL_CONFIRMATION_EXTENSION_HOURS);
            trade.expiresAt = newExpiration;
            trade.extensionGranted = true;
            await trade.save({ session });
          }
        }

        this.logOperation('Trade confirmation processed', { 
          tradeId, 
          confirmingUserId, 
          completed,
          bothConfirmed: trade.sellerConfirmed && trade.buyerConfirmed 
        });

        // Send notifications (outside transaction)
        if (client) {
          setImmediate(async () => {
            try {
              if (completed) {
                await this.notificationManager.sendTradeCompleted(trade, client);
              } else {
                await this.notificationManager.sendPartialConfirmation(trade, confirmingUserId, client);
              }
            } catch (error) {
              this.handleError(error, { operation: 'trade_confirmation_notification' });
            }
          });
        }

        return { trade, completed };
      });
    } catch (error) {
      this.handleError(error, { operation: 'confirm_trade', tradeId, confirmingUserId });
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Cancel a trade
   */
  @requireFeature('TRADE_SYSTEM')
  async cancelTrade(tradeId: string, cancellingUserId: string, reason?: string, client?: Client): Promise<ITrade> {
    this.logOperation('Cancelling trade', { tradeId, cancellingUserId, reason });

    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Get and validate trade
        const trade = await Trade.findOne({ tradeId }).session(session);
        if (!trade) {
          throw new ValidationError('Trade not found');
        }

        // Validate cancellation
        await this.validator.validateTradeCancellation(trade, cancellingUserId);

        // Release any escrowed funds
        if (trade.escrowLocked) {
          await this.escrowManager.refundEscrow(trade, 'Trade cancelled', session);
        }

        // Update trade state
        trade.state = TRADE.STATES.CANCELLED;
        await trade.save({ session });

        // Update user stats
        await this.updateUserTradeStats(trade.sellerId, trade.guildId, 'TRADE_CANCELLED', session);
        await this.updateUserTradeStats(trade.buyerId, trade.guildId, 'TRADE_CANCELLED', session);

        this.logOperation('Trade cancelled', { tradeId, reason });

        // Send notifications (outside transaction)
        if (client) {
          setImmediate(async () => {
            try {
              await this.notificationManager.sendTradeCancelled(trade, cancellingUserId, reason, client);
            } catch (error) {
              this.handleError(error, { operation: 'trade_cancelled_notification' });
            }
          });
        }

        return trade;
      });
    } catch (error) {
      this.handleError(error, { operation: 'cancel_trade', tradeId, cancellingUserId });
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Get trade by ID
   */
  @requireFeature('TRADE_SYSTEM')
  async getTrade(tradeId: string): Promise<ITrade | null> {
    try {
      return await Trade.findOne({ tradeId }).lean();
    } catch (error) {
      this.handleError(error, { operation: 'get_trade', tradeId });
      throw error;
    }
  }

  /**
   * Get user's active trades
   */
  @requireFeature('TRADE_SYSTEM')
  async getUserActiveTrades(discordId: string, guildId?: string): Promise<ITrade[]> {
    try {
      const query: any = {
        $or: [
          { sellerId: discordId },
          { buyerId: discordId }
        ],
        state: { $in: [TRADE.STATES.PROPOSED, TRADE.STATES.ACCEPTED, TRADE.STATES.ACTIVE] }
      };

      if (guildId) {
        query.guildId = guildId;
      }

      return await Trade.find(query)
        .sort({ createdAt: -1 })
        .lean();
    } catch (error) {
      this.handleError(error, { operation: 'get_user_active_trades', discordId });
      throw error;
    }
  }

  // Private helper methods

  private async transitionToActive(trade: ITrade, session: mongoose.ClientSession): Promise<void> {
    trade.state = TRADE.STATES.ACTIVE;
    await trade.save({ session });

    // Update user stats for active trade
    await this.updateUserTradeStats(trade.sellerId, trade.guildId, 'TRADE_ACTIVATED', session);
    await this.updateUserTradeStats(trade.buyerId, trade.guildId, 'TRADE_ACTIVATED', session);
  }

  private async completeTrade(trade: ITrade, session: mongoose.ClientSession): Promise<void> {
    // Release escrow to seller
    await this.escrowManager.releaseEscrow(trade, 'Trade completed successfully', session);

    // Update trade state
    trade.state = TRADE.STATES.COMPLETED;
    trade.completedAt = new Date();
    await trade.save({ session });

    // Calculate completion time
    const completionTimeHours = trade.acceptedAt ? 
      (trade.completedAt.getTime() - trade.acceptedAt.getTime()) / (1000 * 60 * 60) : 0;

    // Update user stats
    await this.updateUserTradeStats(
      trade.sellerId,
      trade.guildId,
      'TRADE_COMPLETED',
      session,
      { tradeAmount: trade.amount, completionTimeHours, asSeller: true }
    );
    await this.updateUserTradeStats(
      trade.buyerId,
      trade.guildId,
      'TRADE_COMPLETED',
      session,
      { tradeAmount: trade.amount, completionTimeHours, asSeller: false }
    );

    // Check for automatic restrictions (outside transaction to avoid blocking)
    setImmediate(async () => {
      try {
        await this.securityService.applyAutomaticRestrictions(trade.sellerId, trade.guildId);
        await this.securityService.applyAutomaticRestrictions(trade.buyerId, trade.guildId);
      } catch (error) {
        this.logger.warn('Error applying automatic restrictions', { error, tradeId: trade.tradeId });
      }
    });
  }

  private async updateUserTradeStats(
    discordId: string,
    guildId: string,
    action: string,
    session: mongoose.ClientSession,
    tradeData?: any
  ): Promise<void> {
    try {
      // Get or create user trade stats
      let userStats = await UserTradeStats.findOne({ discordId, guildId }).session(session);

      if (!userStats) {
        userStats = new UserTradeStats({
          discordId,
          guildId,
          totalTrades: 0,
          successfulTrades: 0,
          cancelledTrades: 0,
          expiredTrades: 0,
          disputedTrades: 0,
          tradesAsSeller: 0,
          tradesAsBuyer: 0,
          totalVolumeTraded: 0,
          averageTradeValue: 0,
          largestTrade: 0,
          reputationScore: 50,
          disputeRatio: 0,
          completionRate: 0,
          averageCompletionTime: 0,
          fastestCompletion: 0,
          activeTrades: 0,
          isRestricted: false,
          dailyTradeCount: 0,
          lastTradeDate: new Date(),
          lastResetDate: new Date(),
          lastUpdated: new Date(),
          warningsReceived: 0,
          violationHistory: []
        });
      }

      // Update stats based on action
      switch (action) {
        case 'TRADE_INITIATED':
          userStats.resetDailyCountIfNeeded();
          userStats.dailyTradeCount++;
          userStats.lastTradeDate = new Date();
          break;

        case 'TRADE_ACTIVATED':
          userStats.activeTrades++;
          break;

        case 'TRADE_COMPLETED':
          if (tradeData) {
            userStats.updateAfterTrade(
              tradeData.tradeAmount,
              tradeData.completionTimeHours,
              true, // wasSuccessful
              false, // wasDisputed
              tradeData.asSeller
            );
          }
          break;

        case 'TRADE_CANCELLED':
          userStats.cancelledTrades++;
          userStats.activeTrades = Math.max(0, userStats.activeTrades - 1);
          break;

        case 'TRADE_EXPIRED':
          userStats.expiredTrades++;
          userStats.activeTrades = Math.max(0, userStats.activeTrades - 1);
          break;

        case 'TRADE_DISPUTED':
          userStats.disputedTrades++;
          break;
      }

      await userStats.save({ session });

      this.logOperation('User trade stats updated', {
        discordId,
        action,
        activeTrades: userStats.activeTrades,
        totalTrades: userStats.totalTrades
      });

    } catch (error) {
      this.handleError(error, { operation: 'update_user_trade_stats', discordId, action });
      // Don't throw here as this is a secondary operation
    }
  }

  private generateTradeId(): string {
    return `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateConfirmationId(): string {
    return `conf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
