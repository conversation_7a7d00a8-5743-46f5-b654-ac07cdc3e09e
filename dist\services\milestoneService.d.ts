import { Client } from 'discord.js';
export interface MilestoneCheckResult {
    achieved: boolean;
    milestoneType: string;
    category: string;
    rewardAmount: number;
    details: string;
    achievementValue: number;
    diminishingFactor: number;
    achievementCount: number;
}
/**
 * Main function to check and process milestones for a user
 */
export declare function checkAndProcessMilestones(client: Client, discordId: string, guildId: string, activityType: 'message' | 'voice' | 'reaction' | 'login', activityData?: any): Promise<MilestoneCheckResult[]>;
/**
 * Gets user milestone statistics
 */
export declare function getUserMilestoneStats(discordId: string, guildId: string): Promise<{
    totalAchievements: number;
    totalRewards: number;
    recentAchievements: any[];
    currentStreaks: any;
    weeklyProgress: any;
}>;
/**
 * Gets milestone leaderboard for a guild
 */
export declare function getMilestoneLeaderboard(guildId: string, limit?: number): Promise<any[]>;
/**
 * Creates default milestone configurations for a guild
 */
export declare function createDefaultMilestoneConfigurations(guildId: string): Promise<void>;
/**
 * Creates a comprehensive security report for a user
 */
export declare function getUserSecurityReport(discordId: string, guildId: string): Promise<{
    isBlacklisted: boolean;
    blacklistReason?: string;
    blacklistUntil?: Date;
    suspiciousActivityCount: number;
    lastSuspiciousActivity?: Date;
    recentRateLimits: number;
    securityScore: 'low' | 'medium' | 'high' | 'critical';
    recommendations: string[];
}>;
//# sourceMappingURL=milestoneService.d.ts.map