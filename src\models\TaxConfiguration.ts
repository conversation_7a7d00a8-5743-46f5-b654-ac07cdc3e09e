import { Schema, model, Document } from 'mongoose';

export interface ITaxConfiguration extends Document {
    guildId: string;
    enabled: boolean;
    frequency: number; // weeks between tax collection
    amount: number; // PLC amount to deduct
    roleId: string; // Discord role ID that gets taxed
    roleName: string; // Role name for display purposes
    lastTaxDate?: Date;
    nextTaxDate?: Date;
    createdAt: Date;
    updatedAt: Date;
}

const taxConfigurationSchema = new Schema<ITaxConfiguration>({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        unique: true,
        validate: {
            validator: function(v: string): boolean {
                return !!(v && v.trim().length > 0);
            },
            message: 'Guild ID cannot be empty'
        }
    },
    enabled: {
        type: Boolean,
        default: false,
        required: true
    },
    frequency: {
        type: Number,
        required: function(this: ITaxConfiguration) {
            return this.enabled;
        },
        min: [1, 'Tax frequency must be at least 1 week'],
        max: [52, 'Tax frequency cannot exceed 52 weeks'],
        validate: {
            validator: function(v: number): boolean {
                return Number.isInteger(v) && v > 0;
            },
            message: 'Tax frequency must be a positive integer'
        }
    },
    amount: {
        type: Number,
        required: function(this: ITaxConfiguration) {
            return this.enabled;
        },
        min: [1, 'Tax amount must be at least 1 PLC'],
        max: [10000, 'Tax amount cannot exceed 10,000 PLC'],
        validate: {
            validator: function(v: number): boolean {
                return Number.isInteger(v) && v > 0;
            },
            message: 'Tax amount must be a positive integer'
        }
    },
    roleId: {
        type: String,
        required: function(this: ITaxConfiguration) {
            return this.enabled;
        },
        validate: {
            validator: function(v: string): boolean {
                return !v || (v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Role ID must be a valid Discord snowflake'
        }
    },
    roleName: {
        type: String,
        required: function(this: ITaxConfiguration) {
            return this.enabled;
        },
        maxlength: [100, 'Role name cannot exceed 100 characters']
    },
    lastTaxDate: {
        type: Date,
        default: null
    },
    nextTaxDate: {
        type: Date,
        default: null
    }
}, {
    timestamps: true
});

// Index for efficient guild lookups
taxConfigurationSchema.index({ guildId: 1 });

// Index for tax scheduling queries
taxConfigurationSchema.index({ enabled: 1, nextTaxDate: 1 });

// Pre-save middleware to calculate next tax date
taxConfigurationSchema.pre('save', function(next) {
    if (this.enabled && this.frequency) {
        const now = new Date();
        if (!this.lastTaxDate) {
            // If no last tax date, set next tax date to frequency weeks from now
            this.nextTaxDate = new Date(now.getTime() + (this.frequency * 7 * 24 * 60 * 60 * 1000));
        } else {
            // Calculate next tax date based on last tax date
            this.nextTaxDate = new Date(this.lastTaxDate.getTime() + (this.frequency * 7 * 24 * 60 * 60 * 1000));
        }
    } else {
        this.nextTaxDate = undefined;
    }
    next();
});

export const TaxConfiguration = model<ITaxConfiguration>('TaxConfiguration', taxConfigurationSchema);
