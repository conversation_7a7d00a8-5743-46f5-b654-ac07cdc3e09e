import { <PERSON><PERSON><PERSON><PERSON>, Role, EmbedBuilder, ActionRowBuilder, ButtonBuilder } from 'discord.js';
import { IWelcomeTemplate } from '../models/WelcomeTemplate';
export interface AutoMessageProcessResult {
    sent: boolean;
    templatesProcessed: number;
    errors: string[];
}
export interface TestMessageResult {
    embed?: EmbedBuilder;
    content?: string;
    components?: ActionRowBuilder<ButtonBuilder>[];
}
/**
 * Processes automated messages for member join events
 */
export declare function processJoinMessage(member: GuildMember): Promise<AutoMessageProcessResult>;
/**
 * Processes automated messages for role change events
 */
export declare function processRoleChangeMessage(member: GuildMember, role: Role, triggerType: 'role_add' | 'role_remove'): Promise<AutoMessageProcessResult>;
/**
 * Creates a test message for preview
 */
export declare function processTestMessage(member: GuildMember, template: IWelcomeTemplate): Promise<TestMessageResult>;
//# sourceMappingURL=automessageService.d.ts.map