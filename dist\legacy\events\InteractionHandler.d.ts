/**
 * Legacy Interaction Handler
 * Extracted interaction handling from monolithic index.ts
 */
import { Client, Interaction } from 'discord.js';
/**
 * Interaction handler for legacy compatibility
 */
export declare class LegacyInteractionHandler {
    private client;
    constructor(client: Client);
    /**
     * Handle interaction create events
     */
    handleInteraction(interaction: Interaction): Promise<void>;
    /**
     * Handle chat input command interactions
     */
    private handleChatInputCommand;
    /**
     * Handle button interactions
     */
    private handleButtonInteraction;
    /**
     * Handle quick command buttons
     */
    private handleQuickCommand;
    /**
     * Handle role achievement info buttons
     */
    private handleRoleAchievementInfo;
    /**
     * Handle announcement confirmation
     */
    private handleAnnouncementConfirm;
    /**
     * Handle announcement cancellation
     */
    private handleAnnouncementCancel;
    /**
     * Handle help command buttons
     */
    private handleHelpButton;
}
export default LegacyInteractionHandler;
//# sourceMappingURL=InteractionHandler.d.ts.map