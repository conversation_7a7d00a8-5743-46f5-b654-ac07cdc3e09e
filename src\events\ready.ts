/**
 * Ready Event Handler
 * Handles Discord client ready event and initializes scheduled tasks
 */

import cron from 'node-cron';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';
import { SCHEDULES } from '../config/constants';

/**
 * Ready event handler
 */
export class Ready<PERSON>ventHandler extends BaseEventHandler {
  public readonly name = 'ready';
  public readonly once = true;

  constructor(app: IApplicationContext) {
    super(app, 'ready');
  }

  /**
   * Execute ready event
   */
  async execute(): Promise<void> {
    try {
      this.logger.info(`[Ready] Bot logged in as ${this.app.client.user?.tag}`);
      
      // Initialize scheduled tasks
      await this.initializeScheduledTasks();

      // Initialize trade background service with client
      await this.initializeTradeBackgroundService();

      this.logger.info('[Ready] Bot is ready and all systems initialized');
    } catch (error) {
      this.handleError(error, { event: 'ready' });
    }
  }

  /**
   * Initialize scheduled cron jobs
   */
  private async initializeScheduledTasks(): Promise<void> {
    try {
      // Tax collection cron job
      if (this.isFeatureEnabled('TAX_SYSTEM')) {
        this.initializeTaxCollection();
      }

      // Milestone tracking cron job
      if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
        this.initializeMilestoneTracking();
      }

      // User cleanup cron job
      if (this.isFeatureEnabled('USER_CLEANUP')) {
        this.initializeUserCleanup();
      }

      // Audit cleanup cron job
      if (this.isFeatureEnabled('AUDIT_LOGGING')) {
        this.initializeAuditCleanup();
      }

      this.logger.info('[Ready] Scheduled tasks initialized');
    } catch (error) {
      this.logger.error('[Ready] Failed to initialize scheduled tasks', { error });
    }
  }

  /**
   * Initialize tax collection cron job
   */
  private initializeTaxCollection(): void {
    cron.schedule(SCHEDULES.TAX_COLLECTION, async () => {
      this.logger.info('[Tax Collection] Running scheduled tax collection check...');

      try {
        // Import here to avoid circular dependencies
        const { processTaxCollection } = await import('../services/taxService');

        // Process tax collection for all guilds the bot is in
        for (const [guildId, guild] of this.app.client.guilds.cache) {
          try {
            const result = await processTaxCollection(this.app.client, guildId);
            if (result.totalProcessed > 0) {
              this.logger.info(`[Tax Collection] Guild ${guild.name}: Processed ${result.totalProcessed}, Taxed ${result.totalTaxed}, Roles Removed ${result.totalRolesRemoved}`);
              if (result.errors.length > 0) {
                this.logger.error(`[Tax Collection] Guild ${guild.name} errors:`, result.errors);
              }
            }
          } catch (error) {
            this.logger.error(`[Tax Collection] Failed for guild ${guild.name}:`, error);
          }
        }
      } catch (error) {
        this.logger.error('[Tax Collection] Cron job error:', error);
      }
    }, {
      timezone: "UTC"
    });

    this.logger.info('[Tax Collection] Cron job initialized - running every hour');
  }

  /**
   * Initialize milestone tracking cron job
   */
  private initializeMilestoneTracking(): void {
    cron.schedule(SCHEDULES.MILESTONE_RESET, async () => {
      this.logger.info('[Milestone Tracking] Running daily milestone check...');

      try {
        // Process login streak tracking for all guilds
        for (const [guildId, guild] of this.app.client.guilds.cache) {
          try {
            // This will be handled by the milestone service when users are active
            // The cron job mainly serves as a daily reset trigger
            this.logger.debug(`[Milestone Tracking] Daily reset processed for guild ${guild.name}`);
          } catch (error) {
            this.logger.error(`[Milestone Tracking] Failed for guild ${guild.name}:`, error);
          }
        }
      } catch (error) {
        this.logger.error('[Milestone Tracking] Cron job error:', error);
      }
    }, {
      timezone: "UTC"
    });

    this.logger.info('[Milestone Tracking] Daily cron job initialized - running at midnight UTC');
  }

  /**
   * Initialize user cleanup cron job
   */
  private initializeUserCleanup(): void {
    cron.schedule(SCHEDULES.USER_CLEANUP, async () => {
      this.logger.info('[User Cleanup] Running scheduled cleanup check...');

      try {
        // Import here to avoid circular dependencies
        const { UserCleanupService } = await import('../services/userCleanupService');

        // Perform maintenance cleanup tasks
        const stats = await UserCleanupService.cleanupOrphanedData();
        this.logger.info('[User Cleanup] Orphaned data cleanup completed', stats);
      } catch (error) {
        this.logger.error('[User Cleanup] Cron job error:', error);
      }
    }, {
      timezone: "UTC"
    });

    this.logger.info('[User Cleanup] Cron job initialized - running daily at 2 AM UTC');
  }

  /**
   * Initialize audit cleanup cron job
   */
  private initializeAuditCleanup(): void {
    cron.schedule(SCHEDULES.AUDIT_CLEANUP, async () => {
      this.logger.info('[Audit Cleanup] Running scheduled audit cleanup...');

      try {
        // Import here to avoid circular dependencies
        const { MilestoneAuditService } = await import('../services/milestoneAuditService');

        // Clean up old audit logs (older than 90 days)
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 90);

        const cleanupStats = await MilestoneAuditService.cleanupOldLogs(cutoffDate.getTime());
        this.logger.info('[Audit Cleanup] Cleanup completed', cleanupStats);
      } catch (error) {
        this.logger.error('[Audit Cleanup] Cron job error:', error);
      }
    }, {
      timezone: "UTC"
    });

    this.logger.info('[Audit Cleanup] Cron job initialized - running weekly on Sunday at 3 AM UTC');
  }

  /**
   * Initialize trade background service with Discord client
   */
  private async initializeTradeBackgroundService(): Promise<void> {
    try {
      if (this.isFeatureEnabled('TRADE_SYSTEM')) {
        const tradeBackgroundService = this.app.getService('TradeBackgroundService');
        if (tradeBackgroundService && typeof tradeBackgroundService.setClient === 'function') {
          tradeBackgroundService.setClient(this.app.client);
          this.logger.info('[Ready] Trade background service initialized with Discord client');
        }
      }
    } catch (error) {
      this.logger.error('[Ready] Failed to initialize trade background service', { error });
    }
  }
}
