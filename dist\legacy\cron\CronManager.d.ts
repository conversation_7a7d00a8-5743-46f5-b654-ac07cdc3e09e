/**
 * Legacy Cron Manager
 * Extracted cron job management from monolithic index.ts
 */
import { Client } from 'discord.js';
/**
 * Cron job manager for legacy compatibility
 */
export declare class LegacyCronManager {
    private client;
    private jobs;
    constructor(client: Client);
    /**
     * Initialize all cron jobs
     */
    initializeJobs(): void;
    /**
     * Initialize tax collection cron job (runs every hour)
     */
    private initializeTaxCollection;
    /**
     * Initialize milestone tracking cron job (runs daily at midnight UTC)
     */
    private initializeMilestoneTracking;
    /**
     * Stop a specific cron job
     */
    stopJob(jobName: string): boolean;
    /**
     * Start a specific cron job
     */
    startJob(jobName: string): boolean;
    /**
     * Stop all cron jobs
     */
    stopAllJobs(): void;
    /**
     * Start all cron jobs
     */
    startAllJobs(): void;
    /**
     * Destroy all cron jobs
     */
    destroyAllJobs(): void;
    /**
     * Get job status
     */
    getJobStatus(jobName: string): string | null;
    /**
     * Get all job statuses
     */
    getAllJobStatuses(): Record<string, string>;
    /**
     * Get job count
     */
    getJobCount(): number;
}
export default LegacyCronManager;
//# sourceMappingURL=CronManager.d.ts.map