{"version": 3, "file": "messageReactionAdd.js", "sourceRoot": "", "sources": ["../../src/events/messageReactionAdd.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,iCAA0C;AAG1C;;GAEG;AACH,MAAa,8BAA+B,SAAQ,uBAAgB;IAGlE,YAAY,GAAwB;QAClC,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;QAHnB,SAAI,GAAG,oBAAoB,CAAC;IAI5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,QAAkD,EAAE,IAAwB;QACxF,IAAI,CAAC;YACH,qCAAqC;YACrC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAEjD,IAAI,CAAC,YAAY,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,iBAAiB;YACjB,IAAI,QAAQ,CAAC,GAAG;gBAAE,OAAO;YAEzB,2BAA2B;YAC3B,IAAI,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC9C,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAC3D,CAAC;YAED,yCAAyC;YACzC,IAAI,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC9C,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAC3D,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;gBACtB,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC9B,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACtC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE;gBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI;aAC3B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,QAAkD;QACjF,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,IAAI,CAAC;gBACH,OAAO,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC9E,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,QAA2B,CAAC;IACrC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,IAAwB;QACnD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC;gBACH,OAAO,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC1E,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,IAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,QAAyB,EAAE,IAAU;QACvE,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,EAAE,qBAAqB,EAAE,GAAG,wDAAa,oCAAoC,GAAC,CAAC;YAErF,MAAM,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uDAAuD,EAAE;gBACzE,KAAK;gBACL,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC9B,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI;aAC3B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,QAAyB,EAAE,IAAU;QACvE,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,EAAE,yBAAyB,EAAE,GAAG,wDAAa,8BAA8B,GAAC,CAAC;YAEnF,MAAM,gBAAgB,GAAG,MAAM,yBAAyB,CACtD,IAAI,CAAC,GAAG,CAAC,MAAM,EACf,IAAI,CAAC,EAAE,EACP,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,EAChC,UAAU,EACV;gBACE,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACtC,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CACF,CAAC;YAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,IAAI,CAAC,GAAG,aAAa,gBAAgB,CAAC,MAAM,sCAAsC,CAAC,CAAC;YACpI,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2DAA2D,EAAE;gBAC7E,KAAK;gBACL,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC9B,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AA7HD,wEA6HC"}