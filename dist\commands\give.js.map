{"version": 3, "file": "give.js", "sourceRoot": "", "sources": ["../../src/commands/give.ts"], "names": [], "mappings": ";;AAAA,2CAAmG;AACnG,+DAA2D;AAC3D,wDAA2F;AAC3F,wDAA+G;AAE/G,MAAM,CAAC,OAAO,GAAG;IACb,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC1B,OAAO,CAAC,MAAM,CAAC;SACf,cAAc,CAAC,mCAAmC,CAAC;SACnD,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACzG,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACvG,2BAA2B,CAAC,gCAAmB,CAAC,aAAa,CAAC;IACnE,OAAO,EAAE,IAAA,+BAAgB,EAAC,KAAK,EAAE,WAAwC,EAAE,EAAE;QACzE,mBAAmB;QACnB,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,gCAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,8BAAe,EAAE,CAAC;QAChC,CAAC;QAED,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAE9D,mBAAmB;QACnB,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;YACd,MAAM,IAAI,8BAAe,CAAC,mCAAmC,CAAC,CAAC;QACnE,CAAC;QACD,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC;YACjB,MAAM,IAAI,8BAAe,CAAC,iCAAiC,CAAC,CAAC;QACjE,CAAC;QAED,kFAAkF;QAClF,MAAM,IAAA,8BAAa,EACf,UAAU,CAAC,EAAE,EACb,MAAM,EACN,MAAM,EACN,kBAAkB,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,EACxC,WAAW,CAAC,MAAM,EAClB,WAAW,CAAC,KAAK,EAAE,EAAE,CACxB,CAAC;QAEF,kCAAkC;QAClC,MAAM,KAAK,GAAG,IAAA,iCAAkB,EAAC,6BAA6B,CAAC;aAC1D,cAAc,CACX,GAAG,qBAAM,CAAC,KAAK,CAAC,MAAM,0CAA0C;YAChE,GAAG,IAAA,0BAAW,EAAC,MAAM,CAAC,0BAA0B,UAAU,CAAC,WAAW,KAAK,CAC9E;aACA,SAAS,CACN;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,GAAG,gBAAgB;YACzC,KAAK,EAAE,KAAK,WAAW,CAAC,IAAI,CAAC,WAAW,IAAI;YAC5C,MAAM,EAAE,IAAI;SACf,EACD;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,MAAM,YAAY;YAC1C,KAAK,EAAE,KAAK,UAAU,CAAC,WAAW,IAAI;YACtC,MAAM,EAAE,IAAI;SACf,EACD;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,iBAAiB;YAC9C,KAAK,EAAE,IAAA,0BAAW,EAAC,MAAM,CAAC;YAC1B,MAAM,EAAE,IAAI;SACf,EACD;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,MAAM,sBAAsB;YACjD,KAAK,EAAE,0BAA0B,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE;YAC/D,MAAM,EAAE,KAAK;SAChB,EACD;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,KAAK,mBAAmB;YAC7C,KAAK,EAAE,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK;YAC/C,MAAM,EAAE,KAAK;SAChB,CACJ;aACA,SAAS,CAAC;YACP,IAAI,EAAE,qDAAqD;SAC9D,CAAC,CAAC;QAEP,8BAA8B;QAC9B,IAAA,0BAAW,EAAC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;QAErC,MAAM,WAAW,CAAC,KAAK,CAAC;YACpB,MAAM,EAAE,CAAC,KAAK,CAAC;YACf,SAAS,EAAE,KAAK;SACnB,CAAC,CAAC;IACP,CAAC,CAAC;CACL,CAAC"}