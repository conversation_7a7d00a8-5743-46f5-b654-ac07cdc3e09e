{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/events/base.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,2CAA8C;AAE9C;;GAEG;AACH,MAAsB,gBAAgB;IAOpC,YAAY,GAAwB,EAAE,SAAiB;QALvC,SAAI,GAAa,KAAK,CAAC;QAMrC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,IAAA,qBAAY,EAAC,SAAS,SAAS,EAAE,CAAC,CAAC;IACnD,CAAC;IAOD;;OAEG;IACO,WAAW,CAAC,KAAU,EAAE,OAAa;QAC7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,uBAAuB,EAAE;YACtD,KAAK;YACL,OAAO;YACP,SAAS,EAAE,IAAI,CAAC,IAAI;SACrB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,OAAe,EAAE,IAAU;QAChD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,WAAmB;QAC5C,6CAA6C;QAC7C,MAAM,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;QACjD,OAAO,eAAe,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;CACF;AA3CD,4CA2CC;AAED;;GAEG;AACH,MAAa,oBAAoB;IAI/B;QAHQ,aAAQ,GAAG,IAAI,GAAG,EAA4B,CAAC;QAIrD,IAAI,CAAC,MAAM,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,OAAyB;QAChC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+CAA+C,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YAChF,OAAO;QACT,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAY;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAY;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,IAAI,EAAE,CAAC,CAAC;QACrE,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;IAC5D,CAAC;CACF;AArDD,oDAqDC;AAED;;GAEG;AACU,QAAA,aAAa,GAAG,IAAI,oBAAoB,EAAE,CAAC"}