import { Schema, model, Document } from 'mongoose';

export interface IReactionReward extends Document {
    userId: string;
    messageId: string;
    channelId: string;
    coinsAwarded: number;
    timestamp: Date;
}

const reactionRewardSchema = new Schema<IReactionReward>({
    userId: {
        type: String,
        required: [true, 'User ID is required'],
        validate: {
            validator: function(v: string): boolean {
                return !!(v && v.trim().length > 0);
            },
            message: 'User ID cannot be empty'
        }
    },
    messageId: {
        type: String,
        required: [true, 'Message ID is required'],
        validate: {
            validator: function(v: string): boolean {
                return !!(v && v.trim().length > 0);
            },
            message: 'Message ID cannot be empty'
        }
    },
    channelId: {
        type: String,
        required: [true, 'Channel ID is required'],
        validate: {
            validator: function(v: string): boolean {
                return !!(v && v.trim().length > 0);
            },
            message: 'Channel ID cannot be empty'
        }
    },
    coinsAwarded: {
        type: Number,
        required: true,
        min: [0, 'Coins awarded cannot be negative'],
        default: 5
    },
    timestamp: {
        type: Date,
        default: Date.now,
        required: true
    }
});

// Compound index to ensure one reward per user per message
reactionRewardSchema.index({ userId: 1, messageId: 1 }, { unique: true });

// Index for efficient user lookups and rate limiting
reactionRewardSchema.index({ userId: 1, timestamp: -1 });

// Index for message age queries
reactionRewardSchema.index({ messageId: 1, timestamp: -1 });

// Index for channel-based queries
reactionRewardSchema.index({ channelId: 1, timestamp: -1 });

export const ReactionReward = model<IReactionReward>('ReactionReward', reactionRewardSchema);
