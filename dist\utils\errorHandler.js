"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BalanceError = exports.RateLimitError = exports.InsufficientFundsError = exports.PermissionError = exports.ValidationError = exports.DatabaseError = exports.CommandError = void 0;
exports.handleCommandError = handleCommandError;
exports.handleButtonError = handleButtonError;
exports.withErrorHandler = withErrorHandler;
const discord_js_1 = require("discord.js");
const mongoose_1 = __importDefault(require("mongoose"));
const embedBuilder_1 = require("./embedBuilder");
// Generate unique error ID for tracking
function generateErrorId() {
    return `ERR-${Date.now().toString(36)}-${Math.random().toString(36).substr(2, 5)}`.toUpperCase();
}
class CommandError extends Error {
    constructor(message, ephemeral = true, debug, category = 'General Error', userGuidance) {
        super(message);
        this.name = 'CommandError';
        this.ephemeral = ephemeral;
        this.debug = debug;
        this.category = category;
        this.userGuidance = userGuidance;
    }
}
exports.CommandError = CommandError;
class DatabaseError extends CommandError {
    constructor(message, originalError) {
        let detailedMessage = message;
        let debugInfo = '';
        let guidance = 'Please try again in a moment. If the issue persists, contact an administrator.';
        if (originalError instanceof Error) {
            const err = originalError;
            // Handle specific MongoDB error codes
            if (err.code === 11000) {
                detailedMessage = 'A database conflict occurred. Please try again.';
                debugInfo = `Duplicate key error: ${err.message}`;
                guidance = 'This operation conflicts with existing data. Please try again or contact support.';
            }
            else if (err.name === 'ValidationError') {
                detailedMessage = 'Invalid data format. Please check your input.';
                debugInfo = `Validation error: ${err.message}`;
                guidance = 'Please verify your input format and try again.';
            }
            else if (err.name === 'MongoServerError') {
                detailedMessage = 'Database operation failed. Please try again.';
                debugInfo = `MongoDB error: ${err.message} (Code: ${err.code})`;
            }
            else {
                debugInfo = `${err.name}: ${err.message}`;
                if (err.stack) {
                    debugInfo += `\nStack: ${err.stack}`;
                }
            }
        }
        else if (originalError) {
            debugInfo = String(originalError);
        }
        super(detailedMessage, true, debugInfo, 'Database Error', guidance);
        this.name = 'DatabaseError';
    }
}
exports.DatabaseError = DatabaseError;
class ValidationError extends CommandError {
    constructor(message, field, expectedFormat) {
        let guidance = 'Please check your input and try again.';
        if (field && expectedFormat) {
            guidance = `The "${field}" field ${expectedFormat}`;
        }
        super(message, true, undefined, 'Validation Error', guidance);
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
class PermissionError extends CommandError {
    constructor(message = 'You do not have permission to use this command.', requiredPermission) {
        const guidance = requiredPermission
            ? `You need the "${requiredPermission}" permission to use this command.`
            : 'Contact an administrator if you believe you should have access to this command.';
        super(message, true, undefined, 'Permission Error', guidance);
        this.name = 'PermissionError';
    }
}
exports.PermissionError = PermissionError;
class InsufficientFundsError extends CommandError {
    constructor(required, available) {
        const message = `Insufficient funds! You need ${required} PLC but only have ${available} PLC.`;
        const guidance = 'Earn more coins through playing time, contributions, or other activities. Use /help to see earning opportunities.';
        super(message, true, undefined, 'Insufficient Funds', guidance);
        this.name = 'InsufficientFundsError';
    }
}
exports.InsufficientFundsError = InsufficientFundsError;
class RateLimitError extends CommandError {
    constructor(retryAfter) {
        const message = `You're doing that too quickly! Please wait ${retryAfter} seconds before trying again.`;
        const guidance = 'Take a short break and try again in a moment.';
        super(message, true, undefined, 'Rate Limit', guidance);
        this.name = 'RateLimitError';
    }
}
exports.RateLimitError = RateLimitError;
// Legacy alias for backward compatibility
class BalanceError extends InsufficientFundsError {
    constructor(message, required, available) {
        if (required !== undefined && available !== undefined) {
            super(required, available);
        }
        else {
            super(0, 0);
            // Override with custom message for legacy usage
            this.message = message;
        }
        this.name = 'BalanceError';
    }
}
exports.BalanceError = BalanceError;
// Enhanced logging function
function logError(error, context) {
    const timestamp = context.timestamp.toISOString();
    const logPrefix = `[${timestamp}] [${context.errorId}]`;
    console.error(`${logPrefix} Command Error in "${context.commandName || 'Unknown'}":`);
    console.error(`${logPrefix} User: ${context.username} (${context.userId})`);
    console.error(`${logPrefix} Guild: ${context.guildId || 'DM'}`);
    console.error(`${logPrefix} Channel: ${context.channelId}`);
    if (context.parameters && Object.keys(context.parameters).length > 0) {
        console.error(`${logPrefix} Parameters:`, JSON.stringify(context.parameters, null, 2));
    }
    if (error instanceof CommandError) {
        console.error(`${logPrefix} Category: ${error.category}`);
        console.error(`${logPrefix} Message: ${error.message}`);
        if (error.debug) {
            console.error(`${logPrefix} Debug Info: ${error.debug}`);
        }
    }
    else if (error instanceof Error) {
        console.error(`${logPrefix} Error: ${error.name}: ${error.message}`);
        if (error.stack) {
            console.error(`${logPrefix} Stack: ${error.stack}`);
        }
    }
    else {
        console.error(`${logPrefix} Unknown Error: ${String(error)}`);
    }
    // Log database connection status
    const dbStatus = mongoose_1.default.connection.readyState;
    const dbStates = ['disconnected', 'connected', 'connecting', 'disconnecting'];
    console.error(`${logPrefix} Database Status: ${dbStates[dbStatus] || 'unknown'}`);
}
// Check if user has admin permissions
function isAdmin(interaction) {
    if (!interaction.guild || !interaction.member)
        return false;
    const member = interaction.member;
    return member.permissions?.has?.(discord_js_1.PermissionFlagsBits.Administrator) || false;
}
function formatError(error, context, interaction) {
    // Log the error with full context
    logError(error, context);
    const isUserAdmin = isAdmin(interaction);
    if (error instanceof CommandError) {
        // Create user-friendly embed
        const embed = (0, embedBuilder_1.createErrorEmbed)(error.category, error.message);
        if (error.userGuidance) {
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} What can you do?`,
                value: error.userGuidance,
                inline: false
            });
        }
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.ID} Error Reference`,
            value: `\`${context.errorId}\``,
            inline: true
        });
        // Add admin-only debug information
        if (isUserAdmin && error.debug) {
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.TOOLS} Debug Information (Admin Only)`,
                value: `\`\`\`\n${error.debug.substring(0, 1000)}\`\`\``,
                inline: false
            });
        }
        return {
            embeds: [embed],
            ephemeral: error.ephemeral
        };
    }
    if (error instanceof Error) {
        let category = 'System Error';
        let message = 'An unexpected error occurred while processing your command.';
        let guidance = 'Please try again in a moment. If the issue persists, contact an administrator.';
        // Handle specific error types
        if (error.code === 11000) {
            category = 'Database Conflict';
            message = 'A database conflict occurred. Please try again.';
            guidance = 'This operation conflicts with existing data. Please try again or contact support.';
        }
        else if (error.name === 'ValidationError') {
            category = 'Validation Error';
            message = 'Invalid data format detected.';
            guidance = 'Please verify your input format and try again.';
        }
        else if (error.name === 'MongoServerError') {
            category = 'Database Error';
            message = 'Database operation failed.';
        }
        const embed = (0, embedBuilder_1.createErrorEmbed)(category, message);
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} What can you do?`,
            value: guidance,
            inline: false
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.ID} Error Reference`,
            value: `\`${context.errorId}\``,
            inline: true
        });
        // Add admin-only debug information
        if (isUserAdmin) {
            const debugInfo = `${error.name}: ${error.message}`;
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.TOOLS} Debug Information (Admin Only)`,
                value: `\`\`\`\n${debugInfo.substring(0, 1000)}\`\`\``,
                inline: false
            });
        }
        return {
            embeds: [embed],
            ephemeral: true
        };
    }
    // Default error response for unknown errors
    const embed = (0, embedBuilder_1.createErrorEmbed)('Unknown Error', 'An unexpected error occurred while processing your command.');
    embed.addFields({
        name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} What can you do?`,
        value: 'Please try again in a moment. If the issue persists, contact an administrator.',
        inline: false
    }, {
        name: `${embedBuilder_1.EMOJIS.MISC.ID} Error Reference`,
        value: `\`${context.errorId}\``,
        inline: true
    });
    if (isUserAdmin) {
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.ADMIN.TOOLS} Debug Information (Admin Only)`,
            value: `\`\`\`\n${String(error).substring(0, 1000)}\`\`\``,
            inline: false
        });
    }
    return {
        embeds: [embed],
        ephemeral: true
    };
}
// Extract command parameters from interaction
function extractParameters(interaction) {
    const params = {};
    if ('options' in interaction && interaction.options) {
        // For slash commands, extract all options
        const options = interaction.options;
        if (options.data) {
            for (const option of options.data) {
                params[option.name] = option.value;
            }
        }
    }
    if ('customId' in interaction && interaction.customId) {
        // For button interactions, include the custom ID
        params.customId = interaction.customId;
    }
    return params;
}
async function handleCommandError(interaction, error) {
    const context = {
        commandName: interaction.commandName,
        userId: interaction.user.id,
        username: interaction.user.username,
        guildId: interaction.guild?.id,
        channelId: interaction.channel?.id || 'unknown',
        parameters: extractParameters(interaction),
        timestamp: new Date(),
        errorId: generateErrorId()
    };
    const response = formatError(error, context, interaction);
    try {
        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(response);
        }
        else {
            await interaction.reply(response);
        }
    }
    catch (replyError) {
        console.error(`[${context.errorId}] Error while sending error response:`, replyError);
        // Last resort error handling
        try {
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: `An error occurred while processing your command. Error ID: \`${context.errorId}\``,
                    ephemeral: true
                });
            }
        }
        catch (finalError) {
            console.error(`[${context.errorId}] Final error handling failed:`, finalError);
        }
    }
}
async function handleButtonError(interaction, error) {
    const context = {
        commandName: `Button: ${interaction.customId}`,
        userId: interaction.user.id,
        username: interaction.user.username,
        guildId: interaction.guild?.id,
        channelId: interaction.channel?.id || 'unknown',
        parameters: extractParameters(interaction),
        timestamp: new Date(),
        errorId: generateErrorId()
    };
    const response = formatError(error, context, interaction);
    try {
        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(response);
        }
        else {
            await interaction.reply(response);
        }
    }
    catch (replyError) {
        console.error(`[${context.errorId}] Error while sending button error response:`, replyError);
        // Last resort error handling
        try {
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: `An error occurred while processing your button interaction. Error ID: \`${context.errorId}\``,
                    ephemeral: true
                });
            }
        }
        catch (finalError) {
            console.error(`[${context.errorId}] Final button error handling failed:`, finalError);
        }
    }
}
function withErrorHandler(commandFn) {
    return async (interaction) => {
        try {
            await commandFn(interaction);
        }
        catch (error) {
            await handleCommandError(interaction, error);
        }
    };
}
//# sourceMappingURL=errorHandler.js.map