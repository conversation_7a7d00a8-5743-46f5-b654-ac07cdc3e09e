import { GuildMember, PartialGuildMember } from 'discord.js';
export interface CleanupResult {
    success: boolean;
    userDataRemoved: boolean;
    transactionsRemoved: number;
    reactionRewardsRemoved: number;
    errors: string[];
    timeTaken: number;
}
/**
 * Comprehensive cleanup service for removing user data when members leave the server
 */
export declare class UserCleanupService {
    private static readonly CLEANUP_TIMEOUT_MS;
    private static readonly GRACE_PERIOD_MS;
    /**
     * Main cleanup function that removes all user data when a member leaves
     */
    static cleanupUserData(member: GuildMember | PartialGuildMember): Promise<CleanupResult>;
    /**
     * Check if user has any data that would be cleaned up (for testing/verification)
     */
    static checkUserData(userId: string): Promise<{
        hasUserRecord: boolean;
        transactionCount: number;
        reactionRewardCount: number;
    }>;
    /**
     * Cleanup orphaned data (for maintenance - removes data for users not in any guild)
     * This is a separate maintenance function, not part of the member leave cleanup
     */
    static cleanupOrphanedData(): Promise<{
        orphanedUsers: number;
        orphanedTransactions: number;
        orphanedReactionRewards: number;
    }>;
}
//# sourceMappingURL=userCleanupService.d.ts.map