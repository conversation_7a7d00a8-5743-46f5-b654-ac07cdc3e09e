"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getIncomeGuideText = getIncomeGuideText;
exports.setIncomeGuideText = setIncomeGuideText;
exports.hasCustomIncomeGuide = hasCustomIncomeGuide;
exports.removeCustomIncomeGuide = removeCustomIncomeGuide;
exports.getDefaultIncomeGuideText = getDefaultIncomeGuideText;
const IncomeGuide_1 = __importDefault(require("../models/IncomeGuide"));
const errorHandler_1 = require("../utils/errorHandler");
/**
 * Formats text to properly handle line breaks for Discord display
 * @param text Raw text input
 * @returns Formatted text with proper line breaks
 */
function formatTextForDiscord(text) {
    // Replace literal \n with actual line breaks
    let formatted = text.replace(/\\n/g, '\n');
    // Normalize line breaks (handle different OS line endings)
    formatted = formatted.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    // Ensure proper spacing - replace multiple consecutive newlines with double newlines
    // This preserves intentional empty lines while preventing excessive spacing
    formatted = formatted.replace(/\n{3,}/g, '\n\n');
    // Trim leading/trailing whitespace but preserve internal formatting
    formatted = formatted.trim();
    return formatted;
}
/**
 * Processes user input text to handle various line break formats
 * @param rawText Raw text from user input
 * @returns Processed text ready for storage
 */
function processUserInput(rawText) {
    // First, handle escaped newlines
    let processed = rawText.replace(/\\n/g, '\n');
    // Handle Discord's text input quirks - sometimes line breaks come as literal \n
    processed = processed.replace(/\\\\n/g, '\n');
    // Normalize all line break types
    processed = processed.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    // Clean up excessive whitespace while preserving intentional formatting
    processed = processed.replace(/[ \t]+$/gm, ''); // Remove trailing spaces on each line
    processed = processed.replace(/\n{4,}/g, '\n\n\n'); // Limit to max 3 consecutive newlines
    return processed.trim();
}
// Default income guide text (original hardcoded version)
const DEFAULT_INCOME_GUIDE = [
    '💰 **Ways to Earn Phalanx Loyalty Coins**',
    '⏰ **Playing Time**',
    '• 10 hours/week = 150 Coins',
    '🔨 **Material Contributions**',
    '• Stack of Iron Ingots = 20 Coins',
    '• Stack of Gold = 10 Coins',
    '• Stack of Diamonds = 100 Coins',
    '🏰 **Territory Capturing**',
    '• Capture 1 node = 100 Coins',
    '• Help conquer a town = 300 Coins',
    '⚔️ **War Achievements**',
    '• Each kill during war = 15 Coins',
    '🤝 **Recruitment**',
    '• Active invite for 1 week = 500 Coins',
    '📝 **Important Note**',
    'To claim your earnings, you must open a ticket in #tickets channel!'
].join('\n');
/**
 * Get the income guide text for a guild
 * @param guildId Discord guild ID
 * @returns Custom income guide text or default if none set
 */
async function getIncomeGuideText(guildId) {
    try {
        const incomeGuide = await IncomeGuide_1.default.findOne({ guildId });
        if (incomeGuide?.customText) {
            // Format the stored text for proper Discord display
            return formatTextForDiscord(incomeGuide.customText);
        }
        return DEFAULT_INCOME_GUIDE;
    }
    catch (error) {
        console.error(`[IncomeGuideService] Error fetching income guide for guild ${guildId}:`, error);
        throw new errorHandler_1.DatabaseError('Failed to retrieve income guide text', error);
    }
}
/**
 * Set custom income guide text for a guild
 * @param guildId Discord guild ID
 * @param customText Custom income guide text
 * @returns Updated income guide document
 */
async function setIncomeGuideText(guildId, customText) {
    try {
        // Process the user input to handle line breaks properly
        const processedText = processUserInput(customText);
        const incomeGuide = await IncomeGuide_1.default.findOneAndUpdate({ guildId }, { customText: processedText }, {
            new: true,
            upsert: true,
            runValidators: true
        });
        if (!incomeGuide) {
            throw new Error('Failed to create or update income guide');
        }
        console.log(`[IncomeGuideService] Updated income guide for guild ${guildId}`);
        console.log(`[IncomeGuideService] Processed text length: ${processedText.length} characters`);
        return incomeGuide;
    }
    catch (error) {
        console.error(`[IncomeGuideService] Error setting income guide for guild ${guildId}:`, error);
        throw new errorHandler_1.DatabaseError('Failed to update income guide text', error);
    }
}
/**
 * Check if a guild has custom income guide text
 * @param guildId Discord guild ID
 * @returns True if custom text exists, false otherwise
 */
async function hasCustomIncomeGuide(guildId) {
    try {
        const incomeGuide = await IncomeGuide_1.default.findOne({ guildId });
        return !!incomeGuide;
    }
    catch (error) {
        console.error(`[IncomeGuideService] Error checking custom income guide for guild ${guildId}:`, error);
        return false;
    }
}
/**
 * Remove custom income guide text for a guild (revert to default)
 * @param guildId Discord guild ID
 * @returns True if removed, false if none existed
 */
async function removeCustomIncomeGuide(guildId) {
    try {
        const result = await IncomeGuide_1.default.deleteOne({ guildId });
        console.log(`[IncomeGuideService] Removed custom income guide for guild ${guildId}`);
        return result.deletedCount > 0;
    }
    catch (error) {
        console.error(`[IncomeGuideService] Error removing income guide for guild ${guildId}:`, error);
        throw new errorHandler_1.DatabaseError('Failed to remove custom income guide text', error);
    }
}
/**
 * Get the default income guide text
 * @returns Default income guide text
 */
function getDefaultIncomeGuideText() {
    return DEFAULT_INCOME_GUIDE;
}
//# sourceMappingURL=incomeGuideService.js.map