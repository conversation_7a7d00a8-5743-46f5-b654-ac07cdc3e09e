/**
 * Economy Service
 * Refactored economy service with improved architecture
 */

import mongoose from 'mongoose';
import { Client } from 'discord.js';
import { BaseService } from '../base/BaseService';
import { IEconomyService, IApplicationContext, TransactionType, LeaderboardEntry, TransactionRecord, IRoleService } from '../../core/interfaces';
import { DatabaseError } from '../../utils/errorHandler';
import { requireFeature } from '../../config/features';
import User from '../../models/User';
import Transaction from '../../models/Transaction';

/**
 * Economy service implementation
 */
export class EconomyService extends BaseService implements IEconomyService {
  public readonly name = 'EconomyService';

  constructor(app: IApplicationContext) {
    super(app);
  }

  /**
   * Initialize the economy service
   */
  protected async onInitialize(): Promise<void> {
    if (!this.isFeatureEnabled('ECONOMY_SYSTEM')) {
      throw new Error('Economy system is not enabled');
    }
    
    this.logger.info('[EconomyService] Economy system initialized');
  }

  /**
   * Adjust user balance with transaction logging
   */
  @requireFeature('ECONOMY_SYSTEM')
  async adjustBalance(
    discordId: string,
    amount: number,
    type: TransactionType,
    details?: string,
    client?: Client,
    guildId?: string,
    dynastyId?: string
  ): Promise<void> {
    // Input validation
    this.validateAdjustBalanceInput(discordId, amount, type);

    if (mongoose.connection.readyState !== 1) {
      throw new DatabaseError('Database is not connected. Please try again in a moment.');
    }
    const session = await mongoose.startSession();
    this.logOperation('Starting balance adjustment transaction', { discordId, amount, type, details });

    try {
      await session.withTransaction(async () => {
        // Validate discordId before database operation
        const trimmedDiscordId = discordId.trim();
        if (!trimmedDiscordId) {
          throw new Error('Discord ID cannot be empty after trimming');
        }

        // Atomic update or insert
        const user = await User.findOneAndUpdate(
          { discordId: trimmedDiscordId },
          {
            $inc: { balance: amount },
            $setOnInsert: { discordId: trimmedDiscordId }
          },
          {
            new: true,
            upsert: true,
            runValidators: true,
            session
          }
        );

        this.logOperation('Creating transaction record', {
          discordId: trimmedDiscordId,
          type,
          amount,
          details,
          dynastyId
        });

        // Create transaction record in same transaction
        await Transaction.create([{
          discordId: trimmedDiscordId,
          type,
          amount,
          details,
          dynastyId,
          timestamp: new Date()
        }], { session });

        this.logOperation('Transaction complete', { 
          userId: user?._id, 
          newBalance: user?.balance 
        });

        // Check for role achievements if balance increased and we have client/guild info
        if (amount > 0 && client && guildId && user) {
          // Schedule role checking after transaction completes
          setImmediate(async () => {
            try {
              await this.checkRoleAchievements(client, trimmedDiscordId, guildId, user.balance);
            } catch (error) {
              this.handleError(error, { operation: 'role_achievement_check' });
            }
          });
        }
      });
    } catch (error) {
      this.handleError(error, { discordId, amount, type });
      throw new DatabaseError(`Failed to adjust balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      await session.endSession();
    }
  }

  /**
   * Get user balance
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getBalance(discordId: string): Promise<number> {
    try {
      const user = await this.ensureUser(discordId);
      return user.balance;
    } catch (error) {
      this.handleError(error, {discordId });
      throw new DatabaseError(`Failed to get balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get leaderboard
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getLeaderboard(guildId?: string, limit: number = 10): Promise<LeaderboardEntry[]> {
    try {
      const users = await User.find({})
        .sort({ balance: -1 })
        .limit(limit)
        .lean();

      return users.map((user, index) => ({
        discordId: user.discordId,
        balance: user.balance,
        rank: index + 1,
      }));
    } catch (error) {
      this.handleError(error, { guildId, limit });
      throw new DatabaseError(`Failed to get leaderboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get transaction history
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getTransactionHistory(discordId: string, limit: number = 20): Promise<TransactionRecord[]> {
    try {
      const transactions = await Transaction.find({ discordId })
        .sort({ timestamp: -1 })
        .limit(limit)
        .lean();

      return transactions.map(tx => ({
        id: tx._id.toString(),
        discordId: tx.discordId,
        type: tx.type,
        amount: tx.amount,
        details: tx.details,
        timestamp: tx.timestamp,
      }));
    } catch (error) {
      this.handleError(error, { discordId, limit });
      throw new DatabaseError(`Failed to get transaction history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Ensure user exists in database
   */
  async ensureUser(discordId: string): Promise<any> {
    try {
      const trimmedDiscordId = discordId.trim();
      if (!trimmedDiscordId) {
        throw new Error('Discord ID cannot be empty');
      }

      const user = await User.findOneAndUpdate(
        { discordId: trimmedDiscordId },
        { $setOnInsert: { discordId: trimmedDiscordId, balance: 0 } },
        { new: true, upsert: true, runValidators: true }
      );

      return user;
    } catch (error) {
      this.handleError(error, { discordId });
      throw new DatabaseError(`Failed to ensure user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate adjust balance input
   */
  private validateAdjustBalanceInput(discordId: string, amount: number, type: TransactionType): void {
    if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
      throw new DatabaseError('Invalid Discord ID provided');
    }

    if (typeof amount !== 'number' || isNaN(amount)) {
      throw new DatabaseError('Invalid amount provided');
    }

    if (!type) {
      throw new DatabaseError('Transaction type is required');
    }
  }

  /**
   * Check and assign role achievements
   */
  private async checkRoleAchievements(client: Client, discordId: string, guildId: string, balance: number): Promise<void> {
    try {
      // Use the service locator to get the RoleService instance at runtime.
      // This correctly avoids the circular dependency issue at the module-import level.
      const roleService = this.getService<IRoleService>('RoleService');
      if (!roleService) {
        this.logger.warn('[EconomyService] RoleService not available for achievement check.');
        return;
      }

      const roleResult = await roleService.checkAndAssignRoles(client, discordId, guildId, balance);
      if (roleResult) {
        // Note: `sendRoleAchievementNotifications` should be a public method on RoleService.
        await (roleService as any).sendRoleAchievementNotifications(roleResult, client);
      }
    } catch (error) {
      this.handleError(error, { operation: 'role_achievement_check', discordId, guildId, balance });
    }
  }
}
