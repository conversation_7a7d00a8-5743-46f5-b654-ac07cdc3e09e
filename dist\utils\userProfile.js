"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.findOrCreateUser = findOrCreateUser;
const User_1 = __importDefault(require("../models/User"));
/**
 * Retrieves an economy profile for a user, creating one if it doesn't exist.
 * @param userId The user's Discord ID.
 * @returns The Mongoose document for the user.
 */
async function findOrCreateUser(userId) {
    const userProfile = await User_1.default.findOneAndUpdate({ userId }, {}, { new: true, upsert: true, setDefaultsOnInsert: true });
    return userProfile;
}
//# sourceMappingURL=userProfile.js.map