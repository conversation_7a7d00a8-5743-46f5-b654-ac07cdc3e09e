/**
 * Guild Member Remove Event Handler
 * Handles Discord guild member leave events for user data cleanup
 */
import { GuildMember, PartialGuildMember } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';
/**
 * Guild member remove event handler
 */
export declare class GuildMemberRemoveEventHandler extends BaseEventHandler {
    readonly name = "guildMemberRemove";
    constructor(app: IApplicationContext);
    /**
     * Execute guild member remove event
     */
    execute(member: GuildMember | PartialGuildMember): Promise<void>;
    /**
     * Ensure member is fully fetched
     */
    private ensureFullMember;
    /**
     * Process user data cleanup
     */
    private processUserCleanup;
}
//# sourceMappingURL=guildMemberRemove.d.ts.map