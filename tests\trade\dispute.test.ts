/**
 * Dispute System Tests
 * Tests for dispute creation, resolution, and edge cases
 */

import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from '@jest/jest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { DisputeService } from '../../src/services/trade/DisputeService';
import { User, Trade, DisputeCase, UserTradeStats } from '../../src/models';
import { TRADE } from '../../src/config/constants';

describe('Dispute System Tests', () => {
  let mongoServer: MongoMemoryServer;
  let disputeService: DisputeService;
  let mockApp: any;

  // Test data
  const testGuildId = '123456789012345678';
  const testSellerId = '111111111111111111';
  const testBuyerId = '222222222222222222';
  const testAdminId = '333333333333333333';
  const testTradeAmount = 1000;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear database
    await User.deleteMany({});
    await Trade.deleteMany({});
    await DisputeCase.deleteMany({});
    await UserTradeStats.deleteMany({});

    // Create mock app
    mockApp = {
      logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
        debug: jest.fn()
      }
    };

    disputeService = new DisputeService(mockApp);
    await disputeService.onInitialize();

    // Create test users
    await User.create([
      { discordId: testSellerId, balance: 5000 },
      { discordId: testBuyerId, balance: 4000 }, // Already paid 1000 to escrow
      { discordId: testAdminId, balance: 10000 }
    ]);

    // Create user trade stats
    await UserTradeStats.create([
      {
        discordId: testSellerId,
        guildId: testGuildId,
        totalTrades: 5,
        successfulTrades: 4,
        cancelledTrades: 0,
        expiredTrades: 0,
        disputedTrades: 1,
        tradesAsSeller: 3,
        tradesAsBuyer: 2,
        totalVolumeTraded: 5000,
        averageTradeValue: 1000,
        largestTrade: 2000,
        reputationScore: 75,
        disputeRatio: 0.2,
        completionRate: 0.8,
        averageCompletionTime: 12,
        fastestCompletion: 2,
        activeTrades: 1,
        isRestricted: false,
        dailyTradeCount: 1,
        lastTradeDate: new Date(),
        lastResetDate: new Date(),
        lastUpdated: new Date(),
        warningsReceived: 0,
        violationHistory: []
      },
      {
        discordId: testBuyerId,
        guildId: testGuildId,
        totalTrades: 3,
        successfulTrades: 2,
        cancelledTrades: 0,
        expiredTrades: 0,
        disputedTrades: 1,
        tradesAsSeller: 1,
        tradesAsBuyer: 2,
        totalVolumeTraded: 3000,
        averageTradeValue: 1000,
        largestTrade: 1500,
        reputationScore: 60,
        disputeRatio: 0.33,
        completionRate: 0.67,
        averageCompletionTime: 18,
        fastestCompletion: 4,
        activeTrades: 1,
        isRestricted: false,
        dailyTradeCount: 1,
        lastTradeDate: new Date(),
        lastResetDate: new Date(),
        lastUpdated: new Date(),
        warningsReceived: 1,
        violationHistory: ['Previous dispute warning']
      }
    ]);
  });

  afterEach(async () => {
    await User.deleteMany({});
    await Trade.deleteMany({});
    await DisputeCase.deleteMany({});
    await UserTradeStats.deleteMany({});
  });

  describe('Dispute Initiation', () => {
    it('should successfully initiate a dispute for an active trade', async () => {
      // Create active trade with locked escrow
      const trade = await Trade.create({
        tradeId: 'test_trade_001',
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Diamond Sword',
        state: TRADE.STATES.ACTIVE,
        initiatedBy: 'SELLER',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        escrowLocked: true,
        escrowAmount: testTradeAmount,
        sellerConfirmed: false,
        buyerConfirmed: false,
        warningsSent: 0,
        extensionGranted: false
      });

      const disputeCase = await disputeService.initiateDispute({
        tradeId: trade.tradeId,
        initiatorId: testBuyerId,
        reason: 'Item not as described',
        description: 'The sword was supposed to be enchanted but it is not',
        category: 'ITEM_NOT_AS_DESCRIBED'
      });

      expect(disputeCase.tradeId).toBe(trade.tradeId);
      expect(disputeCase.initiatorId).toBe(testBuyerId);
      expect(disputeCase.respondentId).toBe(testSellerId);
      expect(disputeCase.reason).toBe('Item not as described');
      expect(disputeCase.category).toBe('ITEM_NOT_AS_DESCRIBED');
      expect(disputeCase.status).toBe('EVIDENCE_COLLECTION');
      expect(disputeCase.evidenceDeadline).toBeTruthy();

      // Verify trade state updated
      const updatedTrade = await Trade.findOne({ tradeId: trade.tradeId });
      expect(updatedTrade?.state).toBe(TRADE.STATES.DISPUTED);
      expect(updatedTrade?.disputeId).toBe(disputeCase.disputeId);
      expect(updatedTrade?.disputedBy).toBe(testBuyerId);
      expect(updatedTrade?.disputeReason).toBe('Item not as described');
    });

    it('should fail to initiate dispute for non-active trade', async () => {
      const trade = await Trade.create({
        tradeId: 'test_trade_002',
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Diamond Sword',
        state: TRADE.STATES.COMPLETED, // Not active
        initiatedBy: 'SELLER',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        escrowLocked: false,
        escrowAmount: 0,
        sellerConfirmed: true,
        buyerConfirmed: true,
        warningsSent: 0,
        extensionGranted: false
      });

      await expect(disputeService.initiateDispute({
        tradeId: trade.tradeId,
        initiatorId: testBuyerId,
        reason: 'Test dispute',
        category: 'OTHER'
      })).rejects.toThrow('Disputes can only be initiated for active trades');
    });

    it('should fail if user is not party to the trade', async () => {
      const trade = await Trade.create({
        tradeId: 'test_trade_003',
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Diamond Sword',
        state: TRADE.STATES.ACTIVE,
        initiatedBy: 'SELLER',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        escrowLocked: true,
        escrowAmount: testTradeAmount,
        sellerConfirmed: false,
        buyerConfirmed: false,
        warningsSent: 0,
        extensionGranted: false
      });

      await expect(disputeService.initiateDispute({
        tradeId: trade.tradeId,
        initiatorId: testAdminId, // Not party to trade
        reason: 'Test dispute',
        category: 'OTHER'
      })).rejects.toThrow('You are not a party to this trade');
    });

    it('should fail if trade already has a dispute', async () => {
      const trade = await Trade.create({
        tradeId: 'test_trade_004',
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Diamond Sword',
        state: TRADE.STATES.ACTIVE,
        initiatedBy: 'SELLER',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        escrowLocked: true,
        escrowAmount: testTradeAmount,
        disputeId: 'existing_dispute', // Already has dispute
        sellerConfirmed: false,
        buyerConfirmed: false,
        warningsSent: 0,
        extensionGranted: false
      });

      await expect(disputeService.initiateDispute({
        tradeId: trade.tradeId,
        initiatorId: testBuyerId,
        reason: 'Test dispute',
        category: 'OTHER'
      })).rejects.toThrow('This trade already has an active dispute');
    });
  });

  describe('Dispute Resolution', () => {
    let trade: any;
    let disputeCase: any;

    beforeEach(async () => {
      // Create trade and dispute for resolution tests
      trade = await Trade.create({
        tradeId: 'dispute_trade_001',
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Diamond Sword',
        state: TRADE.STATES.DISPUTED,
        initiatedBy: 'SELLER',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        escrowLocked: true,
        escrowAmount: testTradeAmount,
        sellerConfirmed: false,
        buyerConfirmed: false,
        warningsSent: 0,
        extensionGranted: false
      });

      disputeCase = await DisputeCase.create({
        disputeId: 'dispute_001',
        tradeId: trade.tradeId,
        guildId: testGuildId,
        initiatorId: testBuyerId,
        respondentId: testSellerId,
        reason: 'Item not received',
        category: 'ITEM_NOT_RECEIVED',
        status: 'UNDER_REVIEW',
        priority: 'MEDIUM',
        evidenceDeadline: new Date(Date.now() + 48 * 60 * 60 * 1000),
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
        adminNotes: [],
        escalationLevel: 0,
        tags: ['item_not_received']
      });

      trade.disputeId = disputeCase.disputeId;
      await trade.save();
    });

    it('should resolve dispute in favor of initiator', async () => {
      const resolvedDispute = await disputeService.resolveDispute({
        disputeId: disputeCase.disputeId,
        adminId: testAdminId,
        resolution: 'FAVOR_INITIATOR',
        resolutionDetails: 'Evidence shows item was never sent',
        adminNotes: 'Clear case of non-delivery'
      });

      expect(resolvedDispute.status).toBe('RESOLVED');
      expect(resolvedDispute.resolution).toBe('FAVOR_INITIATOR');
      expect(resolvedDispute.resolutionDetails).toBe('Evidence shows item was never sent');
      expect(resolvedDispute.resolvedAt).toBeTruthy();
      expect(resolvedDispute.assignedAdminId).toBe(testAdminId);

      // Verify trade completed
      const updatedTrade = await Trade.findOne({ tradeId: trade.tradeId });
      expect(updatedTrade?.state).toBe(TRADE.STATES.COMPLETED);
      expect(updatedTrade?.completedAt).toBeTruthy();

      // Verify buyer got refund (since buyer was initiator and won)
      const buyer = await User.findOne({ discordId: testBuyerId });
      expect(buyer?.balance).toBe(4000 + testTradeAmount); // Original + refund
    });

    it('should resolve dispute in favor of respondent', async () => {
      const resolvedDispute = await disputeService.resolveDispute({
        disputeId: disputeCase.disputeId,
        adminId: testAdminId,
        resolution: 'FAVOR_RESPONDENT',
        resolutionDetails: 'Evidence shows item was delivered correctly',
        adminNotes: 'Buyer confirmed receipt in private message'
      });

      expect(resolvedDispute.resolution).toBe('FAVOR_RESPONDENT');

      // Verify seller got payment (since seller was respondent and won)
      const seller = await User.findOne({ discordId: testSellerId });
      expect(seller?.balance).toBe(5000 + testTradeAmount); // Original + payment
    });

    it('should resolve dispute with split escrow', async () => {
      const resolvedDispute = await disputeService.resolveDispute({
        disputeId: disputeCase.disputeId,
        adminId: testAdminId,
        resolution: 'SPLIT_ESCROW',
        resolutionDetails: 'Both parties partially at fault',
        adminNotes: 'Fair split due to miscommunication'
      });

      expect(resolvedDispute.resolution).toBe('SPLIT_ESCROW');

      // Verify both parties got half
      const seller = await User.findOne({ discordId: testSellerId });
      const buyer = await User.findOne({ discordId: testBuyerId });
      
      const halfAmount = Math.floor(testTradeAmount / 2);
      const remainder = testTradeAmount - halfAmount;
      
      expect(seller?.balance).toBe(5000 + halfAmount);
      expect(buyer?.balance).toBe(4000 + remainder);
    });

    it('should resolve dispute with custom amount', async () => {
      const customSellerAmount = 300;
      const customBuyerAmount = 700;

      const resolvedDispute = await disputeService.resolveDispute({
        disputeId: disputeCase.disputeId,
        adminId: testAdminId,
        resolution: 'CUSTOM',
        resolutionDetails: 'Custom split based on evidence',
        resolutionAmount: customSellerAmount,
        adminNotes: 'Seller gets 30%, buyer gets 70%'
      });

      expect(resolvedDispute.resolution).toBe('CUSTOM');
      expect(resolvedDispute.resolutionAmount).toBe(customSellerAmount);

      // Verify custom amounts
      const seller = await User.findOne({ discordId: testSellerId });
      const buyer = await User.findOne({ discordId: testBuyerId });
      
      expect(seller?.balance).toBe(5000 + customSellerAmount);
      expect(buyer?.balance).toBe(4000 + customBuyerAmount);
    });

    it('should fail custom resolution without amount', async () => {
      await expect(disputeService.resolveDispute({
        disputeId: disputeCase.disputeId,
        adminId: testAdminId,
        resolution: 'CUSTOM',
        resolutionDetails: 'Custom resolution',
        // Missing resolutionAmount
      })).rejects.toThrow('Custom resolution requires resolution amount');
    });
  });

  describe('Evidence Collection', () => {
    let disputeCase: any;

    beforeEach(async () => {
      disputeCase = await DisputeCase.create({
        disputeId: 'evidence_dispute_001',
        tradeId: 'evidence_trade_001',
        guildId: testGuildId,
        initiatorId: testBuyerId,
        respondentId: testSellerId,
        reason: 'Item not as described',
        category: 'ITEM_NOT_AS_DESCRIBED',
        status: 'EVIDENCE_COLLECTION',
        priority: 'MEDIUM',
        evidenceDeadline: new Date(Date.now() + 48 * 60 * 60 * 1000),
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
        adminNotes: [],
        escalationLevel: 0,
        tags: ['item_not_as_described'],
        initiatorEvidence: [],
        respondentEvidence: []
      });
    });

    it('should allow initiator to add evidence', async () => {
      const evidence = [
        'Screenshot showing item stats',
        'Chat log of original agreement'
      ];

      const updatedDispute = await disputeService.addEvidence(
        disputeCase.disputeId,
        testBuyerId,
        evidence
      );

      expect(updatedDispute.initiatorEvidence).toEqual(evidence);
      expect(updatedDispute.respondentEvidence).toEqual([]);
    });

    it('should allow respondent to add evidence', async () => {
      const evidence = [
        'Screenshot of item delivery',
        'Proof of enchantment application'
      ];

      const updatedDispute = await disputeService.addEvidence(
        disputeCase.disputeId,
        testSellerId,
        evidence
      );

      expect(updatedDispute.respondentEvidence).toEqual(evidence);
      expect(updatedDispute.initiatorEvidence).toEqual([]);
    });

    it('should fail if non-party tries to add evidence', async () => {
      await expect(disputeService.addEvidence(
        disputeCase.disputeId,
        testAdminId, // Not party to dispute
        ['Some evidence']
      )).rejects.toThrow('You are not a party to this dispute');
    });

    it('should fail if evidence deadline has passed', async () => {
      // Update dispute to have passed deadline
      await DisputeCase.updateOne(
        { disputeId: disputeCase.disputeId },
        { evidenceDeadline: new Date(Date.now() - 1000) } // 1 second ago
      );

      await expect(disputeService.addEvidence(
        disputeCase.disputeId,
        testBuyerId,
        ['Late evidence']
      )).rejects.toThrow('Evidence submission deadline has passed');
    });
  });

  describe('Dispute Queries', () => {
    beforeEach(async () => {
      // Create multiple disputes for query testing
      await DisputeCase.create([
        {
          disputeId: 'query_dispute_001',
          tradeId: 'query_trade_001',
          guildId: testGuildId,
          initiatorId: testBuyerId,
          respondentId: testSellerId,
          reason: 'Item not received',
          category: 'ITEM_NOT_RECEIVED',
          status: 'OPEN',
          priority: 'HIGH',
          evidenceDeadline: new Date(Date.now() + 48 * 60 * 60 * 1000),
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
          adminNotes: [],
          escalationLevel: 0,
          tags: ['item_not_received']
        },
        {
          disputeId: 'query_dispute_002',
          tradeId: 'query_trade_002',
          guildId: testGuildId,
          initiatorId: testSellerId,
          respondentId: testBuyerId,
          reason: 'Payment not received',
          category: 'PAYMENT_ISSUE',
          status: 'UNDER_REVIEW',
          priority: 'MEDIUM',
          evidenceDeadline: new Date(Date.now() + 48 * 60 * 60 * 1000),
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
          adminNotes: [],
          escalationLevel: 0,
          tags: ['payment_issue']
        },
        {
          disputeId: 'query_dispute_003',
          tradeId: 'query_trade_003',
          guildId: 'different_guild',
          initiatorId: testBuyerId,
          respondentId: testSellerId,
          reason: 'Communication issue',
          category: 'COMMUNICATION_ISSUE',
          status: 'RESOLVED',
          priority: 'LOW',
          evidenceDeadline: new Date(Date.now() + 48 * 60 * 60 * 1000),
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
          adminNotes: [],
          escalationLevel: 0,
          tags: ['communication_issue']
        }
      ]);
    });

    it('should get active disputes for guild', async () => {
      const activeDisputes = await disputeService.getActiveDisputes(testGuildId);
      
      expect(activeDisputes).toHaveLength(2); // Only 2 from test guild and active
      expect(activeDisputes[0].priority).toBe('HIGH'); // Should be sorted by priority
      expect(activeDisputes[1].priority).toBe('MEDIUM');
    });

    it('should get all active disputes when no guild specified', async () => {
      const allActiveDisputes = await disputeService.getActiveDisputes();
      
      expect(allActiveDisputes).toHaveLength(2); // Only active ones (RESOLVED excluded)
    });

    it('should get specific dispute by ID', async () => {
      const dispute = await disputeService.getDispute('query_dispute_001');
      
      expect(dispute).toBeTruthy();
      expect(dispute?.disputeId).toBe('query_dispute_001');
      expect(dispute?.reason).toBe('Item not received');
    });

    it('should return null for non-existent dispute', async () => {
      const dispute = await disputeService.getDispute('nonexistent_dispute');
      expect(dispute).toBeNull();
    });
  });
});
