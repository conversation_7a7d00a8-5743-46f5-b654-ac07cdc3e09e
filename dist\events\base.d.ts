/**
 * Base Event Handler
 * Abstract base class for all Discord event handlers
 */
import { IEventHandler, ILogger, IApplicationContext } from '../core/interfaces';
/**
 * Abstract base event handler class
 */
export declare abstract class BaseEventHandler implements IEventHandler {
    abstract readonly name: string;
    readonly once?: boolean;
    protected logger: ILogger;
    protected app: IApplicationContext;
    constructor(app: IApplicationContext, eventName: string);
    /**
     * Execute the event handler
     */
    abstract execute(...args: any[]): Promise<void>;
    /**
     * Handle errors in event execution
     */
    protected handleError(error: any, context?: any): void;
    /**
     * Log event execution
     */
    protected logExecution(message: string, meta?: any): void;
    /**
     * Check if a feature is enabled before processing
     */
    protected isFeatureEnabled(featureName: string): boolean;
}
/**
 * Event handler registry
 */
export declare class EventHandlerRegistry {
    private handlers;
    private logger;
    constructor();
    /**
     * Register an event handler
     */
    register(handler: BaseEventHandler): void;
    /**
     * Get all registered handlers
     */
    getHandlers(): BaseEventHandler[];
    /**
     * Get handler by name
     */
    getHandler(name: string): BaseEventHandler | undefined;
    /**
     * Unregister a handler
     */
    unregister(name: string): boolean;
    /**
     * Clear all handlers
     */
    clear(): void;
}
/**
 * Global event registry instance
 */
export declare const eventRegistry: EventHandlerRegistry;
//# sourceMappingURL=base.d.ts.map