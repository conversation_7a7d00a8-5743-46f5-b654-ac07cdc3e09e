const mongoose = require('mongoose');
require('dotenv').config();

const uri = process.env.MONGODB_URI;

// Test schema similar to the bot's User model
const testUserSchema = new mongoose.Schema({
  discordId: { type: String, required: true, unique: true },
  balance: { type: Number, default: 0 },
  lastActive: { type: Date, default: Date.now },
  createdAt: { type: Date, default: Date.now }
});

const TestUser = mongoose.model('TestUser', testUserSchema);

async function testDatabaseOperations() {
  try {
    console.log('🔄 Testing database operations...');
    
    // Connect with the same configuration as the bot
    await mongoose.connect(uri, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // Test 1: Create a user
    const testUser = new TestUser({
      discordId: 'test_user_' + Date.now(),
      balance: 1000
    });
    
    await testUser.save();
    console.log('✅ Test 1: Successfully created user');
    
    // Test 2: Find the user
    const foundUser = await TestUser.findOne({ discordId: testUser.discordId });
    if (foundUser && foundUser.balance === 1000) {
      console.log('✅ Test 2: Successfully found user with correct balance');
    } else {
      throw new Error('User not found or balance incorrect');
    }
    
    // Test 3: Update the user
    await TestUser.updateOne(
      { discordId: testUser.discordId },
      { $inc: { balance: 500 } }
    );
    
    const updatedUser = await TestUser.findOne({ discordId: testUser.discordId });
    if (updatedUser && updatedUser.balance === 1500) {
      console.log('✅ Test 3: Successfully updated user balance');
    } else {
      throw new Error('Balance update failed');
    }
    
    // Test 4: Test aggregation (similar to leaderboard queries)
    const leaderboard = await TestUser.aggregate([
      { $sort: { balance: -1 } },
      { $limit: 10 },
      { $project: { discordId: 1, balance: 1 } }
    ]);
    
    if (leaderboard.length > 0) {
      console.log('✅ Test 4: Successfully executed aggregation query');
    }
    
    // Test 5: Test transaction-like operations
    const session = await mongoose.startSession();
    try {
      await session.withTransaction(async () => {
        await TestUser.updateOne(
          { discordId: testUser.discordId },
          { $inc: { balance: -100 } },
          { session }
        );
      });
      console.log('✅ Test 5: Successfully executed transaction');
    } finally {
      await session.endSession();
    }
    
    // Test 6: Cleanup
    await TestUser.deleteOne({ discordId: testUser.discordId });
    console.log('✅ Test 6: Successfully cleaned up test data');
    
    console.log('🎉 All database operations completed successfully!');
    
    // Test connection stability by keeping it open for a bit
    console.log('⏳ Testing connection stability for 30 seconds...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // Test one more operation after the wait
    const connectionTest = await TestUser.countDocuments();
    console.log('✅ Connection remained stable - document count:', connectionTest);
    
  } catch (error) {
    console.error('❌ Database operation failed:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

testDatabaseOperations().catch(console.error);
