/**
 * Trade Validation Tests
 * Tests for edge cases and validation logic
 */

import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from '@jest/jest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { TradeValidator } from '../../src/services/trade/managers/TradeValidator';
import { TradeSecurityService } from '../../src/services/trade/TradeSecurityService';
import { User, Trade, UserTradeStats } from '../../src/models';
import { TRADE } from '../../src/config/constants';

describe('Trade Validation Tests', () => {
  let mongoServer: MongoMemoryServer;
  let tradeValidator: TradeValidator;
  let securityService: TradeSecurityService;
  let mockApp: any;

  // Test data
  const testGuildId = '123456789012345678';
  const testSellerId = '111111111111111111';
  const testBuyerId = '222222222222222222';
  const testTradeAmount = 1000;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear database
    await User.deleteMany({});
    await Trade.deleteMany({});
    await UserTradeStats.deleteMany({});

    // Create mock app
    mockApp = {
      logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
        debug: jest.fn()
      }
    };

    tradeValidator = new TradeValidator(mockApp);
    securityService = new TradeSecurityService(mockApp);
    
    await tradeValidator.initialize();
    await securityService.onInitialize();

    // Create test users
    await User.create([
      { discordId: testSellerId, balance: 5000 },
      { discordId: testBuyerId, balance: 5000 }
    ]);
  });

  afterEach(async () => {
    await User.deleteMany({});
    await Trade.deleteMany({});
    await UserTradeStats.deleteMany({});
  });

  describe('Trade Creation Validation', () => {
    it('should validate minimum trade amount', async () => {
      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: TRADE.MIN_TRADE_AMOUNT - 1,
        itemDescription: 'Test Item',
        initiatedBy: 'SELLER'
      })).rejects.toThrow(`Trade amount must be at least ${TRADE.MIN_TRADE_AMOUNT} PLC`);
    });

    it('should validate maximum trade amount', async () => {
      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: TRADE.MAX_TRADE_AMOUNT + 1,
        itemDescription: 'Test Item',
        initiatedBy: 'SELLER'
      })).rejects.toThrow(`Trade amount cannot exceed ${TRADE.MAX_TRADE_AMOUNT} PLC`);
    });

    it('should validate Discord ID format', async () => {
      await expect(tradeValidator.validateTradeCreation({
        sellerId: 'invalid_id',
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Test Item',
        initiatedBy: 'SELLER'
      })).rejects.toThrow('Invalid seller Discord ID');
    });

    it('should prevent self-trading', async () => {
      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testSellerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Test Item',
        initiatedBy: 'SELLER'
      })).rejects.toThrow('You cannot trade with yourself');
    });

    it('should validate item description length', async () => {
      const longDescription = 'a'.repeat(501); // Exceeds 500 char limit
      
      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: longDescription,
        initiatedBy: 'SELLER'
      })).rejects.toThrow('Item description cannot exceed 500 characters');
    });

    it('should validate empty item description', async () => {
      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: '   ', // Only whitespace
        initiatedBy: 'SELLER'
      })).rejects.toThrow('Item description cannot be empty');
    });

    it('should prevent duplicate active trades between same parties', async () => {
      // Create existing active trade
      await Trade.create({
        tradeId: 'existing_trade',
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Existing Item',
        state: TRADE.STATES.ACTIVE,
        initiatedBy: 'SELLER',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        escrowLocked: false,
        escrowAmount: 0,
        sellerConfirmed: false,
        buyerConfirmed: false,
        warningsSent: 0,
        extensionGranted: false
      });

      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'New Item',
        initiatedBy: 'SELLER'
      })).rejects.toThrow('You already have an active trade with this user');
    });
  });

  describe('Rate Limiting Tests', () => {
    it('should enforce daily trade proposal limit', async () => {
      // Create user stats with max daily trades
      await UserTradeStats.create({
        discordId: testSellerId,
        guildId: testGuildId,
        totalTrades: 0,
        successfulTrades: 0,
        cancelledTrades: 0,
        expiredTrades: 0,
        disputedTrades: 0,
        tradesAsSeller: 0,
        tradesAsBuyer: 0,
        totalVolumeTraded: 0,
        averageTradeValue: 0,
        largestTrade: 0,
        reputationScore: 50,
        disputeRatio: 0,
        completionRate: 0,
        averageCompletionTime: 0,
        fastestCompletion: 0,
        activeTrades: 0,
        isRestricted: false,
        dailyTradeCount: TRADE.MAX_TRADE_PROPOSALS_PER_DAY,
        lastTradeDate: new Date(),
        lastResetDate: new Date(),
        lastUpdated: new Date(),
        warningsReceived: 0,
        violationHistory: []
      });

      const rateLimitCheck = await securityService.checkTradeRateLimit(testSellerId, testGuildId);
      expect(rateLimitCheck.allowed).toBe(false);
      expect(rateLimitCheck.reason).toContain('Daily trade limit');
    });

    it('should enforce maximum active trades limit', async () => {
      await UserTradeStats.create({
        discordId: testSellerId,
        guildId: testGuildId,
        totalTrades: 0,
        successfulTrades: 0,
        cancelledTrades: 0,
        expiredTrades: 0,
        disputedTrades: 0,
        tradesAsSeller: 0,
        tradesAsBuyer: 0,
        totalVolumeTraded: 0,
        averageTradeValue: 0,
        largestTrade: 0,
        reputationScore: 50,
        disputeRatio: 0,
        completionRate: 0,
        averageCompletionTime: 0,
        fastestCompletion: 0,
        activeTrades: TRADE.MAX_ACTIVE_TRADES_PER_USER,
        isRestricted: false,
        dailyTradeCount: 0,
        lastTradeDate: new Date(),
        lastResetDate: new Date(),
        lastUpdated: new Date(),
        warningsReceived: 0,
        violationHistory: []
      });

      const rateLimitCheck = await securityService.checkTradeRateLimit(testSellerId, testGuildId);
      expect(rateLimitCheck.allowed).toBe(false);
      expect(rateLimitCheck.reason).toContain('Maximum of');
    });

    it('should reset daily count at midnight', async () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      await UserTradeStats.create({
        discordId: testSellerId,
        guildId: testGuildId,
        totalTrades: 0,
        successfulTrades: 0,
        cancelledTrades: 0,
        expiredTrades: 0,
        disputedTrades: 0,
        tradesAsSeller: 0,
        tradesAsBuyer: 0,
        totalVolumeTraded: 0,
        averageTradeValue: 0,
        largestTrade: 0,
        reputationScore: 50,
        disputeRatio: 0,
        completionRate: 0,
        averageCompletionTime: 0,
        fastestCompletion: 0,
        activeTrades: 0,
        isRestricted: false,
        dailyTradeCount: TRADE.MAX_TRADE_PROPOSALS_PER_DAY,
        lastTradeDate: yesterday,
        lastResetDate: yesterday, // Yesterday's reset
        lastUpdated: new Date(),
        warningsReceived: 0,
        violationHistory: []
      });

      const rateLimitCheck = await securityService.checkTradeRateLimit(testSellerId, testGuildId);
      expect(rateLimitCheck.allowed).toBe(true);
    });
  });

  describe('Security Risk Assessment', () => {
    it('should detect high-risk users', async () => {
      // Create user with high dispute ratio
      await UserTradeStats.create({
        discordId: testSellerId,
        guildId: testGuildId,
        totalTrades: 10,
        successfulTrades: 5,
        cancelledTrades: 2,
        expiredTrades: 1,
        disputedTrades: 3, // 30% dispute ratio
        tradesAsSeller: 5,
        tradesAsBuyer: 5,
        totalVolumeTraded: 10000,
        averageTradeValue: 1000,
        largestTrade: 2000,
        reputationScore: 25, // Low reputation
        disputeRatio: 0.3,
        completionRate: 0.5,
        averageCompletionTime: 24,
        fastestCompletion: 2,
        activeTrades: 0,
        isRestricted: false,
        dailyTradeCount: 0,
        lastTradeDate: new Date(),
        lastResetDate: new Date(),
        lastUpdated: new Date(),
        warningsReceived: 3,
        violationHistory: ['Warning 1', 'Warning 2', 'Warning 3']
      });

      const riskProfile = await securityService.getUserRiskProfile(testSellerId, testGuildId);
      
      expect(riskProfile.riskLevel).toBe('HIGH');
      expect(riskProfile.factors).toContain('High dispute ratio: 30.0%');
      expect(riskProfile.factors).toContain('Multiple warnings: 3');
      expect(riskProfile.factors).toContain('Low reputation: 25/100');
    });

    it('should apply automatic restrictions for high-risk behavior', async () => {
      // Create user with very high dispute ratio
      await UserTradeStats.create({
        discordId: testSellerId,
        guildId: testGuildId,
        totalTrades: 10,
        successfulTrades: 3,
        cancelledTrades: 2,
        expiredTrades: 0,
        disputedTrades: 5, // 50% dispute ratio
        tradesAsSeller: 5,
        tradesAsBuyer: 5,
        totalVolumeTraded: 10000,
        averageTradeValue: 1000,
        largestTrade: 2000,
        reputationScore: 15,
        disputeRatio: 0.5, // Above threshold
        completionRate: 0.3,
        averageCompletionTime: 24,
        fastestCompletion: 2,
        activeTrades: 0,
        isRestricted: false,
        dailyTradeCount: 0,
        lastTradeDate: new Date(),
        lastResetDate: new Date(),
        lastUpdated: new Date(),
        warningsReceived: 0,
        violationHistory: []
      });

      const restricted = await securityService.applyAutomaticRestrictions(testSellerId, testGuildId);
      expect(restricted).toBe(true);

      // Verify user is now restricted
      const userStats = await UserTradeStats.findOne({ discordId: testSellerId, guildId: testGuildId });
      expect(userStats?.isRestricted).toBe(true);
      expect(userStats?.restrictionReason).toContain('High dispute ratio');
    });
  });

  describe('Trade State Validation', () => {
    it('should validate trade acceptance state', async () => {
      const trade = await Trade.create({
        tradeId: 'test_trade_001',
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Test Item',
        state: TRADE.STATES.COMPLETED, // Wrong state
        initiatedBy: 'SELLER',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        escrowLocked: false,
        escrowAmount: 0,
        sellerConfirmed: false,
        buyerConfirmed: false,
        warningsSent: 0,
        extensionGranted: false
      });

      await expect(tradeValidator.validateTradeAcceptance(trade, testBuyerId))
        .rejects.toThrow('Trade is not in PROPOSED state');
    });

    it('should validate trade confirmation state', async () => {
      const trade = await Trade.create({
        tradeId: 'test_trade_002',
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Test Item',
        state: TRADE.STATES.PROPOSED, // Wrong state
        initiatedBy: 'SELLER',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        escrowLocked: false,
        escrowAmount: 0,
        sellerConfirmed: false,
        buyerConfirmed: false,
        warningsSent: 0,
        extensionGranted: false
      });

      await expect(tradeValidator.validateTradeConfirmation(trade, testSellerId))
        .rejects.toThrow('Trade is not in ACTIVE state');
    });

    it('should prevent non-party users from interacting with trades', async () => {
      const trade = await Trade.create({
        tradeId: 'test_trade_003',
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Test Item',
        state: TRADE.STATES.PROPOSED,
        initiatedBy: 'SELLER',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        escrowLocked: false,
        escrowAmount: 0,
        sellerConfirmed: false,
        buyerConfirmed: false,
        warningsSent: 0,
        extensionGranted: false
      });

      const randomUserId = '999999999999999999';

      await expect(tradeValidator.validateTradeAcceptance(trade, randomUserId))
        .rejects.toThrow('You are not a party to this trade');
    });
  });

  describe('Edge Cases', () => {
    it('should handle non-integer trade amounts', async () => {
      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: 1000.5, // Non-integer
        itemDescription: 'Test Item',
        initiatedBy: 'SELLER'
      })).rejects.toThrow('Trade amount must be a positive integer');
    });

    it('should handle zero or negative amounts', async () => {
      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: 0,
        itemDescription: 'Test Item',
        initiatedBy: 'SELLER'
      })).rejects.toThrow('Trade amount must be a positive integer');

      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: -100,
        itemDescription: 'Test Item',
        initiatedBy: 'SELLER'
      })).rejects.toThrow('Trade amount must be a positive integer');
    });

    it('should handle missing users in database', async () => {
      // Remove one user
      await User.deleteOne({ discordId: testBuyerId });

      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Test Item',
        initiatedBy: 'SELLER'
      })).rejects.toThrow('Buyer not found in the system');
    });

    it('should handle extremely long notes', async () => {
      const longNotes = 'a'.repeat(201); // Exceeds 200 char limit
      
      await expect(tradeValidator.validateTradeCreation({
        sellerId: testSellerId,
        buyerId: testBuyerId,
        guildId: testGuildId,
        amount: testTradeAmount,
        itemDescription: 'Test Item',
        notes: longNotes,
        initiatedBy: 'SELLER'
      })).rejects.toThrow('Notes cannot exceed 200 characters');
    });
  });
});
