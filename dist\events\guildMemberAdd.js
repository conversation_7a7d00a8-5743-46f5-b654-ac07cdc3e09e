"use strict";
/**
 * Guild Member Add Event Handler
 * Handles Discord guild member join events for automated messages and milestone tracking
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuildMemberAddEventHandler = void 0;
const base_1 = require("./base");
/**
 * Guild member add event handler
 */
class GuildMemberAddEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'guildMemberAdd');
        this.name = 'guildMemberAdd';
    }
    /**
     * Execute guild member add event
     */
    async execute(member) {
        try {
            this.logExecution(`Member joined: ${member.displayName}`, {
                userId: member.user.id,
                guildId: member.guild.id,
                guildName: member.guild.name,
            });
            // Process join messages
            if (this.isFeatureEnabled('AUTO_MESSAGES')) {
                await this.processJoinMessages(member);
            }
            // Track login activity for milestones (new member joining counts as login)
            if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
                await this.trackJoinActivity(member);
            }
            // Note: Starter balance is processed in guildMemberUpdate when roles are added
        }
        catch (error) {
            this.handleError(error, {
                userId: member.user.id,
                guildId: member.guild.id,
                displayName: member.displayName,
            });
        }
    }
    /**
     * Process automated join messages
     */
    async processJoinMessages(member) {
        try {
            // Import here to avoid circular dependencies
            const { processJoinMessage } = await Promise.resolve().then(() => __importStar(require('../services/automessageService')));
            const joinResult = await processJoinMessage(member);
            if (joinResult.sent) {
                this.logger.info(`[GuildMemberAdd] Sent ${joinResult.templatesProcessed} join message(s) to ${member.displayName} in ${member.guild.name}`);
            }
            if (joinResult.errors.length > 0) {
                this.logger.error(`[GuildMemberAdd] Errors processing join messages for ${member.displayName}`, {
                    errors: joinResult.errors,
                });
            }
        }
        catch (error) {
            this.logger.error('[GuildMemberAdd] Error processing join messages', {
                error,
                userId: member.user.id,
                guildId: member.guild.id,
            });
        }
    }
    /**
     * Track join activity for milestones
     */
    async trackJoinActivity(member) {
        try {
            // Import here to avoid circular dependencies
            const { checkAndProcessMilestones } = await Promise.resolve().then(() => __importStar(require('../services/milestoneService')));
            const milestoneResults = await checkAndProcessMilestones(this.app.client, member.user.id, member.guild.id, 'login', { timestamp: new Date() });
            if (milestoneResults.length > 0) {
                this.logger.info(`[GuildMemberAdd] New member ${member.displayName} achieved ${milestoneResults.length} milestone(s) on join`);
            }
        }
        catch (error) {
            this.logger.error('[GuildMemberAdd] Error processing join milestones', {
                error,
                userId: member.user.id,
                guildId: member.guild.id,
            });
        }
    }
}
exports.GuildMemberAddEventHandler = GuildMemberAddEventHandler;
//# sourceMappingURL=guildMemberAdd.js.map