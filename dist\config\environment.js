"use strict";
/**
 * Environment Configuration
 * Handles environment variables with validation and type safety
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLoggingConfig = exports.getDiscordConfig = exports.getDatabaseConfig = exports.isFeatureEnabled = exports.isTest = exports.isProduction = exports.isDevelopment = exports.ENV = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const errorHandler_1 = require("../utils/errorHandler");
// Load environment variables
dotenv_1.default.config();
/**
 * Validates and parses environment variables
 */
function validateEnvironment() {
    const errors = [];
    // Required variables
    const BOT_TOKEN = process.env.BOT_TOKEN;
    if (!BOT_TOKEN) {
        errors.push('BOT_TOKEN is required');
    }
    const CLIENT_ID = process.env.CLIENT_ID;
    if (!CLIENT_ID) {
        errors.push('CLIENT_ID is required');
    }
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
        errors.push('MONGODB_URI is required');
    }
    // Node environment
    const NODE_ENV = process.env.NODE_ENV;
    if (!NODE_ENV || !['development', 'production', 'test'].includes(NODE_ENV)) {
        errors.push('NODE_ENV must be one of: development, production, test');
    }
    // Port
    const PORT = parseInt(process.env.PORT || '3000', 10);
    if (isNaN(PORT) || PORT < 1 || PORT > 65535) {
        errors.push('PORT must be a valid port number (1-65535)');
    }
    if (errors.length > 0) {
        throw new errorHandler_1.ValidationError(`Environment validation failed: ${errors.join(', ')}`);
    }
    return {
        BOT_TOKEN: BOT_TOKEN,
        CLIENT_ID: CLIENT_ID,
        MONGODB_URI: MONGODB_URI,
        NODE_ENV: NODE_ENV,
        PORT,
        // Optional feature toggles
        ENABLE_DYNASTY_SYSTEM: process.env.ENABLE_DYNASTY_SYSTEM === 'true',
        ENABLE_MILESTONE_SYSTEM: process.env.ENABLE_MILESTONE_SYSTEM !== 'false', // Default true
        ENABLE_REACTION_REWARDS: process.env.ENABLE_REACTION_REWARDS !== 'false', // Default true
        ENABLE_TAX_SYSTEM: process.env.ENABLE_TAX_SYSTEM !== 'false', // Default true
        // Logging
        LOG_LEVEL: process.env.LOG_LEVEL || 'info',
        LOG_FILE_PATH: process.env.LOG_FILE_PATH,
        // Development
        DEBUG_MODE: process.env.DEBUG_MODE === 'true',
        VERBOSE_LOGGING: process.env.VERBOSE_LOGGING === 'true',
    };
}
/**
 * Validated environment configuration
 */
exports.ENV = validateEnvironment();
/**
 * Helper functions for environment checks
 */
const isDevelopment = () => exports.ENV.NODE_ENV === 'development';
exports.isDevelopment = isDevelopment;
const isProduction = () => exports.ENV.NODE_ENV === 'production';
exports.isProduction = isProduction;
const isTest = () => exports.ENV.NODE_ENV === 'test';
exports.isTest = isTest;
/**
 * Feature flag helpers
 */
const isFeatureEnabled = (feature) => {
    const value = exports.ENV[feature];
    return typeof value === 'boolean' ? value : false;
};
exports.isFeatureEnabled = isFeatureEnabled;
/**
 * Database configuration helper
 */
const getDatabaseConfig = () => ({
    uri: exports.ENV.MONGODB_URI,
    options: {
        maxPoolSize: (0, exports.isProduction)() ? 10 : 5,
        serverSelectionTimeoutMS: 30000,
        socketTimeoutMS: 45000,
        // Removed deprecated options that are not supported in newer MongoDB driver versions:
        // bufferMaxEntries: 0,  // Not supported in MongoDB driver 4.0+
        // bufferCommands: false, // Not supported in MongoDB driver 4.0+
    },
});
exports.getDatabaseConfig = getDatabaseConfig;
/**
 * Discord configuration helper
 */
const getDiscordConfig = () => ({
    token: exports.ENV.BOT_TOKEN,
    clientId: exports.ENV.CLIENT_ID,
    intents: [
        'Guilds',
        'GuildMessages',
        'MessageContent',
        'GuildMembers',
        'GuildMessageReactions'
    ],
});
exports.getDiscordConfig = getDiscordConfig;
const getLoggingConfig = () => ({
    level: exports.ENV.LOG_LEVEL || 'info',
    filePath: exports.ENV.LOG_FILE_PATH,
    console: (0, exports.isDevelopment)(),
    file: (0, exports.isProduction)(),
    format: (0, exports.isProduction)() ? 'json' : 'simple',
});
exports.getLoggingConfig = getLoggingConfig;
/**
 * Export environment for external use
 */
exports.default = exports.ENV;
//# sourceMappingURL=environment.js.map