{"version": 3, "file": "history.js", "sourceRoot": "", "sources": ["../../src/commands/history.ts"], "names": [], "mappings": ";;AAAA,2CAA8E;AAC9E,+DAAmE;AACnE,wDAAwE;AACxE,wDAAsH;AAGtH,2CAA2C;AAC3C,SAAS,mBAAmB,CAAC,IAAY,EAAE,MAAc;IACvD,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,KAAK;YACR,OAAO,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,qBAAM,CAAC,OAAO,CAAC,SAAS,CAAC;QACtE,KAAK,SAAS;YACZ,OAAO,qBAAM,CAAC,KAAK,CAAC,KAAK,CAAC;QAC5B,KAAK,MAAM;YACT,OAAO,qBAAM,CAAC,OAAO,CAAC,IAAI,CAAC;QAC7B,KAAK,MAAM;YACT,OAAO,qBAAM,CAAC,KAAK,CAAC,OAAO,CAAC;QAC9B,KAAK,UAAU;YACb,OAAO,qBAAM,CAAC,OAAO,CAAC,SAAS,CAAC;QAClC;YACE,OAAO,qBAAM,CAAC,OAAO,CAAC,KAAK,CAAC;IAChC,CAAC;AACH,CAAC;AAED,SAAS,eAAe,CAAC,GAAW;IAClC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC;AAED,MAAM,CAAC,OAAO,GAAG;IACf,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC5B,OAAO,CAAC,SAAS,CAAC;SAClB,cAAc,CAAC,sCAAsC,CAAC;IACzD,OAAO,EAAE,IAAA,+BAAgB,EAAC,KAAK,EAAE,WAAwC,EAAE,EAAE;QAC3E,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,MAAM,YAAY,GAAG,MAAM,IAAA,sCAAqB,EAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAEhE,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,KAAK,GAAG,IAAA,iCAAkB,EAAC,qBAAqB,CAAC;qBACpD,cAAc,CAAC,GAAG,qBAAM,CAAC,IAAI,CAAC,MAAM,sFAAsF,CAAC;qBAC3H,SAAS,CAAC,EAAE,IAAI,EAAE,qCAAqC,EAAE,CAAC,CAAC;gBAE9D,IAAA,0BAAW,EAAC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;gBAErC,MAAM,WAAW,CAAC,KAAK,CAAC;oBACtB,MAAM,EAAE,CAAC,KAAK,CAAC;oBACf,SAAS,EAAE,IAAI;iBAChB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,kCAAkC;YAClC,MAAM,iBAAiB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,EAAgB,EAAE,KAAa,EAAE,EAAE;gBAC7E,MAAM,IAAI,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtC,MAAM,KAAK,GAAG,mBAAmB,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;gBACtD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;gBAEjF,OAAO;oBACL,IAAI,EAAE,GAAG,KAAK,IAAI,eAAe,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc;oBACxD,KAAK,EAAE,eAAe,IAAI,GAAG,IAAA,0BAAW,EAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI;wBAC1D,gBAAgB,EAAE,CAAC,OAAO,IAAI,YAAY,IAAI;wBAC9C,aAAa,SAAS,EAAE;oBAC/B,MAAM,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,qCAAqC;iBAC9D,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,IAAA,iCAAkB,EAAC,0BAA0B,CAAC;iBACzD,cAAc,CAAC,GAAG,qBAAM,CAAC,IAAI,CAAC,IAAI,kDAAkD,qBAAM,CAAC,OAAO,CAAC,KAAK,+CAA+C,CAAC;iBACxJ,SAAS,CAAC,GAAG,iBAAiB,CAAC;iBAC/B,SAAS,CAAC;gBACT,IAAI,EAAE,WAAW,YAAY,CAAC,MAAM,4CAA4C;aACjF,CAAC,CAAC;YAEL,6BAA6B;YAC7B,IAAA,0BAAW,EAAC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAErC,MAAM,WAAW,CAAC,KAAK,CAAC;gBACtB,MAAM,EAAE,CAAC,KAAK,CAAC;gBACf,SAAS,EAAE,IAAI,CAAC,mCAAmC;aACpD,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,4BAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACzC,CAAC;YACD,MAAM,IAAI,4BAAa,CAAC,sCAAsC,CAAC,CAAC;QAClE,CAAC;IACH,CAAC,CAAC;CACH,CAAC"}