"use strict";
/**
 * Guild Member Remove Event Handler
 * Handles Discord guild member leave events for user data cleanup
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuildMemberRemoveEventHandler = void 0;
const base_1 = require("./base");
/**
 * Guild member remove event handler
 */
class GuildMemberRemoveEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'guildMemberRemove');
        this.name = 'guildMemberRemove';
    }
    /**
     * Execute guild member remove event
     */
    async execute(member) {
        try {
            // Handle partial members
            const fullMember = await this.ensureFullMember(member);
            const userId = member.user?.id;
            const guildName = member.guild?.name || 'Unknown Guild';
            const displayName = fullMember?.displayName || member.user?.username || 'Unknown User';
            if (!userId) {
                this.logger.error('[GuildMemberRemove] No user ID available for member who left');
                return;
            }
            this.logExecution(`Member left: ${displayName}`, {
                userId,
                guildId: member.guild?.id,
                guildName,
            });
            // Process user data cleanup
            if (this.isFeatureEnabled('USER_CLEANUP')) {
                await this.processUserCleanup(member, userId, displayName, guildName);
            }
        }
        catch (error) {
            this.handleError(error, {
                userId: member.user?.id,
                guildId: member.guild?.id,
                displayName: member.displayName || member.user?.username,
            });
        }
    }
    /**
     * Ensure member is fully fetched
     */
    async ensureFullMember(member) {
        if (member.partial) {
            try {
                return await member.fetch();
            }
            catch (error) {
                this.logger.error('[GuildMemberRemove] Failed to fetch member data', { error });
                // Continue with cleanup using available data
                return null;
            }
        }
        return member;
    }
    /**
     * Process user data cleanup
     */
    async processUserCleanup(member, userId, displayName, guildName) {
        try {
            // Import here to avoid circular dependencies
            const { UserCleanupService } = await Promise.resolve().then(() => __importStar(require('../services/userCleanupService')));
            // Check if we have user data before attempting cleanup
            const userData = await UserCleanupService.checkUserData(userId);
            const hasData = userData.hasUserRecord || userData.transactionCount > 0 || userData.reactionRewardCount > 0;
            if (!hasData) {
                this.logger.info(`[GuildMemberRemove] No data found for ${displayName} (${userId}) who left ${guildName}, skipping cleanup`);
                return;
            }
            this.logger.info(`[GuildMemberRemove] User ${displayName} left ${guildName} - found data: ${userData.hasUserRecord ? 'balance' : ''} ${userData.transactionCount > 0 ? `${userData.transactionCount} transactions` : ''} ${userData.reactionRewardCount > 0 ? `${userData.reactionRewardCount} reaction rewards` : ''}`.trim());
            // Perform cleanup
            const cleanupResult = await UserCleanupService.cleanupUserData(member);
            if (cleanupResult.success) {
                const removedItems = [];
                if (cleanupResult.userDataRemoved)
                    removedItems.push('user balance');
                if (cleanupResult.transactionsRemoved > 0)
                    removedItems.push(`${cleanupResult.transactionsRemoved} transactions`);
                if (cleanupResult.reactionRewardsRemoved > 0)
                    removedItems.push(`${cleanupResult.reactionRewardsRemoved} reaction rewards`);
                this.logger.info(`[GuildMemberRemove] Successfully cleaned up data for ${displayName}: ${removedItems.join(', ')} (${cleanupResult.timeTaken}ms)`);
            }
            else {
                this.logger.error(`[GuildMemberRemove] Failed to clean up data for ${displayName}`, {
                    errors: cleanupResult.errors,
                });
            }
        }
        catch (error) {
            this.logger.error('[GuildMemberRemove] Error processing user cleanup', {
                error,
                userId,
                displayName,
                guildName,
            });
        }
    }
}
exports.GuildMemberRemoveEventHandler = GuildMemberRemoveEventHandler;
//# sourceMappingURL=guildMemberRemove.js.map