import { <PERSON>lash<PERSON><PERSON><PERSON><PERSON>uilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DatabaseError } from '../utils/errorHandler';
import { createAdminEmbed, createErrorEmbed, EMOJIS } from '../utils/embedBuilder';
import { MilestoneIntegrationService } from '../services/milestoneIntegrationService';
import { MilestoneAuditService } from '../services/milestoneAuditService';
import { getUserSecurityReport } from '../services/milestoneService';

module.exports = {
  data: new SlashCommandBuilder()
    .setName('milestonestatus')
    .setDescription('View milestone system status and integration health')
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
    .addSubcommand(subcommand =>
      subcommand
        .setName('system')
        .setDescription('View overall system health and status')
    )
    .addSubcommand(subcommand =>
      subcommand
        .setName('integration')
        .setDescription('View integration status with existing systems')
    )
    .addSubcommand(subcommand =>
      subcommand
        .setName('security')
        .setDescription('View security alerts and suspicious activity')
        .addIntegerOption(option =>
          option
            .setName('hours')
            .setDescription('Hours to look back for alerts (1-168)')
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(168)
        )
    )
    .addSubcommand(subcommand =>
      subcommand
        .setName('user')
        .setDescription('View security report for a specific user')
        .addUserOption(option =>
          option
            .setName('user')
            .setDescription('User to check')
            .setRequired(true)
        )
    )
    .addSubcommand(subcommand =>
      subcommand
        .setName('audit')
        .setDescription('View recent audit logs')
        .addStringOption(option =>
          option
            .setName('category')
            .setDescription('Filter by category')
            .setRequired(false)
            .addChoices(
              { name: 'All', value: 'all' },
              { name: 'Achievements', value: 'achievement' },
              { name: 'Admin Actions', value: 'admin' },
              { name: 'System Events', value: 'system' },
              { name: 'Security', value: 'security' }
            )
        )
        .addIntegerOption(option =>
          option
            .setName('limit')
            .setDescription('Number of logs to show (1-50)')
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(50)
        )
    ),
  execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
    const subcommand = interaction.options.getSubcommand();
    const guildId = interaction.guild!.id;

    switch (subcommand) {
      case 'system':
        await handleSystemStatus(interaction, guildId);
        break;
      case 'integration':
        await handleIntegrationStatus(interaction, guildId);
        break;
      case 'security':
        await handleSecurityStatus(interaction, guildId);
        break;
      case 'user':
        await handleUserSecurity(interaction, guildId);
        break;
      case 'audit':
        await handleAuditLogs(interaction, guildId);
        break;
      default:
        throw new Error('Unknown subcommand');
    }
  })
};

async function handleSystemStatus(interaction: ChatInputCommandInteraction, guildId: string) {
  try {
    const healthCheck = await MilestoneIntegrationService.performHealthCheck(guildId);
    const integrationReport = await MilestoneIntegrationService.getIntegrationReport(guildId);

    const embed = createAdminEmbed(
      `${EMOJIS.MILESTONE.PROGRESS} Milestone System Status`,
      'Overall system health and operational status'
    );

    const statusEmoji = healthCheck.healthy ? '✅' : '⚠️';
    const systemStatus = healthCheck.healthy ? 'Healthy' : 'Issues Detected';

    embed.addFields(
      {
        name: `${statusEmoji} System Health`,
        value: `**Status:** ${systemStatus}\n**Milestone System:** ${healthCheck.milestoneSystemStatus}\n**Integration:** ${healthCheck.integrationStatus}`,
        inline: true
      },
      {
        name: `${EMOJIS.MILESTONE.ACHIEVEMENT} Activity Summary`,
        value: `**Configurations:** ${integrationReport.milestoneConfigurations}\n**Active Users:** ${integrationReport.activeUsers}\n**Recent Achievements:** ${integrationReport.recentAchievements}`,
        inline: true
      },
      {
        name: `${EMOJIS.ADMIN.WARNING} Security Status`,
        value: `**Security Alerts:** ${integrationReport.securityAlerts}\n**System Compatibility:** ${integrationReport.systemCompatibility ? 'Compatible' : 'Issues'}\n**Last Updated:** <t:${Math.floor(integrationReport.lastUpdated.getTime() / 1000)}:R>`,
        inline: true
      }
    );

    if (healthCheck.recommendations.length > 0) {
      embed.addFields({
        name: `${EMOJIS.MISC.LIGHTBULB} Recommendations`,
        value: healthCheck.recommendations.map(r => `• ${r}`).join('\n'),
        inline: false
      });
    }

    if (healthCheck.errors.length > 0) {
      embed.addFields({
        name: `${EMOJIS.ADMIN.WARNING} Errors`,
        value: healthCheck.errors.map(e => `• ${e}`).join('\n'),
        inline: false
      });
    }

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('[Milestone Status] Error getting system status:', error);
    throw new DatabaseError('Failed to retrieve system status');
  }
}

async function handleIntegrationStatus(interaction: ChatInputCommandInteraction, guildId: string) {
  try {
    const integrationReport = await MilestoneIntegrationService.getIntegrationReport(guildId);

    const embed = createAdminEmbed(
      `${EMOJIS.ACTIONS.GEAR} Integration Status`,
      'Milestone system integration with existing bot features'
    );

    const statusColor = integrationReport.status === 'healthy' ? '🟢' : 
                       integrationReport.status === 'warning' ? '🟡' : '🔴';

    embed.addFields(
      {
        name: `${statusColor} Overall Status`,
        value: `**Integration Status:** ${integrationReport.status.toUpperCase()}\n**System Compatibility:** ${integrationReport.systemCompatibility ? 'Compatible' : 'Issues Detected'}`,
        inline: true
      },
      {
        name: `${EMOJIS.MILESTONE.STAR} Configuration`,
        value: `**Milestone Types:** ${integrationReport.milestoneConfigurations}\n**Active Users:** ${integrationReport.activeUsers}\n**Recent Activity:** ${integrationReport.recentAchievements} achievements`,
        inline: true
      },
      {
        name: `${EMOJIS.ADMIN.SETTINGS} System Integration`,
        value: `• **Economy System:** ✅ Compatible\n• **Role Assignment:** ✅ Compatible\n• **Reaction Rewards:** ✅ Compatible\n• **Tax System:** ✅ Compatible\n• **Audit Logging:** ✅ Active`,
        inline: false
      }
    );

    if (integrationReport.securityAlerts > 0) {
      embed.addFields({
        name: `${EMOJIS.ADMIN.WARNING} Security Alerts`,
        value: `${integrationReport.securityAlerts} security alerts in the last 24 hours. Use \`/milestonestatus security\` for details.`,
        inline: false
      });
    }

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('[Milestone Status] Error getting integration status:', error);
    throw new DatabaseError('Failed to retrieve integration status');
  }
}

async function handleSecurityStatus(interaction: ChatInputCommandInteraction, guildId: string) {
  try {
    const hours = interaction.options.getInteger('hours') || 24;
    const securityAlerts = await MilestoneAuditService.getSecurityAlerts(guildId, hours);

    const embed = createAdminEmbed(
      `${EMOJIS.ADMIN.WARNING} Security Status`,
      `Security alerts and suspicious activity in the last ${hours} hours`
    );

    if (securityAlerts.length === 0) {
      embed.addFields({
        name: `${EMOJIS.SUCCESS.CHECK} All Clear`,
        value: `No security alerts found in the last ${hours} hours. System is operating normally.`,
        inline: false
      });
    } else {
      const criticalAlerts = securityAlerts.filter(alert => alert.severity === 'critical').length;
      const highAlerts = securityAlerts.filter(alert => alert.severity === 'high').length;
      const mediumAlerts = securityAlerts.filter(alert => alert.severity === 'medium').length;

      embed.addFields(
        {
          name: `${EMOJIS.ADMIN.WARNING} Alert Summary`,
          value: `**Total Alerts:** ${securityAlerts.length}\n**Critical:** ${criticalAlerts}\n**High:** ${highAlerts}\n**Medium:** ${mediumAlerts}`,
          inline: true
        }
      );

      // Show recent alerts
      const recentAlerts = securityAlerts.slice(0, 5);
      let alertText = '';
      for (const alert of recentAlerts) {
        const severityEmoji = alert.severity === 'critical' ? '🔴' : 
                             alert.severity === 'high' ? '🟠' : '🟡';
        const timeAgo = `<t:${Math.floor(alert.timestamp.getTime() / 1000)}:R>`;
        alertText += `${severityEmoji} **${alert.action}** - ${alert.details.substring(0, 50)}... ${timeAgo}\n`;
      }

      embed.addFields({
        name: `${EMOJIS.MISC.CLOCK} Recent Alerts`,
        value: alertText || 'No recent alerts',
        inline: false
      });

      if (securityAlerts.length > 5) {
        embed.addFields({
          name: `${EMOJIS.ADMIN.INFO} Additional Alerts`,
          value: `${securityAlerts.length - 5} more alerts not shown. Use audit logs for complete history.`,
          inline: false
        });
      }
    }

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('[Milestone Status] Error getting security status:', error);
    throw new DatabaseError('Failed to retrieve security status');
  }
}

async function handleUserSecurity(interaction: ChatInputCommandInteraction, guildId: string) {
  try {
    const user = interaction.options.getUser('user', true);
    const securityReport = await getUserSecurityReport(user.id, guildId);

    const embed = createAdminEmbed(
      `${EMOJIS.ADMIN.LOCK} User Security Report`,
      `Security analysis for ${user.username}`
    );

    const scoreEmoji = securityReport.securityScore === 'critical' ? '🔴' : 
                      securityReport.securityScore === 'high' ? '🟠' : 
                      securityReport.securityScore === 'medium' ? '🟡' : '🟢';

    embed.addFields(
      {
        name: `${scoreEmoji} Security Score`,
        value: `**Risk Level:** ${securityReport.securityScore.toUpperCase()}\n**Blacklisted:** ${securityReport.isBlacklisted ? 'Yes' : 'No'}\n**Suspicious Activities:** ${securityReport.suspiciousActivityCount}`,
        inline: true
      },
      {
        name: `${EMOJIS.MILESTONE.PROGRESS} Activity Summary`,
        value: `**Recent Rate Limits:** ${securityReport.recentRateLimits}\n**Last Suspicious Activity:** ${securityReport.lastSuspiciousActivity ? `<t:${Math.floor(securityReport.lastSuspiciousActivity.getTime() / 1000)}:R>` : 'None'}`,
        inline: true
      }
    );

    if (securityReport.isBlacklisted) {
      embed.addFields({
        name: `${EMOJIS.ADMIN.WARNING} Blacklist Information`,
        value: `**Reason:** ${securityReport.blacklistReason || 'Not specified'}\n**Until:** ${securityReport.blacklistUntil ? `<t:${Math.floor(securityReport.blacklistUntil.getTime() / 1000)}:F>` : 'Permanent'}`,
        inline: false
      });
    }

    if (securityReport.recommendations.length > 0) {
      embed.addFields({
        name: `${EMOJIS.MISC.LIGHTBULB} Recommendations`,
        value: securityReport.recommendations.map(r => `• ${r}`).join('\n'),
        inline: false
      });
    }

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('[Milestone Status] Error getting user security report:', error);
    throw new DatabaseError('Failed to retrieve user security report');
  }
}

async function handleAuditLogs(interaction: ChatInputCommandInteraction, guildId: string) {
  try {
    const category = interaction.options.getString('category') || 'all';
    const limit = interaction.options.getInteger('limit') || 20;

    const options: any = { limit };
    if (category !== 'all') {
      options.category = category;
    }

    const auditLogs = await MilestoneAuditService.getAuditLogs(guildId, options);

    const embed = createAdminEmbed(
      `${EMOJIS.MISC.SCROLL} Audit Logs`,
      `Recent ${category === 'all' ? 'system' : category} activity (last ${auditLogs.length} entries)`
    );

    if (auditLogs.length === 0) {
      embed.addFields({
        name: 'No Logs Found',
        value: 'No audit logs found matching the specified criteria.',
        inline: false
      });
    } else {
      let logText = '';
      for (const log of auditLogs.slice(0, 10)) {
        const timeAgo = `<t:${Math.floor(log.timestamp.getTime() / 1000)}:R>`;
        const categoryEmoji = log.category === 'achievement' ? EMOJIS.MILESTONE.STAR :
                             log.category === 'admin' ? EMOJIS.ADMIN.HAMMER :
                             log.category === 'security' ? EMOJIS.ADMIN.WARNING :
                             EMOJIS.ACTIONS.GEAR;
        logText += `${categoryEmoji} **${log.action}** - ${log.details.substring(0, 60)}... ${timeAgo}\n`;
      }

      embed.addFields({
        name: 'Recent Activity',
        value: logText,
        inline: false
      });

      if (auditLogs.length > 10) {
        embed.addFields({
          name: 'Additional Logs',
          value: `${auditLogs.length - 10} more logs not shown. Use a more specific filter to see additional entries.`,
          inline: false
        });
      }
    }

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('[Milestone Status] Error getting audit logs:', error);
    throw new DatabaseError('Failed to retrieve audit logs');
  }
}
