/**
 * Legacy Reaction Handler
 * Extracted reaction handling from monolithic index.ts
 */
import { Client, MessageReaction, User, PartialMessageReaction, PartialUser } from 'discord.js';
/**
 * Reaction handler for legacy compatibility
 */
export declare class LegacyReactionHandler {
    private client;
    constructor(client: Client);
    /**
     * Handle message reaction add events
     */
    handleReactionAdd(reaction: MessageReaction | PartialMessageReaction, user: User | PartialUser): Promise<void>;
    /**
     * Ensure reaction is fully fetched
     */
    private ensureFullReaction;
    /**
     * Ensure user is fully fetched
     */
    private ensureFullUser;
    /**
     * Process reaction reward
     */
    private processReactionReward;
    /**
     * Track reaction activity for milestones
     */
    private trackReactionActivity;
    /**
     * Get reaction handler statistics
     */
    getStats(): any;
}
export default LegacyReactionHandler;
//# sourceMappingURL=ReactionHandler.d.ts.map