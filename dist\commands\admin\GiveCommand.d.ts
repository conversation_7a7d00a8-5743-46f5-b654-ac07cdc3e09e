/**
 * Give Command
 * Refactored give command using the new command architecture
 */
import { SlashCommandBuilder } from 'discord.js';
import { BaseCommand } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
/**
 * Give command implementation
 */
export declare class GiveCommand extends BaseCommand {
    constructor();
    /**
     * Customize the command builder
     */
    protected customizeCommand(command: SlashCommandBuilder): void;
    /**
     * Execute the give command
     */
    protected executeCommand(context: CommandContext): Promise<void>;
    /**
     * Validate give parameters
     */
    private validateGive;
}
//# sourceMappingURL=GiveCommand.d.ts.map