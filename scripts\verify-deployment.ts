#!/usr/bin/env ts-node

/**
 * Deployment Verification Script
 * Verifies that trade commands are properly deployed and functional
 */

import { REST, Routes } from 'discord.js';
import dotenv from 'dotenv';
import { CommandManager } from '../src/commands/CommandManager';
import { createLogger } from '../src/core/logger';

dotenv.config();

const logger = createLogger('deployment-verification');

interface DeployedCommand {
  id: string;
  name: string;
  description: string;
  options?: any[];
  default_member_permissions?: string;
}

/**
 * Verify deployed commands
 */
async function verifyDeployedCommands(): Promise<boolean> {
  try {
    console.log('🔍 Verifying deployed commands...\n');

    if (!process.env.BOT_TOKEN || !process.env.CLIENT_ID) {
      console.error('❌ BOT_TOKEN and CLIENT_ID environment variables are required');
      return false;
    }

    const rest = new REST({ version: '10' }).setToken(process.env.BOT_TOKEN);

    // Get deployed commands from Discord
    console.log('1. Fetching deployed commands from Discord...');
    const deployedCommands = await rest.get(
      Routes.applicationCommands(process.env.CLIENT_ID)
    ) as DeployedCommand[];

    console.log(`   ✅ Found ${deployedCommands.length} deployed commands`);

    // Check for trade commands
    console.log('\n2. Checking for trade commands:');
    const tradeCommands = ['trade', 'tradeadmin', 'trademonitor'];
    const foundTradeCommands: string[] = [];

    for (const command of deployedCommands) {
      if (tradeCommands.includes(command.name)) {
        foundTradeCommands.push(command.name);
        console.log(`   ✅ Found /${command.name}: ${command.description}`);
        
        // Check permissions for admin commands
        if (['tradeadmin', 'trademonitor'].includes(command.name)) {
          if (command.default_member_permissions) {
            console.log(`      - Permissions: Administrator required ✅`);
          } else {
            console.log(`      - Permissions: ⚠️  No default permissions set`);
          }
        }

        // Check subcommands for trade command
        if (command.name === 'trade' && command.options) {
          const subcommands = command.options.filter(opt => opt.type === 1);
          console.log(`      - Subcommands: ${subcommands.length}`);
          subcommands.forEach(sub => {
            console.log(`        • ${sub.name}: ${sub.description}`);
          });
        }
      }
    }

    // Check for missing commands
    console.log('\n3. Checking for missing commands:');
    const missingCommands = tradeCommands.filter(cmd => !foundTradeCommands.includes(cmd));
    
    if (missingCommands.length > 0) {
      console.log(`   ❌ Missing commands: ${missingCommands.join(', ')}`);
      console.log('   💡 Run "npm run deploy-commands" to deploy missing commands');
      return false;
    } else {
      console.log('   ✅ All trade commands are deployed');
    }

    return true;

  } catch (error) {
    console.error('❌ Failed to verify deployed commands:', error);
    return false;
  }
}

/**
 * Verify local command structure
 */
async function verifyLocalCommands(): Promise<boolean> {
  try {
    console.log('\n🔧 Verifying local command structure...\n');

    // Mock application context
    const mockApp = {
      logger: createLogger('mock-app'),
      getService: (name: string) => {
        // Return mock services
        return {
          initialize: async () => {},
          [name]: true
        };
      }
    };

    console.log('1. Loading local commands...');
    const commandManager = new CommandManager();
    commandManager.setApplicationContext(mockApp);
    
    const stats = await commandManager.loadCommands();
    console.log(`   ✅ Loaded ${stats.totalLoaded} commands (${stats.newArchitecture} new architecture)`);

    if (stats.failedLoads > 0) {
      console.log(`   ⚠️  ${stats.failedLoads} commands failed to load`);
    }

    console.log('\n2. Checking trade command structure:');
    const discordCommands = commandManager.getDiscordCommands();
    
    const tradeCommands = ['trade', 'tradeadmin', 'trademonitor'];
    let allValid = true;

    for (const commandName of tradeCommands) {
      const command = discordCommands.get(commandName);
      
      if (!command) {
        console.log(`   ❌ Command /${commandName} not found`);
        allValid = false;
        continue;
      }

      console.log(`   ✅ /${commandName} loaded successfully`);

      // Verify command data
      if (!command.data) {
        console.log(`      ❌ Missing command data`);
        allValid = false;
        continue;
      }

      // Test serialization
      try {
        const serialized = command.data.toJSON();
        console.log(`      ✅ Serializes correctly`);
        
        // Check required fields
        if (!serialized.name || !serialized.description) {
          console.log(`      ❌ Missing required fields`);
          allValid = false;
        }
      } catch (error) {
        console.log(`      ❌ Serialization failed: ${error}`);
        allValid = false;
      }

      // Check metadata
      const metadata = command.getMetadata();
      console.log(`      - Category: ${metadata.category}`);
      console.log(`      - Admin Only: ${metadata.adminOnly}`);
      console.log(`      - Features: ${metadata.requiredFeatures.join(', ')}`);
    }

    return allValid;

  } catch (error) {
    console.error('❌ Failed to verify local commands:', error);
    return false;
  }
}

/**
 * Verify feature flags
 */
async function verifyFeatureFlags(): Promise<boolean> {
  try {
    console.log('\n🚩 Verifying feature flags...\n');

    const { featureManager } = await import('../src/config/features');

    console.log('1. Checking TRADE_SYSTEM feature:');
    const tradeSystemEnabled = featureManager.isEnabled('TRADE_SYSTEM');
    console.log(`   ✅ TRADE_SYSTEM enabled: ${tradeSystemEnabled}`);

    if (!tradeSystemEnabled) {
      console.log('   ❌ TRADE_SYSTEM feature is disabled!');
      console.log('   💡 Set ENABLE_TRADE_SYSTEM=true in environment or check feature configuration');
      return false;
    }

    console.log('\n2. Checking feature dependencies:');
    const economyEnabled = featureManager.isEnabled('ECONOMY_SYSTEM');
    console.log(`   ✅ ECONOMY_SYSTEM enabled: ${economyEnabled}`);

    if (!economyEnabled) {
      console.log('   ❌ ECONOMY_SYSTEM is required for TRADE_SYSTEM');
      return false;
    }

    console.log('\n3. Feature configuration:');
    const tradeConfig = featureManager.getFeatureConfig('TRADE_SYSTEM');
    if (tradeConfig) {
      console.log(`   - Description: ${tradeConfig.description}`);
      console.log(`   - Dependencies: ${tradeConfig.dependencies?.join(', ') || 'None'}`);
      console.log(`   - Admin Only: ${tradeConfig.adminOnly || false}`);
    }

    return true;

  } catch (error) {
    console.error('❌ Failed to verify feature flags:', error);
    return false;
  }
}

/**
 * Generate deployment report
 */
function generateDeploymentReport(results: {
  localCommands: boolean;
  featureFlags: boolean;
  deployedCommands: boolean;
}) {
  console.log('\n📊 Deployment Verification Report');
  console.log('=' .repeat(50));
  
  console.log(`Local Commands:     ${results.localCommands ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Feature Flags:      ${results.featureFlags ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Deployed Commands:  ${results.deployedCommands ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = Object.values(results).every(result => result);
  
  console.log('\n' + '=' .repeat(50));
  console.log(`Overall Status:     ${allPassed ? '✅ READY FOR USE' : '❌ ISSUES FOUND'}`);
  
  if (allPassed) {
    console.log('\n🎉 Trade system is properly integrated and deployed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Test commands in Discord: /trade help');
    console.log('   2. Verify admin commands: /tradeadmin overview');
    console.log('   3. Check monitoring: /trademonitor health');
  } else {
    console.log('\n🔧 Issues found that need to be resolved:');
    if (!results.localCommands) {
      console.log('   • Fix local command loading issues');
    }
    if (!results.featureFlags) {
      console.log('   • Enable required feature flags');
    }
    if (!results.deployedCommands) {
      console.log('   • Deploy commands to Discord');
    }
  }
  
  return allPassed;
}

/**
 * Main verification function
 */
async function main() {
  console.log('🔍 Trade System Deployment Verification\n');
  console.log('=' .repeat(50));

  const results = {
    localCommands: false,
    featureFlags: false,
    deployedCommands: false
  };

  // Run all verification steps
  results.featureFlags = await verifyFeatureFlags();
  results.localCommands = await verifyLocalCommands();
  results.deployedCommands = await verifyDeployedCommands();

  // Generate report
  const success = generateDeploymentReport(results);

  process.exit(success ? 0 : 1);
}

// Handle errors
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled rejection:', reason);
  process.exit(1);
});

// Run verification
main().catch((error) => {
  console.error('❌ Verification failed:', error);
  process.exit(1);
});
