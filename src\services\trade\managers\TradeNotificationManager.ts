/**
 * Trade Notification Manager
 * Handles Discord notifications for trade events
 */

import { Client, User as DiscordUser, TextChannel, DMChannel } from 'discord.js';
import { BaseService } from '../../base/BaseService';
import { ITrade } from '../../../models';
import {
  createTradeProposalEmbed,
  createTradeProposalButtons,
  createActiveTradeEmbed,
  createActiveTradeButtons,
  createCompletedTradeEmbed,
  createCancelledTradeEmbed
} from '../../../utils/tradeEmbedBuilder';
import { createSuccessEmbed, createInfoEmbed, EMOJIS } from '../../../utils/embedBuilder';

/**
 * Trade Notification Manager Class
 */
export class TradeNotificationManager extends BaseService {
  constructor(app: any) {
    super('TradeNotificationManager', app);
  }

  /**
   * Initialize the notification manager
   */
  async initialize(): Promise<void> {
    this.logger.info('[TradeNotificationManager] Trade notification manager initialized');
  }

  /**
   * Send trade proposal notification
   */
  async sendTradeProposal(trade: ITrade, client: Client): Promise<void> {
    this.logOperation('Sending trade proposal notification', { tradeId: trade.tradeId });

    try {
      const seller = await client.users.fetch(trade.sellerId);
      const buyer = await client.users.fetch(trade.buyerId);

      const embed = createTradeProposalEmbed(trade, seller, buyer);
      const buttons = createTradeProposalButtons(trade.tradeId);

      // Determine who to notify (the recipient of the proposal)
      const recipient = trade.initiatedBy === 'SELLER' ? buyer : seller;
      const initiator = trade.initiatedBy === 'SELLER' ? seller : buyer;

      // Send DM to recipient
      try {
        const dmChannel = await recipient.createDM();
        await dmChannel.send({
          content: `${EMOJIS.TRADE.PROPOSAL} **New Trade Proposal**\n\n${initiator.displayName} has sent you a trade proposal!`,
          embeds: [embed],
          components: [buttons]
        });

        this.logOperation('Trade proposal DM sent', {
          tradeId: trade.tradeId,
          recipient: recipient.id
        });
      } catch (dmError) {
        this.logger.warn('Could not send trade proposal DM', {
          tradeId: trade.tradeId,
          recipient: recipient.id,
          error: dmError
        });
      }

      // Also send to guild channel if possible
      try {
        const guild = await client.guilds.fetch(trade.guildId);
        // You might want to configure a specific trade channel
        // For now, we'll skip guild notifications to avoid spam
      } catch (guildError) {
        this.logger.warn('Could not access guild for trade notification', {
          tradeId: trade.tradeId,
          guildId: trade.guildId,
          error: guildError
        });
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_trade_proposal', tradeId: trade.tradeId });
    }
  }

  /**
   * Send trade accepted notification
   */
  async sendTradeAccepted(trade: ITrade, client: Client): Promise<void> {
    this.logOperation('Sending trade accepted notification', { tradeId: trade.tradeId });

    try {
      const seller = await client.users.fetch(trade.sellerId);
      const buyer = await client.users.fetch(trade.buyerId);

      const embed = createActiveTradeEmbed(trade, seller, buyer);

      // Send to both parties
      const parties = [seller, buyer];

      for (const user of parties) {
        try {
          const userConfirmed = (user.id === trade.sellerId && trade.sellerConfirmed) ||
                               (user.id === trade.buyerId && trade.buyerConfirmed);
          const buttons = createActiveTradeButtons(trade.tradeId, userConfirmed);

          const dmChannel = await user.createDM();
          await dmChannel.send({
            content: `${EMOJIS.TRADE.ACTIVE} **Trade Activated**\n\nYour trade is now active! Funds have been locked in escrow.`,
            embeds: [embed],
            components: [buttons]
          });

          this.logOperation('Trade accepted DM sent', {
            tradeId: trade.tradeId,
            recipient: user.id
          });
        } catch (dmError) {
          this.logger.warn('Could not send trade accepted DM', {
            tradeId: trade.tradeId,
            recipient: user.id,
            error: dmError
          });
        }
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_trade_accepted', tradeId: trade.tradeId });
    }
  }

  /**
   * Send trade completed notification
   */
  async sendTradeCompleted(trade: ITrade, client: Client): Promise<void> {
    this.logOperation('Sending trade completed notification', { tradeId: trade.tradeId });

    try {
      const seller = await client.users.fetch(trade.sellerId);
      const buyer = await client.users.fetch(trade.buyerId);

      const embed = createCompletedTradeEmbed(trade, seller, buyer);

      // Send to both parties
      const parties = [seller, buyer];

      for (const user of parties) {
        try {
          const dmChannel = await user.createDM();
          await dmChannel.send({
            content: `${EMOJIS.TRADE.COMPLETED} **Trade Completed Successfully!**\n\nCongratulations! Your trade has been completed and funds have been released.`,
            embeds: [embed]
          });

          this.logOperation('Trade completed DM sent', {
            tradeId: trade.tradeId,
            recipient: user.id
          });
        } catch (dmError) {
          this.logger.warn('Could not send trade completed DM', {
            tradeId: trade.tradeId,
            recipient: user.id,
            error: dmError
          });
        }
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_trade_completed', tradeId: trade.tradeId });
    }
  }

  /**
   * Send partial confirmation notification
   */
  async sendPartialConfirmation(trade: ITrade, confirmingUserId: string, client: Client): Promise<void> {
    this.logOperation('Sending partial confirmation notification', {
      tradeId: trade.tradeId,
      confirmingUserId
    });

    try {
      const seller = await client.users.fetch(trade.sellerId);
      const buyer = await client.users.fetch(trade.buyerId);
      const confirmingUser = await client.users.fetch(confirmingUserId);

      // Notify the other party
      const otherPartyId = trade.getOtherParty(confirmingUserId);
      if (!otherPartyId) return;

      const otherParty = await client.users.fetch(otherPartyId);
      const embed = createActiveTradeEmbed(trade, seller, buyer);

      try {
        const otherPartyConfirmed = (otherPartyId === trade.sellerId && trade.sellerConfirmed) ||
                                   (otherPartyId === trade.buyerId && trade.buyerConfirmed);
        const buttons = createActiveTradeButtons(trade.tradeId, otherPartyConfirmed);

        const dmChannel = await otherParty.createDM();
        await dmChannel.send({
          content: `${EMOJIS.TRADE.CONFIRMATION} **Trade Confirmation Update**\n\n${confirmingUser.displayName} has confirmed their part of the trade. Please confirm when you have completed your part.`,
          embeds: [embed],
          components: [buttons]
        });

        this.logOperation('Partial confirmation DM sent', {
          tradeId: trade.tradeId,
          recipient: otherPartyId
        });
      } catch (dmError) {
        this.logger.warn('Could not send partial confirmation DM', {
          tradeId: trade.tradeId,
          recipient: otherPartyId,
          error: dmError
        });
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_partial_confirmation', tradeId: trade.tradeId });
    }
  }

  /**
   * Send trade cancelled notification
   */
  async sendTradeCancelled(trade: ITrade, cancellingUserId: string, reason: string | undefined, client: Client): Promise<void> {
    this.logOperation('Sending trade cancelled notification', {
      tradeId: trade.tradeId,
      cancellingUserId,
      reason
    });

    try {
      const seller = await client.users.fetch(trade.sellerId);
      const buyer = await client.users.fetch(trade.buyerId);
      const cancellingUser = await client.users.fetch(cancellingUserId);

      const embed = createCancelledTradeEmbed(trade, seller, buyer, reason);

      // Notify the other party
      const otherPartyId = trade.getOtherParty(cancellingUserId);
      if (otherPartyId) {
        try {
          const otherParty = await client.users.fetch(otherPartyId);
          const dmChannel = await otherParty.createDM();
          await dmChannel.send({
            content: `${EMOJIS.TRADE.CANCELLED} **Trade Cancelled**\n\n${cancellingUser.displayName} has cancelled the trade.`,
            embeds: [embed]
          });

          this.logOperation('Trade cancelled DM sent', {
            tradeId: trade.tradeId,
            recipient: otherPartyId
          });
        } catch (dmError) {
          this.logger.warn('Could not send trade cancelled DM', {
            tradeId: trade.tradeId,
            recipient: otherPartyId,
            error: dmError
          });
        }
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_trade_cancelled', tradeId: trade.tradeId });
    }
  }

  /**
   * Send trade expired notification
   */
  async sendTradeExpired(trade: ITrade, client: Client): Promise<void> {
    this.logOperation('Sending trade expired notification', { tradeId: trade.tradeId });

    try {
      const seller = await client.users.fetch(trade.sellerId);
      const buyer = await client.users.fetch(trade.buyerId);

      const embed = createCancelledTradeEmbed(trade, seller, buyer, 'Trade expired');

      // Send to both parties
      const parties = [seller, buyer];

      for (const user of parties) {
        try {
          const dmChannel = await user.createDM();
          await dmChannel.send({
            content: `${EMOJIS.TRADE.EXPIRED} **Trade Expired**\n\nYour trade has expired and any escrowed funds have been refunded.`,
            embeds: [embed]
          });

          this.logOperation('Trade expired DM sent', {
            tradeId: trade.tradeId,
            recipient: user.id
          });
        } catch (dmError) {
          this.logger.warn('Could not send trade expired DM', {
            tradeId: trade.tradeId,
            recipient: user.id,
            error: dmError
          });
        }
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_trade_expired', tradeId: trade.tradeId });
    }
  }

  /**
   * Send trade warning notification
   */
  async sendTradeWarning(trade: ITrade, hoursRemaining: number, client: Client): Promise<void> {
    this.logOperation('Sending trade warning notification', {
      tradeId: trade.tradeId,
      hoursRemaining
    });

    try {
      const seller = await client.users.fetch(trade.sellerId);
      const buyer = await client.users.fetch(trade.buyerId);

      const embed = createInfoEmbed(
        `${EMOJIS.TRADE.TIMER} Trade Expiring Soon`,
        `Your trade will expire in ${hoursRemaining} hour(s)!`
      );

      embed.addFields([
        {
          name: `${EMOJIS.MISC.ID} Trade ID`,
          value: `\`${trade.tradeId}\``,
          inline: true
        },
        {
          name: `${EMOJIS.TRADE.PACKAGE} Item`,
          value: trade.itemDescription,
          inline: true
        },
        {
          name: `${EMOJIS.ECONOMY.COINS} Amount`,
          value: `${trade.amount} PLC`,
          inline: true
        },
        {
          name: `${EMOJIS.MISC.CLOCK} Expires`,
          value: `<t:${Math.floor(trade.expiresAt.getTime() / 1000)}:R>`,
          inline: false
        }
      ]);

      // Send to both parties
      const parties = [seller, buyer];

      for (const user of parties) {
        try {
          const userConfirmed = (user.id === trade.sellerId && trade.sellerConfirmed) ||
                               (user.id === trade.buyerId && trade.buyerConfirmed);

          let content = `${EMOJIS.ADMIN.WARNING} **Trade Expiring Soon**\n\n`;
          if (trade.state === 'ACTIVE' && !userConfirmed) {
            content += 'Please confirm your trade completion if you have finished your part!';
          } else if (trade.state === 'PROPOSED') {
            content += 'Please accept or decline this trade proposal soon!';
          } else {
            content += 'Your trade is expiring soon!';
          }

          const dmChannel = await user.createDM();
          await dmChannel.send({
            content,
            embeds: [embed]
          });

          this.logOperation('Trade warning DM sent', {
            tradeId: trade.tradeId,
            recipient: user.id,
            hoursRemaining
          });
        } catch (dmError) {
          this.logger.warn('Could not send trade warning DM', {
            tradeId: trade.tradeId,
            recipient: user.id,
            error: dmError
          });
        }
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_trade_warning', tradeId: trade.tradeId });
    }
  }

  /**
   * Send dispute initiated notification
   */
  async sendDisputeInitiated(trade: ITrade, disputingUserId: string, client: Client): Promise<void> {
    this.logOperation('Sending dispute initiated notification', {
      tradeId: trade.tradeId,
      disputingUserId
    });

    try {
      const disputingUser = await client.users.fetch(disputingUserId);

      // Notify the other party
      const otherPartyId = trade.getOtherParty(disputingUserId);
      if (otherPartyId) {
        const otherParty = await client.users.fetch(otherPartyId);

        const embed = createInfoEmbed(
          `${EMOJIS.TRADE.DISPUTED} Trade Dispute Initiated`,
          `A dispute has been initiated for your trade.`
        );

        embed.addFields([
          {
            name: `${EMOJIS.MISC.ID} Trade ID`,
            value: `\`${trade.tradeId}\``,
            inline: true
          },
          {
            name: `${EMOJIS.MISC.USER} Disputed By`,
            value: disputingUser.displayName,
            inline: true
          },
          {
            name: `${EMOJIS.TRADE.PACKAGE} Item`,
            value: trade.itemDescription,
            inline: false
          },
          {
            name: `${EMOJIS.ADMIN.INFO} Next Steps`,
            value: 'An admin will review this dispute. You may be contacted for additional information.',
            inline: false
          }
        ]);

        try {
          const dmChannel = await otherParty.createDM();
          await dmChannel.send({
            content: `${EMOJIS.TRADE.DISPUTED} **Trade Dispute**\n\n${disputingUser.displayName} has initiated a dispute for your trade.`,
            embeds: [embed]
          });

          this.logOperation('Dispute initiated DM sent', {
            tradeId: trade.tradeId,
            recipient: otherPartyId
          });
        } catch (dmError) {
          this.logger.warn('Could not send dispute initiated DM', {
            tradeId: trade.tradeId,
            recipient: otherPartyId,
            error: dmError
          });
        }
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_dispute_initiated', tradeId: trade.tradeId });
    }
  }

  /**
   * Send dispute resolved notification
   */
  async sendDisputeResolved(trade: ITrade, resolution: string, client: Client): Promise<void> {
    this.logOperation('Sending dispute resolved notification', {
      tradeId: trade.tradeId,
      resolution
    });

    try {
      const seller = await client.users.fetch(trade.sellerId);
      const buyer = await client.users.fetch(trade.buyerId);

      const embed = createSuccessEmbed(
        `${EMOJIS.TRADE.SCALES} Dispute Resolved`,
        `The dispute for your trade has been resolved.`
      );

      embed.addFields([
        {
          name: `${EMOJIS.MISC.ID} Trade ID`,
          value: `\`${trade.tradeId}\``,
          inline: true
        },
        {
          name: `${EMOJIS.ADMIN.SCALES} Resolution`,
          value: resolution,
          inline: true
        },
        {
          name: `${EMOJIS.TRADE.PACKAGE} Item`,
          value: trade.itemDescription,
          inline: false
        }
      ]);

      // Send to both parties
      const parties = [seller, buyer];

      for (const user of parties) {
        try {
          const dmChannel = await user.createDM();
          await dmChannel.send({
            content: `${EMOJIS.ADMIN.SCALES} **Dispute Resolved**\n\nThe dispute for your trade has been resolved by an administrator.`,
            embeds: [embed]
          });

          this.logOperation('Dispute resolved DM sent', {
            tradeId: trade.tradeId,
            recipient: user.id
          });
        } catch (dmError) {
          this.logger.warn('Could not send dispute resolved DM', {
            tradeId: trade.tradeId,
            recipient: user.id,
            error: dmError
          });
        }
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_dispute_resolved', tradeId: trade.tradeId });
    }
  }
}
