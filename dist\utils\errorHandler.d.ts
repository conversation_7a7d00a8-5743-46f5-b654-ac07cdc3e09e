import { ChatInputCommandInteraction, ButtonInteraction } from 'discord.js';
export declare class CommandError extends Error {
    readonly ephemeral: boolean;
    readonly debug?: string;
    readonly category: string;
    readonly userGuidance?: string;
    constructor(message: string, ephemeral?: boolean, debug?: string, category?: string, userGuidance?: string);
}
export declare class DatabaseError extends CommandError {
    constructor(message: string, originalError?: Error | unknown);
}
export declare class ValidationError extends CommandError {
    constructor(message: string, field?: string, expectedFormat?: string);
}
export declare class PermissionError extends CommandError {
    constructor(message?: string, requiredPermission?: string);
}
export declare class InsufficientFundsError extends CommandError {
    constructor(required: number, available: number);
}
export declare class RateLimitError extends CommandError {
    constructor(retryAfter: number);
}
export declare class BalanceError extends InsufficientFundsError {
    constructor(message: string, required?: number, available?: number);
}
export declare function handleCommandError(interaction: ChatInputCommandInteraction, error: unknown): Promise<void>;
export declare function handleButtonError(interaction: ButtonInteraction, error: unknown): Promise<void>;
export declare function withErrorHandler(commandFn: (interaction: ChatInputCommandInteraction) => Promise<void>): (interaction: ChatInputCommandInteraction) => Promise<void>;
//# sourceMappingURL=errorHandler.d.ts.map