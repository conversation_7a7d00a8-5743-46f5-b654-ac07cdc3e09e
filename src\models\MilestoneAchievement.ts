import { Schema, model, Document } from 'mongoose';

export interface IMilestoneAchievement extends Document {
    discordId: string;
    guildId: string;
    milestoneType: string;
    category: 'time_based' | 'participation_diversity' | 'loyalty' | 'engagement';
    
    // Achievement details
    achievementValue: number; // The value that triggered the milestone (e.g., streak days, channels used)
    rewardAmount: number;
    diminishingFactor: number; // Factor applied for this achievement
    achievementCount: number; // How many times this milestone type has been achieved
    
    // Metadata
    details: string; // Human-readable description
    timestamp: Date;
    
    // Weekly/Daily tracking for caps
    weekNumber: number; // Week of year
    dayOfYear: number; // Day of year
    year: number;
    
    createdAt: Date;
}

const milestoneAchievementSchema = new Schema<IMilestoneAchievement>({
    discordId: {
        type: String,
        required: [true, 'Discord ID is required'],
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true
    },
    milestoneType: {
        type: String,
        required: [true, 'Milestone type is required'],
        index: true
    },
    category: {
        type: String,
        enum: ['time_based', 'participation_diversity', 'loyalty', 'engagement'],
        required: [true, 'Category is required'],
        index: true
    },
    
    // Achievement details
    achievementValue: {
        type: Number,
        required: [true, 'Achievement value is required'],
        min: [0, 'Achievement value cannot be negative']
    },
    rewardAmount: {
        type: Number,
        required: [true, 'Reward amount is required'],
        min: [0, 'Reward amount cannot be negative']
    },
    diminishingFactor: {
        type: Number,
        required: [true, 'Diminishing factor is required'],
        min: [0.1, 'Diminishing factor must be at least 0.1'],
        max: [1.0, 'Diminishing factor cannot exceed 1.0']
    },
    achievementCount: {
        type: Number,
        required: [true, 'Achievement count is required'],
        min: [1, 'Achievement count must be positive']
    },
    
    // Metadata
    details: {
        type: String,
        required: [true, 'Details are required']
    },
    timestamp: {
        type: Date,
        default: Date.now,
        index: true
    },
    
    // Weekly/Daily tracking for caps
    weekNumber: {
        type: Number,
        required: [true, 'Week number is required'],
        min: [1, 'Week number must be between 1-53'],
        max: [53, 'Week number must be between 1-53'],
        index: true
    },
    dayOfYear: {
        type: Number,
        required: [true, 'Day of year is required'],
        min: [1, 'Day of year must be between 1-366'],
        max: [366, 'Day of year must be between 1-366'],
        index: true
    },
    year: {
        type: Number,
        required: [true, 'Year is required'],
        index: true
    }
}, {
    timestamps: { createdAt: true, updatedAt: false }
});

// Compound indexes for efficient queries
milestoneAchievementSchema.index({ discordId: 1, guildId: 1, timestamp: -1 });
milestoneAchievementSchema.index({ discordId: 1, milestoneType: 1, timestamp: -1 });
milestoneAchievementSchema.index({ guildId: 1, category: 1, timestamp: -1 });

// Indexes for cap checking
milestoneAchievementSchema.index({ discordId: 1, year: 1, weekNumber: 1 });
milestoneAchievementSchema.index({ discordId: 1, year: 1, dayOfYear: 1 });

// Index for achievement counting
milestoneAchievementSchema.index({ discordId: 1, milestoneType: 1, achievementCount: 1 });

export default model<IMilestoneAchievement>('MilestoneAchievement', milestoneAchievementSchema);
