"use strict";
/**
 * Give Command
 * Refactored give command using the new command architecture
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GiveCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const embedBuilder_1 = require("../../utils/embedBuilder");
const economyService_1 = require("../../services/economyService");
const errorHandler_1 = require("../../utils/errorHandler");
const constants_1 = require("../../config/constants");
/**
 * Give command implementation
 */
class GiveCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'give',
            description: 'Give coins to a user (admin only)',
            category: BaseCommand_1.CommandCategory.ADMIN,
            adminOnly: true,
            requiredFeatures: ['ECONOMY_SYSTEM'],
            requiredPermissions: ['Administrator'],
        });
    }
    /**
     * Customize the command builder
     */
    customizeCommand(command) {
        command
            .addUserOption(option => option.setName('user')
            .setDescription('The user to give coins to')
            .setRequired(true))
            .addIntegerOption(option => option.setName('amount')
            .setDescription('Amount of coins to give')
            .setRequired(true)
            .setMinValue(constants_1.VALIDATION.MIN_TRANSACTION_AMOUNT)
            .setMaxValue(constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT))
            .addStringOption(option => option.setName('reason')
            .setDescription('Reason for giving coins (optional)')
            .setRequired(false)
            .setMaxLength(constants_1.VALIDATION.MAX_REASON_LENGTH));
    }
    /**
     * Execute the give command
     */
    async executeCommand(context) {
        const { interaction } = context;
        const targetUser = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);
        const reason = interaction.options.getString('reason') || 'Administrative action';
        // Validation
        this.validateGive(targetUser.id, amount);
        try {
            // Adjust balance - this will automatically handle user creation with upsert
            await (0, economyService_1.adjustBalance)(targetUser.id, amount, 'give', `Given by admin ${interaction.user.username} (${interaction.user.id}): ${reason}`, interaction.client, interaction.guild?.id);
            // Create rich admin success embed
            const embed = (0, embedBuilder_1.createSuccessEmbed)('Coins Awarded Successfully!')
                .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.HAMMER} **Administrative Action Completed**\n\n` +
                `${(0, embedBuilder_1.formatCoins)(amount)} has been awarded to **${targetUser.displayName}**!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.KEY} Administrator`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ACTIONS.TARGET} Recipient`,
                value: `**${targetUser.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
                value: (0, embedBuilder_1.formatCoins)(amount),
                inline: true
            });
            if (reason !== 'Administrative action') {
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Reason`,
                    value: reason,
                    inline: false
                });
            }
            // Add admin info
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            this.logger.info(`Admin ${interaction.user.username} gave ${amount} PLC to ${targetUser.username}`, {
                adminId: interaction.user.id,
                recipientId: targetUser.id,
                amount,
                reason,
                guildId: interaction.guild?.id,
            });
            // Try to notify recipient via DM
            try {
                const recipientEmbed = (0, embedBuilder_1.createSuccessEmbed)('Coins Received!')
                    .setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.COINS} You received ${(0, embedBuilder_1.formatCoins)(amount)} from an administrator!\n\n` +
                    `**Reason:** ${reason}`);
                await targetUser.send({ embeds: [recipientEmbed] });
            }
            catch (error) {
                // DM failed, but give was successful
                this.logger.debug(`Failed to send give notification DM to ${targetUser.username}`, { error });
            }
        }
        catch (error) {
            this.logger.error('Error executing give command', {
                error,
                adminId: interaction.user.id,
                recipientId: targetUser.id,
                amount,
                reason
            });
            throw error;
        }
    }
    /**
     * Validate give parameters
     */
    validateGive(recipientId, amount) {
        if (amount <= 0) {
            throw new errorHandler_1.ValidationError('Amount must be greater than 0.');
        }
        if (amount > constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT) {
            throw new errorHandler_1.ValidationError(`Amount cannot exceed ${(0, embedBuilder_1.formatCoins)(constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT)}.`);
        }
    }
}
exports.GiveCommand = GiveCommand;
//# sourceMappingURL=GiveCommand.js.map