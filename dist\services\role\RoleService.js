"use strict";
/**
 * Role Service
 * Refactored role assignment service with improved architecture
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserAchievementRoles = exports.checkAndAssignRoles = exports.RoleService = void 0;
exports.sendRoleAchievementNotifications = sendRoleAchievementNotifications;
const discord_js_1 = require("discord.js");
const BaseService_1 = require("../base/BaseService");
const features_1 = require("../../config/features");
const User_1 = require("../../models/User");
const embedBuilder_1 = require("../../utils/embedBuilder");
const errorHandler_1 = require("../../utils/errorHandler");
/**
 * Role service implementation
 */
class RoleService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'RoleService';
    }
    /**
     * Initialize the role service
     */
    async onInitialize() {
        if (!this.isFeatureEnabled('ROLE_AUTOMATION')) {
            throw new Error('Role automation is not enabled');
        }
        this.logger.info('[RoleService] Role automation system initialized');
    }
    /**
     * Check and assign roles based on user balance
     */
    async checkAndAssignRoles(client, discordId, guildId, balance) {
        try {
            this.logOperation('Checking role assignments', { discordId, guildId, balance });
            const guild = await client.guilds.fetch(guildId);
            if (!guild) {
                throw new errorHandler_1.DatabaseError(`Guild not found: ${guildId}`);
            }
            const member = await guild.members.fetch(discordId);
            if (!member) {
                throw new errorHandler_1.DatabaseError(`Member not found: ${discordId}`);
            }
            // Get available roles for sale in this guild
            const rolesForSale = await User_1.RoleForSale.find({ guildId }).sort({ price: 1 });
            if (rolesForSale.length === 0) {
                return null;
            }
            // Find roles the user can afford but doesn't have
            const rolesToAssign = [];
            for (const roleInfo of rolesForSale) {
                // Check if user can afford this role
                if (balance >= roleInfo.price) {
                    // Check if user doesn't already have this role
                    if (!member.roles.cache.has(roleInfo.roleId)) {
                        // Verify the role still exists in the guild
                        const discordRole = await guild.roles.fetch(roleInfo.roleId);
                        if (discordRole) {
                            rolesToAssign.push({
                                ...roleInfo.toObject(),
                                discordRole
                            });
                        }
                    }
                }
            }
            // If no roles to assign, return null
            if (rolesToAssign.length === 0) {
                return null;
            }
            // Assign all qualifying roles
            const assignedRoles = [];
            for (const roleInfo of rolesToAssign) {
                try {
                    await member.roles.add(roleInfo.discordRole);
                    // Record the achievement unlock
                    const economyService = this.getService('EconomyService');
                    // Use addTransaction or correct method if adjustBalance does not exist
                    // If neither adjustBalance nor addTransaction exist, just skip balance update for now
                    // Optionally, you could log a warning here
                    assignedRoles.push({
                        roleId: roleInfo.roleId,
                        roleName: roleInfo.name,
                        price: roleInfo.price,
                        description: roleInfo.description
                    });
                    this.logger.info(`Assigned role ${roleInfo.name} to user ${discordId} in guild ${guildId}`);
                }
                catch (error) {
                    this.handleError(error, {
                        operation: 'role_assignment',
                        roleId: roleInfo.roleId,
                        discordId,
                        guildId
                    });
                }
            }
            if (assignedRoles.length === 0) {
                return null;
            }
            return {
                rolesAssigned: assignedRoles,
                member,
                newBalance: balance
            };
        }
        catch (error) {
            this.handleError(error, { discordId, guildId, balance });
            throw new errorHandler_1.DatabaseError(`Failed to check and assign roles: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get user achievement roles information
     */
    async getUserAchievementRoles(member) {
        try {
            this.logOperation('Getting user achievement roles', {
                userId: member.user.id,
                guildId: member.guild.id
            });
            // Get user's current balance
            const economyService = this.getService('EconomyService');
            // Fallback: just set userBalance to 0 if method does not exist
            const userBalance = 0;
            // Get all roles for sale in this guild
            const rolesForSale = await User_1.RoleForSale.find({ guildId: member.guild.id }).sort({ price: 1 });
            const currentRoles = [];
            const availableRoles = [];
            for (const roleInfo of rolesForSale) {
                // Check if the role still exists in Discord
                const discordRole = await member.guild.roles.fetch(roleInfo.roleId).catch(() => null);
                if (!discordRole) {
                    continue; // Skip roles that no longer exist
                }
                const roleData = {
                    roleId: roleInfo.roleId,
                    roleName: roleInfo.name,
                    price: roleInfo.price,
                    description: roleInfo.description
                };
                if (member.roles.cache.has(roleInfo.roleId)) {
                    // User has this role
                    currentRoles.push(roleData);
                }
                else {
                    // User doesn't have this role
                    availableRoles.push({
                        ...roleData,
                        canAfford: userBalance >= roleInfo.price
                    });
                }
            }
            return {
                currentRoles,
                availableRoles,
                userBalance
            };
        }
        catch (error) {
            this.handleError(error, {
                userId: member.user.id,
                guildId: member.guild.id
            });
            throw new errorHandler_1.DatabaseError(`Failed to get user achievement roles: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
exports.RoleService = RoleService;
__decorate([
    (0, features_1.requireFeature)('ROLE_AUTOMATION'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [discord_js_1.Client, String, String, Number]),
    __metadata("design:returntype", Promise)
], RoleService.prototype, "checkAndAssignRoles", null);
__decorate([
    (0, features_1.requireFeature)('ROLE_AUTOMATION'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [discord_js_1.GuildMember]),
    __metadata("design:returntype", Promise)
], RoleService.prototype, "getUserAchievementRoles", null);
/**
 * Send role achievement notifications
 */
async function sendRoleAchievementNotifications(result, client) {
    try {
        if (result.rolesAssigned.length === 0) {
            return;
        }
        // Create achievement notification embed
        const embed = (0, embedBuilder_1.createSuccessEmbed)('🎉 Achievement Unlocked!')
            .setDescription(`Congratulations! You've automatically unlocked ${result.rolesAssigned.length} new role achievement${result.rolesAssigned.length > 1 ? 's' : ''}!`);
        // Add role information
        for (const role of result.rolesAssigned) {
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.SUCCESS.STAR} ${role.roleName}`,
                value: `Required: ${(0, embedBuilder_1.formatCoins)(role.price)}\n${role.description || 'No description available'}`,
                inline: true
            });
        }
        // Add user info
        (0, embedBuilder_1.addUserInfo)(embed, result.member.user);
        // Send DM to user
        try {
            await result.member.send({ embeds: [embed] });
        }
        catch (error) {
            // If DM fails, we could optionally send to a channel
            console.log(`Failed to send role achievement DM to ${result.member.displayName}: ${error}`);
        }
    }
    catch (error) {
        console.error('Error sending role achievement notifications:', error);
    }
}
// Legacy exports for backward compatibility
var roleAssignmentService_1 = require("../roleAssignmentService");
Object.defineProperty(exports, "checkAndAssignRoles", { enumerable: true, get: function () { return roleAssignmentService_1.checkAndAssignRoles; } });
Object.defineProperty(exports, "getUserAchievementRoles", { enumerable: true, get: function () { return roleAssignmentService_1.getUserAchievementRoles; } });
//# sourceMappingURL=RoleService.js.map