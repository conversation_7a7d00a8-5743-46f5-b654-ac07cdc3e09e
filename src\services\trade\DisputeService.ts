/**
 * Dispute Service
 * Handles trade dispute resolution workflow
 */

import mongoose from 'mongoose';
import { Client } from 'discord.js';
import { BaseService } from '../base/BaseService';
import { requireFeature } from '../../core/decorators';
import { DatabaseError, ValidationError } from '../../utils/errorHandler';
import { TRADE } from '../../config/constants';
import { 
  Trade, 
  ITrade, 
  DisputeCase, 
  IDisputeCase,
  UserTradeStats,
  EscrowTransaction
} from '../../models';
import { EscrowManager } from './managers/EscrowManager';
import { TradeNotificationManager } from './managers/TradeNotificationManager';

export interface DisputeCreationParams {
  tradeId: string;
  initiatorId: string;
  reason: string;
  description?: string;
  category: 'ITEM_NOT_RECEIVED' | 'ITEM_NOT_AS_DESCRIBED' | 'PAYMENT_ISSUE' | 'COMMUNICATION_ISSUE' | 'OTHER';
}

export interface DisputeResolutionParams {
  disputeId: string;
  adminId: string;
  resolution: 'FAVOR_INITIATOR' | 'FAVOR_RESPONDENT' | 'SPLIT_ESCROW' | 'FULL_REFUND' | 'CUSTOM';
  resolutionDetails: string;
  resolutionAmount?: number; // For custom resolutions
  adminNotes?: string;
}

/**
 * Dispute Service Class
 */
export class DisputeService extends BaseService {
  private escrowManager: EscrowManager;
  private notificationManager: TradeNotificationManager;

  constructor(app: any) {
    super('DisputeService', app);
    this.escrowManager = new EscrowManager(app);
    this.notificationManager = new TradeNotificationManager(app);
  }

  /**
   * Initialize the dispute service
   */
  @requireFeature('TRADE_SYSTEM')
  async onInitialize(): Promise<void> {
    this.logger.info('[DisputeService] Dispute service initialized');
    
    // Initialize sub-managers
    await this.escrowManager.initialize();
    await this.notificationManager.initialize();
  }

  /**
   * Initiate a dispute for a trade
   */
  @requireFeature('TRADE_SYSTEM')
  async initiateDispute(params: DisputeCreationParams, client?: Client): Promise<IDisputeCase> {
    this.logOperation('Initiating trade dispute', params);

    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Get and validate trade
        const trade = await Trade.findOne({ tradeId: params.tradeId }).session(session);
        if (!trade) {
          throw new ValidationError('Trade not found');
        }

        // Validate dispute initiation
        this.validateDisputeInitiation(trade, params.initiatorId);

        // Get respondent ID
        const respondentId = trade.getOtherParty(params.initiatorId);
        if (!respondentId) {
          throw new ValidationError('Could not determine other party in trade');
        }

        // Generate dispute ID
        const disputeId = this.generateDisputeId();
        
        // Set evidence deadline (48 hours from now)
        const evidenceDeadline = new Date();
        evidenceDeadline.setHours(evidenceDeadline.getHours() + 48);

        // Create dispute case
        const disputeCase = await DisputeCase.create([{
          disputeId,
          tradeId: trade.tradeId,
          guildId: trade.guildId,
          initiatorId: params.initiatorId,
          respondentId,
          reason: params.reason,
          description: params.description,
          category: params.category,
          evidenceDeadline,
          status: 'EVIDENCE_COLLECTION',
          priority: this.calculateDisputePriority(trade, params),
          createdAt: new Date(),
          lastUpdatedAt: new Date(),
          adminNotes: [],
          escalationLevel: 0,
          tags: [params.category.toLowerCase()]
        }], { session });

        // Update trade state
        trade.state = TRADE.STATES.DISPUTED;
        trade.disputeId = disputeId;
        trade.disputedBy = params.initiatorId;
        trade.disputedAt = new Date();
        trade.disputeReason = params.reason;
        await trade.save({ session });

        // Update user trade stats
        await this.updateUserTradeStats(params.initiatorId, trade.guildId, 'DISPUTE_INITIATED', session);
        await this.updateUserTradeStats(respondentId, trade.guildId, 'DISPUTE_RECEIVED', session);

        this.logOperation('Dispute initiated successfully', { 
          disputeId, 
          tradeId: trade.tradeId,
          initiatorId: params.initiatorId 
        });

        // Send notifications (outside transaction)
        if (client) {
          setImmediate(async () => {
            try {
              await this.notificationManager.sendDisputeInitiated(trade, params.initiatorId, client);
            } catch (error) {
              this.handleError(error, { operation: 'dispute_initiated_notification' });
            }
          });
        }

        return disputeCase[0];
      });
    } catch (error) {
      this.handleError(error, { operation: 'initiate_dispute', params });
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Resolve a dispute
   */
  @requireFeature('TRADE_SYSTEM')
  async resolveDispute(params: DisputeResolutionParams, client?: Client): Promise<IDisputeCase> {
    this.logOperation('Resolving dispute', params);

    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Get dispute case
        const disputeCase = await DisputeCase.findOne({ disputeId: params.disputeId }).session(session);
        if (!disputeCase) {
          throw new ValidationError('Dispute case not found');
        }

        // Get associated trade
        const trade = await Trade.findOne({ tradeId: disputeCase.tradeId }).session(session);
        if (!trade) {
          throw new ValidationError('Associated trade not found');
        }

        // Validate resolution
        this.validateDisputeResolution(disputeCase, params.adminId);

        // Execute resolution
        await this.executeResolution(trade, disputeCase, params, session);

        // Update dispute case
        disputeCase.status = 'RESOLVED';
        disputeCase.resolution = params.resolution;
        disputeCase.resolutionDetails = params.resolutionDetails;
        disputeCase.resolutionAmount = params.resolutionAmount;
        disputeCase.resolvedAt = new Date();
        disputeCase.assignedAdminId = params.adminId;
        
        if (params.adminNotes) {
          disputeCase.addAdminNote(params.adminNotes, params.adminId);
        }
        
        await disputeCase.save({ session });

        // Update trade state
        trade.state = TRADE.STATES.COMPLETED; // Dispute resolved = trade completed
        trade.completedAt = new Date();
        await trade.save({ session });

        // Update user trade stats
        await this.updateUserTradeStats(
          disputeCase.initiatorId, 
          trade.guildId, 
          'DISPUTE_RESOLVED', 
          session,
          { wasSuccessful: params.resolution === 'FAVOR_INITIATOR' }
        );
        await this.updateUserTradeStats(
          disputeCase.respondentId, 
          trade.guildId, 
          'DISPUTE_RESOLVED', 
          session,
          { wasSuccessful: params.resolution === 'FAVOR_RESPONDENT' }
        );

        this.logOperation('Dispute resolved successfully', { 
          disputeId: params.disputeId,
          resolution: params.resolution,
          adminId: params.adminId
        });

        // Send notifications (outside transaction)
        if (client) {
          setImmediate(async () => {
            try {
              await this.notificationManager.sendDisputeResolved(trade, params.resolutionDetails, client);
            } catch (error) {
              this.handleError(error, { operation: 'dispute_resolved_notification' });
            }
          });
        }

        return disputeCase;
      });
    } catch (error) {
      this.handleError(error, { operation: 'resolve_dispute', params });
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Get dispute case by ID
   */
  @requireFeature('TRADE_SYSTEM')
  async getDispute(disputeId: string): Promise<IDisputeCase | null> {
    try {
      return await DisputeCase.findOne({ disputeId }).lean();
    } catch (error) {
      this.handleError(error, { operation: 'get_dispute', disputeId });
      throw error;
    }
  }

  /**
   * Get active disputes for admin review
   */
  @requireFeature('TRADE_SYSTEM')
  async getActiveDisputes(guildId?: string, limit: number = 20): Promise<IDisputeCase[]> {
    try {
      const query: any = {
        status: { $in: ['OPEN', 'EVIDENCE_COLLECTION', 'UNDER_REVIEW'] }
      };

      if (guildId) {
        query.guildId = guildId;
      }

      return await DisputeCase.find(query)
        .sort({ priority: -1, createdAt: -1 })
        .limit(limit)
        .lean();
    } catch (error) {
      this.handleError(error, { operation: 'get_active_disputes', guildId });
      throw error;
    }
  }

  /**
   * Add evidence to a dispute
   */
  @requireFeature('TRADE_SYSTEM')
  async addEvidence(disputeId: string, userId: string, evidence: string[]): Promise<IDisputeCase> {
    this.logOperation('Adding evidence to dispute', { disputeId, userId, evidenceCount: evidence.length });

    try {
      const disputeCase = await DisputeCase.findOne({ disputeId });
      if (!disputeCase) {
        throw new ValidationError('Dispute case not found');
      }

      // Validate user is involved
      if (userId !== disputeCase.initiatorId && userId !== disputeCase.respondentId) {
        throw new ValidationError('You are not a party to this dispute');
      }

      // Check evidence deadline
      if (disputeCase.isEvidenceDeadlinePassed()) {
        throw new ValidationError('Evidence submission deadline has passed');
      }

      // Add evidence to appropriate array
      if (userId === disputeCase.initiatorId) {
        disputeCase.initiatorEvidence.push(...evidence);
      } else {
        disputeCase.respondentEvidence.push(...evidence);
      }

      await disputeCase.save();

      this.logOperation('Evidence added successfully', { 
        disputeId, 
        userId, 
        evidenceCount: evidence.length 
      });

      return disputeCase;
    } catch (error) {
      this.handleError(error, { operation: 'add_evidence', disputeId, userId });
      throw error;
    }
  }

  // Private helper methods

  private validateDisputeInitiation(trade: ITrade, initiatorId: string): void {
    if (trade.state !== TRADE.STATES.ACTIVE) {
      throw new ValidationError('Disputes can only be initiated for active trades');
    }

    if (!trade.involvesUser(initiatorId)) {
      throw new ValidationError('You are not a party to this trade');
    }

    if (trade.disputeId) {
      throw new ValidationError('This trade already has an active dispute');
    }
  }

  private validateDisputeResolution(disputeCase: IDisputeCase, adminId: string): void {
    if (!disputeCase.isActive()) {
      throw new ValidationError('Dispute is not in an active state');
    }

    // Add admin permission validation here if needed
    // For now, we assume any user with admin permissions can resolve disputes
  }

  private async executeResolution(
    trade: ITrade, 
    disputeCase: IDisputeCase, 
    params: DisputeResolutionParams, 
    session: mongoose.ClientSession
  ): Promise<void> {
    if (!trade.escrowLocked || trade.escrowAmount <= 0) {
      // No escrow to distribute
      return;
    }

    switch (params.resolution) {
      case 'FAVOR_INITIATOR':
        // Refund to buyer (initiator gets the money back)
        if (trade.buyerId === disputeCase.initiatorId) {
          await this.escrowManager.refundEscrow(trade, 'Dispute resolved in favor of initiator', session);
        } else {
          await this.escrowManager.releaseEscrow(trade, 'Dispute resolved in favor of initiator', session);
        }
        break;

      case 'FAVOR_RESPONDENT':
        // Release to seller (respondent gets the money)
        if (trade.sellerId === disputeCase.respondentId) {
          await this.escrowManager.releaseEscrow(trade, 'Dispute resolved in favor of respondent', session);
        } else {
          await this.escrowManager.refundEscrow(trade, 'Dispute resolved in favor of respondent', session);
        }
        break;

      case 'SPLIT_ESCROW':
        // Split 50/50
        const halfAmount = Math.floor(trade.escrowAmount / 2);
        const remainder = trade.escrowAmount - halfAmount;
        await this.escrowManager.splitEscrow(
          trade, 
          trade.sellerId === disputeCase.initiatorId ? halfAmount : remainder,
          trade.buyerId === disputeCase.initiatorId ? halfAmount : remainder,
          'Dispute resolved with split escrow',
          session
        );
        break;

      case 'FULL_REFUND':
        await this.escrowManager.refundEscrow(trade, 'Dispute resolved with full refund', session);
        break;

      case 'CUSTOM':
        if (params.resolutionAmount === undefined) {
          throw new ValidationError('Custom resolution requires resolution amount');
        }
        const sellerAmount = params.resolutionAmount;
        const buyerAmount = trade.escrowAmount - sellerAmount;
        await this.escrowManager.splitEscrow(
          trade,
          sellerAmount,
          buyerAmount,
          'Dispute resolved with custom amount',
          session
        );
        break;
    }
  }

  private calculateDisputePriority(trade: ITrade, params: DisputeCreationParams): 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' {
    // High value trades get higher priority
    if (trade.amount >= 10000) return 'HIGH';
    if (trade.amount >= 5000) return 'MEDIUM';
    
    // Certain categories get higher priority
    if (params.category === 'PAYMENT_ISSUE') return 'HIGH';
    
    return 'LOW';
  }

  private async updateUserTradeStats(
    discordId: string, 
    guildId: string, 
    action: string, 
    session: mongoose.ClientSession,
    data?: any
  ): Promise<void> {
    // Implementation similar to TradeService.updateUserTradeStats
    // This is a simplified version
    try {
      const userStats = await UserTradeStats.findOne({ discordId, guildId }).session(session);
      if (userStats) {
        switch (action) {
          case 'DISPUTE_INITIATED':
          case 'DISPUTE_RECEIVED':
            // These are already counted in the main trade stats
            break;
          case 'DISPUTE_RESOLVED':
            // Update reputation based on resolution
            if (data?.wasSuccessful) {
              userStats.reputationScore = Math.min(100, userStats.reputationScore + 2);
            } else {
              userStats.reputationScore = Math.max(0, userStats.reputationScore - 5);
            }
            break;
        }
        await userStats.save({ session });
      }
    } catch (error) {
      this.logger.warn('Failed to update user trade stats', { error, discordId, action });
    }
  }

  private generateDisputeId(): string {
    return `dispute_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
