{"version": 3, "file": "removerole.js", "sourceRoot": "", "sources": ["../../src/commands/removerole.ts"], "names": [], "mappings": ";;AAAA,2CAAmG;AACnG,yCAA6C;AAC7C,wDAA0G;AAC1G,wDAA6E;AAE7E,MAAM,CAAC,OAAO,GAAG;IACb,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC1B,OAAO,CAAC,YAAY,CAAC;SACrB,cAAc,CAAC,wCAAwC,CAAC;SACxD,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACrG,2BAA2B,CAAC,gCAAmB,CAAC,aAAa,CAAC;IACnE,OAAO,EAAE,IAAA,+BAAgB,EAAC,KAAK,EAAE,WAAwC,EAAE,EAAE;QACzE,mBAAmB;QACnB,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,gCAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,8BAAe,EAAE,CAAC;QAChC,CAAC;QAED,MAAM,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACrB,MAAM,IAAI,8BAAe,CAAC,4CAA4C,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC;YACD,iCAAiC;YACjC,MAAM,cAAc,GAAG,MAAM,IAAA,0BAAW,EAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YACvE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,cAAc,CAAC;YAExD,4BAA4B;YAC5B,IAAA,sCAAuB,EAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAEjD,4BAA4B;YAC5B,MAAM,QAAQ,GAAG,MAAM,kBAAW,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAChE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACZ,MAAM,IAAI,8BAAe,CAAC,qBAAqB,IAAI,CAAC,IAAI,mBAAmB,CAAC,CAAC;YACjF,CAAC;YAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,6BAA6B;YAC7D,MAAM,kBAAW,CAAC,gBAAgB,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAExD,8CAA8C;YAC9C,IAAI,cAAc,GAAG,sBAAsB,QAAQ,0BAA0B,CAAC;YAE9E,IAAI,UAAU,KAAK,YAAY,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;gBAClD,cAAc,IAAI,sBAAsB,SAAS,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC;YAC5E,CAAC;YAED,MAAM,WAAW,CAAC,KAAK,CAAC;gBACpB,OAAO,EAAE,cAAc;gBACvB,SAAS,EAAE,KAAK;aACnB,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACtB,IAAI,KAAK,YAAY,8BAAe,EAAE,CAAC;gBACnC,MAAM,KAAK,CAAC;YAChB,CAAC;YACD,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBACzB,MAAM,IAAI,4BAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,4BAAa,CAAC,oCAAoC,CAAC,CAAC;YAClE,CAAC;QACL,CAAC;IACL,CAAC,CAAC;CACL,CAAC"}