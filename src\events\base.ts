/**
 * Base Event Handler
 * Abstract base class for all Discord event handlers
 */

import { IEventHandler, ILogger, IApplicationContext } from '../core/interfaces';
import { createLogger } from '../core/logger';

/**
 * Abstract base event handler class
 */
export abstract class BaseEventHandler implements IEventHandler {
  public abstract readonly name: string;
  public readonly once?: boolean = false;

  protected logger: ILogger;
  protected app: IApplicationContext;

  constructor(app: IApplicationContext, eventName: string) {
    this.app = app;
    this.logger = createLogger(`event:${eventName}`);
  }

  /**
   * Execute the event handler
   */
  abstract execute(...args: any[]): Promise<void>;

  /**
   * Handle errors in event execution
   */
  protected handleError(error: any, context?: any): void {
    this.logger.error(`[${this.name}] Event handler error`, {
      error,
      context,
      eventName: this.name,
    });
  }

  /**
   * Log event execution
   */
  protected logExecution(message: string, meta?: any): void {
    this.logger.debug(`[${this.name}] ${message}`, meta);
  }

  /**
   * Check if a feature is enabled before processing
   */
  protected isFeatureEnabled(featureName: string): boolean {
    // Import here to avoid circular dependencies
    const { isFeatureActive } = require('../config');
    return isFeatureActive(featureName);
  }
}

/**
 * Event handler registry
 */
export class EventHandlerRegistry {
  private handlers = new Map<string, BaseEventHandler>();
  private logger: ILogger;

  constructor() {
    this.logger = createLogger('event-registry');
  }

  /**
   * Register an event handler
   */
  register(handler: BaseEventHandler): void {
    if (this.handlers.has(handler.name)) {
      this.logger.warn(`[EventRegistry] Handler already registered: ${handler.name}`);
      return;
    }

    this.handlers.set(handler.name, handler);
    this.logger.debug(`[EventRegistry] Registered handler: ${handler.name}`);
  }

  /**
   * Get all registered handlers
   */
  getHandlers(): BaseEventHandler[] {
    return Array.from(this.handlers.values());
  }

  /**
   * Get handler by name
   */
  getHandler(name: string): BaseEventHandler | undefined {
    return this.handlers.get(name);
  }

  /**
   * Unregister a handler
   */
  unregister(name: string): boolean {
    const removed = this.handlers.delete(name);
    if (removed) {
      this.logger.debug(`[EventRegistry] Unregistered handler: ${name}`);
    }
    return removed;
  }

  /**
   * Clear all handlers
   */
  clear(): void {
    this.handlers.clear();
    this.logger.debug('[EventRegistry] Cleared all handlers');
  }
}

/**
 * Global event registry instance
 */
export const eventRegistry = new EventHandlerRegistry();
