import { Guild, Role } from 'discord.js';
import { ValidationError } from './errorHandler';

export interface RoleResolutionResult {
    role: Role;
    resolvedBy: 'id' | 'exact_name' | 'fuzzy_name';
    confidence: number;
}

/**
 * Resolves a role by ID or name within a guild
 * Supports exact matching, case-insensitive matching, and fuzzy matching
 */
export async function resolveRole(guild: Guild, input: string): Promise<RoleResolutionResult> {
    if (!input || input.trim().length === 0) {
        throw new ValidationError('Role identifier cannot be empty');
    }

    const trimmedInput = input.trim();

    // First, try to resolve by ID (for backward compatibility)
    if (/^\d{17,20}$/.test(trimmedInput)) {
        try {
            const role = await guild.roles.fetch(trimmedInput);
            if (role) {
                return {
                    role,
                    resolvedBy: 'id',
                    confidence: 1.0
                };
            }
        } catch (error) {
            // Role ID not found, continue to name resolution
        }
    }

    // Get all roles in the guild
    const roles = await guild.roles.fetch();
    const roleArray = Array.from(roles.values()).filter(role => role.name !== '@everyone');

    // Try exact name match (case-sensitive)
    const exactMatch = roleArray.find(role => role.name === trimmedInput);
    if (exactMatch) {
        return {
            role: exactMatch,
            resolvedBy: 'exact_name',
            confidence: 1.0
        };
    }

    // Try case-insensitive exact match
    const caseInsensitiveMatch = roleArray.find(role => 
        role.name.toLowerCase() === trimmedInput.toLowerCase()
    );
    if (caseInsensitiveMatch) {
        return {
            role: caseInsensitiveMatch,
            resolvedBy: 'exact_name',
            confidence: 0.9
        };
    }

    // Try fuzzy matching (partial matches)
    const fuzzyMatches = roleArray
        .map(role => ({
            role,
            score: calculateFuzzyScore(role.name, trimmedInput)
        }))
        .filter(match => match.score > 0.5)
        .sort((a, b) => b.score - a.score);

    if (fuzzyMatches.length === 0) {
        // No matches found, provide helpful error with suggestions
        const suggestions = roleArray
            .slice(0, 5)
            .map(role => `"${role.name}"`)
            .join(', ');
        
        throw new ValidationError(
            `Role "${trimmedInput}" not found. Available roles include: ${suggestions}${roleArray.length > 5 ? '...' : ''}`
        );
    }

    if (fuzzyMatches.length === 1) {
        return {
            role: fuzzyMatches[0].role,
            resolvedBy: 'fuzzy_name',
            confidence: fuzzyMatches[0].score
        };
    }

    // Multiple matches found - check if the top match is significantly better
    const topMatch = fuzzyMatches[0];
    const secondMatch = fuzzyMatches[1];

    if (topMatch.score - secondMatch.score > 0.3) {
        // Top match is significantly better, use it
        return {
            role: topMatch.role,
            resolvedBy: 'fuzzy_name',
            confidence: topMatch.score
        };
    }

    // Ambiguous matches - provide disambiguation
    const ambiguousRoles = fuzzyMatches
        .slice(0, 5)
        .map(match => `"${match.role.name}"`)
        .join(', ');

    throw new ValidationError(
        `Multiple roles match "${trimmedInput}": ${ambiguousRoles}. Please be more specific.`
    );
}

/**
 * Calculate fuzzy matching score between two strings
 * Returns a score between 0 and 1, where 1 is a perfect match
 */
function calculateFuzzyScore(roleName: string, input: string): number {
    const roleNameLower = roleName.toLowerCase();
    const inputLower = input.toLowerCase();

    // Exact match
    if (roleNameLower === inputLower) {
        return 1.0;
    }

    // Check if input is contained in role name
    if (roleNameLower.includes(inputLower)) {
        return 0.8 * (inputLower.length / roleNameLower.length);
    }

    // Check if role name starts with input
    if (roleNameLower.startsWith(inputLower)) {
        return 0.7 * (inputLower.length / roleNameLower.length);
    }

    // Check for word matches
    const roleWords = roleNameLower.split(/\s+/);
    const inputWords = inputLower.split(/\s+/);
    
    let matchingWords = 0;
    for (const inputWord of inputWords) {
        if (roleWords.some(roleWord => roleWord.includes(inputWord) || inputWord.includes(roleWord))) {
            matchingWords++;
        }
    }

    if (matchingWords > 0) {
        return 0.6 * (matchingWords / Math.max(roleWords.length, inputWords.length));
    }

    return 0;
}

/**
 * Validates that a role exists in the guild and can be managed by the bot
 */
export function validateRolePermissions(guild: Guild, role: Role): void {
    // Check if the bot can manage this role
    const botMember = guild.members.me;
    if (!botMember) {
        throw new ValidationError('Bot member not found in guild');
    }

    if (!botMember.permissions.has('ManageRoles')) {
        throw new ValidationError('Bot does not have permission to manage roles');
    }

    // Check if the role is higher than the bot's highest role
    const botHighestRole = botMember.roles.highest;
    if (role.position >= botHighestRole.position) {
        throw new ValidationError(
            `Cannot manage role "${role.name}" - it is higher than or equal to the bot's highest role`
        );
    }

    // Check if it's a system role
    if (role.managed) {
        throw new ValidationError(
            `Cannot manage role "${role.name}" - it is managed by an integration or bot`
        );
    }

    // Check if it's the @everyone role
    if (role.name === '@everyone') {
        throw new ValidationError('Cannot manage the @everyone role');
    }
}
