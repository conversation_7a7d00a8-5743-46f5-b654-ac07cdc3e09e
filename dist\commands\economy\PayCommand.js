"use strict";
/**
 * Pay Command
 * Refactored pay command using the new command architecture
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const embedBuilder_1 = require("../../utils/embedBuilder");
const economyService_1 = require("../../services/economyService");
const errorHandler_1 = require("../../utils/errorHandler");
const constants_1 = require("../../config/constants");
/**
 * Pay command implementation
 */
class PayCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'pay',
            description: 'Transfer coins to another user',
            category: BaseCommand_1.CommandCategory.ECONOMY,
            requiredFeatures: ['ECONOMY_SYSTEM'],
            cooldown: 5, // Longer cooldown for transfers
        });
    }
    /**
     * Customize the command builder
     */
    customizeCommand(command) {
        command
            .addUserOption(option => option.setName('user')
            .setDescription('The user to send coins to')
            .setRequired(true))
            .addIntegerOption(option => option.setName('amount')
            .setDescription('Amount of coins to send')
            .setRequired(true)
            .setMinValue(constants_1.VALIDATION.MIN_TRANSACTION_AMOUNT)
            .setMaxValue(constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT));
    }
    /**
     * Execute the pay command
     */
    async executeCommand(context) {
        const { interaction } = context;
        const targetUser = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);
        // Validation
        this.validatePayment(interaction.user.id, targetUser.id, amount);
        try {
            // Get sender's current balance
            const senderUser = await (0, economyService_1.ensureUser)(interaction.user.id);
            if (senderUser.balance < amount) {
                throw new errorHandler_1.ValidationError(`Insufficient balance. You have ${(0, embedBuilder_1.formatCoins)(senderUser.balance)} but need ${(0, embedBuilder_1.formatCoins)(amount)}.`);
            }
            // Ensure recipient exists
            await (0, economyService_1.ensureUser)(targetUser.id);
            // Perform the transfer
            await (0, economyService_1.adjustBalance)(interaction.user.id, -amount, 'pay', `Payment to ${targetUser.username} (${targetUser.id})`, interaction.client, interaction.guild?.id);
            await (0, economyService_1.adjustBalance)(targetUser.id, amount, 'pay', `Payment from ${interaction.user.username} (${interaction.user.id})`, interaction.client, interaction.guild?.id);
            // Create success embed
            const embed = (0, embedBuilder_1.createSuccessEmbed)('Payment Sent Successfully!')
                .setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.TRANSFER} **Payment Completed**\n\n` +
                `${(0, embedBuilder_1.formatCoins)(amount)} has been sent to **${targetUser.displayName}**!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ACTIONS.SENDER} From`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ACTIONS.TARGET} To`,
                value: `**${targetUser.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
                value: (0, embedBuilder_1.formatCoins)(amount),
                inline: true
            });
            // Add updated balance info
            const newBalance = senderUser.balance - amount;
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            this.logger.info(`Payment completed: ${interaction.user.username} sent ${amount} PLC to ${targetUser.username}`);
            // Try to notify recipient via DM
            try {
                const recipientEmbed = (0, embedBuilder_1.createSuccessEmbed)('Payment Received!')
                    .setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.COINS} You received ${(0, embedBuilder_1.formatCoins)(amount)} from **${interaction.user.displayName}**!`);
                await targetUser.send({ embeds: [recipientEmbed] });
            }
            catch (error) {
                // DM failed, but payment was successful
                this.logger.debug(`Failed to send payment notification DM to ${targetUser.username}`, { error });
            }
        }
        catch (error) {
            this.logger.error('Error executing pay command', {
                error,
                senderId: interaction.user.id,
                recipientId: targetUser.id,
                amount
            });
            throw error;
        }
    }
    /**
     * Validate payment parameters
     */
    validatePayment(senderId, recipientId, amount) {
        if (senderId === recipientId) {
            throw new errorHandler_1.ValidationError('You cannot send coins to yourself.');
        }
        if (amount <= 0) {
            throw new errorHandler_1.ValidationError('Amount must be greater than 0.');
        }
        if (amount > constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT) {
            throw new errorHandler_1.ValidationError(`Amount cannot exceed ${(0, embedBuilder_1.formatCoins)(constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT)}.`);
        }
    }
}
exports.PayCommand = PayCommand;
//# sourceMappingURL=PayCommand.js.map