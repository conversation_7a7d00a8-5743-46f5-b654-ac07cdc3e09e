/**
 * Legacy Message Handler
 * Extracted message handling from monolithic index.ts
 */
import { Client, Message } from 'discord.js';
/**
 * Message handler for legacy compatibility
 */
export declare class LegacyMessageHandler {
    private client;
    constructor(client: Client);
    /**
     * Handle message create events
     */
    handleMessageCreate(message: Message): Promise<void>;
    /**
     * Handle bot mentions with reaction
     */
    private handleBotMention;
    /**
     * Track message activity for milestones
     */
    private trackMessageActivity;
    /**
     * Get message statistics
     */
    getStats(): any;
}
export default LegacyMessageHandler;
//# sourceMappingURL=MessageHandler.d.ts.map