"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AVAILABLE_PLACEHOLDERS = void 0;
exports.processPlaceholders = processPlaceholders;
exports.validatePlaceholders = validatePlaceholders;
exports.getPlaceholderHelp = getPlaceholderHelp;
exports.processTemplatePlaceholders = processTemplatePlaceholders;
/**
 * Available placeholders for welcome messages
 */
exports.AVAILABLE_PLACEHOLDERS = {
    // User placeholders
    '{user}': 'User mention (@username)',
    '{user.name}': 'Username without mention',
    '{user.displayName}': 'Display name or username',
    '{user.id}': 'User ID',
    '{user.tag}': 'Username#discriminator',
    '{user.avatar}': 'User avatar URL',
    '{user.createdAt}': 'Account creation date',
    '{user.accountAge}': 'Account age in days',
    // Server placeholders
    '{server}': 'Server name',
    '{server.name}': 'Server name',
    '{server.id}': 'Server ID',
    '{server.memberCount}': 'Total member count',
    '{server.humanCount}': 'Human member count (excluding bots)',
    '{server.botCount}': 'Bot count',
    '{server.icon}': 'Server icon URL',
    '{server.banner}': 'Server banner URL',
    // Role placeholders (for role-based triggers)
    '{role}': 'Role mention (@role)',
    '{role.name}': 'Role name without mention',
    '{role.id}': 'Role ID',
    '{role.color}': 'Role color (hex)',
    '{role.memberCount}': 'Number of members with this role',
    // Date/Time placeholders
    '{date}': 'Current date (MM/DD/YYYY)',
    '{time}': 'Current time (HH:MM AM/PM)',
    '{timestamp}': 'Unix timestamp',
    '{joinedAt}': 'Member join date',
    // Special placeholders
    '{newline}': 'Line break',
    '{space}': 'Space character'
};
/**
 * Processes placeholders in text content
 */
function processPlaceholders(text, context) {
    if (!text)
        return text;
    let processedText = text;
    const { user, member, guild, role } = context;
    // Calculate member counts if not provided
    const memberCount = context.memberCount || guild.memberCount;
    const botCount = context.botCount || guild.members.cache.filter(m => m.user.bot).size;
    const humanCount = context.humanCount || (memberCount - botCount);
    // User placeholders
    processedText = processedText.replace(/{user}/g, `<@${user.id}>`);
    processedText = processedText.replace(/{user\.name}/g, user.username);
    processedText = processedText.replace(/{user\.displayName}/g, member.displayName || user.username);
    processedText = processedText.replace(/{user\.id}/g, user.id);
    processedText = processedText.replace(/{user\.tag}/g, user.tag);
    processedText = processedText.replace(/{user\.avatar}/g, user.displayAvatarURL({ size: 256 }));
    processedText = processedText.replace(/{user\.createdAt}/g, user.createdAt.toLocaleDateString());
    // Calculate account age
    const accountAge = Math.floor((Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24));
    processedText = processedText.replace(/{user\.accountAge}/g, accountAge.toString());
    // Server placeholders
    processedText = processedText.replace(/{server}/g, guild.name);
    processedText = processedText.replace(/{server\.name}/g, guild.name);
    processedText = processedText.replace(/{server\.id}/g, guild.id);
    processedText = processedText.replace(/{server\.memberCount}/g, memberCount.toString());
    processedText = processedText.replace(/{server\.humanCount}/g, humanCount.toString());
    processedText = processedText.replace(/{server\.botCount}/g, botCount.toString());
    processedText = processedText.replace(/{server\.icon}/g, guild.iconURL({ size: 256 }) || '');
    processedText = processedText.replace(/{server\.banner}/g, guild.bannerURL({ size: 1024 }) || '');
    // Role placeholders (if role context is provided)
    if (role) {
        processedText = processedText.replace(/{role}/g, `<@&${role.id}>`);
        processedText = processedText.replace(/{role\.name}/g, role.name);
        processedText = processedText.replace(/{role\.id}/g, role.id);
        processedText = processedText.replace(/{role\.color}/g, role.hexColor);
        processedText = processedText.replace(/{role\.memberCount}/g, role.members.size.toString());
    }
    // Date/Time placeholders
    const now = new Date();
    processedText = processedText.replace(/{date}/g, now.toLocaleDateString());
    processedText = processedText.replace(/{time}/g, now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
    processedText = processedText.replace(/{timestamp}/g, Math.floor(now.getTime() / 1000).toString());
    if (member.joinedAt) {
        processedText = processedText.replace(/{joinedAt}/g, member.joinedAt.toLocaleDateString());
    }
    // Special placeholders
    processedText = processedText.replace(/{newline}/g, '\n');
    processedText = processedText.replace(/{space}/g, ' ');
    return processedText;
}
/**
 * Validates that a text contains only valid placeholders
 */
function validatePlaceholders(text) {
    if (!text)
        return { valid: true, invalidPlaceholders: [] };
    // Find all placeholder-like patterns
    const placeholderPattern = /{[^}]+}/g;
    const foundPlaceholders = text.match(placeholderPattern) || [];
    // Check which ones are invalid
    const validPlaceholders = Object.keys(exports.AVAILABLE_PLACEHOLDERS);
    const invalidPlaceholders = foundPlaceholders.filter(placeholder => !validPlaceholders.includes(placeholder));
    return {
        valid: invalidPlaceholders.length === 0,
        invalidPlaceholders: [...new Set(invalidPlaceholders)] // Remove duplicates
    };
}
/**
 * Gets a formatted list of available placeholders for help text
 */
function getPlaceholderHelp() {
    const categories = {
        'User Information': [
            '{user}', '{user.name}', '{user.displayName}', '{user.id}',
            '{user.tag}', '{user.avatar}', '{user.createdAt}', '{user.accountAge}'
        ],
        'Server Information': [
            '{server}', '{server.name}', '{server.id}', '{server.memberCount}',
            '{server.humanCount}', '{server.botCount}', '{server.icon}', '{server.banner}'
        ],
        'Role Information': [
            '{role}', '{role.name}', '{role.id}', '{role.color}', '{role.memberCount}'
        ],
        'Date & Time': [
            '{date}', '{time}', '{timestamp}', '{joinedAt}'
        ],
        'Special': [
            '{newline}', '{space}'
        ]
    };
    let helpText = '**Available Placeholders:**\n\n';
    for (const [category, placeholders] of Object.entries(categories)) {
        helpText += `**${category}:**\n`;
        for (const placeholder of placeholders) {
            helpText += `• \`${placeholder}\` - ${exports.AVAILABLE_PLACEHOLDERS[placeholder]}\n`;
        }
        helpText += '\n';
    }
    return helpText;
}
/**
 * Processes placeholders in an entire welcome template
 */
function processTemplatePlaceholders(template, context) {
    const processed = { ...template };
    // Process text fields
    if (processed.title)
        processed.title = processPlaceholders(processed.title, context);
    if (processed.description)
        processed.description = processPlaceholders(processed.description, context);
    if (processed.footerText)
        processed.footerText = processPlaceholders(processed.footerText, context);
    // Process fields
    if (processed.fields && Array.isArray(processed.fields)) {
        processed.fields = processed.fields.map((field) => ({
            ...field,
            name: processPlaceholders(field.name, context),
            value: processPlaceholders(field.value, context)
        }));
    }
    // Process button labels
    if (processed.buttons && Array.isArray(processed.buttons)) {
        processed.buttons = processed.buttons.map((button) => ({
            ...button,
            label: processPlaceholders(button.label, context)
        }));
    }
    return processed;
}
//# sourceMappingURL=placeholderService.js.map