import { Schema, model, Document } from 'mongoose';

export interface IIncomeGuide extends Document {
    guildId: string;
    customText: string;
    createdAt: Date;
    updatedAt: Date;
}

const incomeGuideSchema = new Schema<IIncomeGuide>({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        unique: true,
        validate: {
            validator: function(v: string): boolean {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    customText: {
        type: String,
        required: [true, 'Custom income guide text is required'],
        maxlength: [1000, 'Income guide text cannot exceed 1000 characters'],
        validate: {
            validator: function(v: string): boolean {
                return !!(v && v.trim().length > 0);
            },
            message: 'Income guide text cannot be empty'
        }
    }
}, {
    timestamps: true
});

export const IncomeGuide = model<IIncomeGuide>('IncomeGuide', incomeGuideSchema);
export default IncomeGuide;
