/**
 * Legacy Member Handler
 * Extracted member lifecycle handling from monolithic index.ts
 */

import { Client, GuildMember, PartialGuildMember, VoiceState } from 'discord.js';
import { processStarterBalance } from '../../services/starterBalanceService';
import { processJoinMessage, processRoleChangeMessage } from '../../services/automessageService';
import { UserCleanupService } from '../../services/userCleanupService';
import { checkAndProcessMilestones } from '../../services/milestoneService';

/**
 * Member handler for legacy compatibility
 */
export class LegacyMemberHandler {
  private client: Client;

  constructor(client: Client) {
    this.client = client;
  }

  /**
   * Handle guild member add events
   */
  async handleMemberAdd(member: GuildMember): Promise<void> {
    try {
      // Process join messages
      await this.processJoinMessages(member);

      // Track login activity for milestones (new member joining counts as login)
      await this.trackJoinActivity(member);

      // Note: Starter balance is processed in guildMemberUpdate when roles are added
    } catch (error) {
      console.error('[Member Add] Error in guildMemberAdd handler:', error);
    }
  }

  /**
   * Handle guild member remove events
   */
  async handleMemberRemove(member: GuildMember | PartialGuildMember): Promise<void> {
    try {
      // Handle partial members
      const fullMember = await this.ensureFullMember(member);
      
      const userId = member.user?.id;
      const guildName = member.guild?.name || 'Unknown Guild';
      const displayName = fullMember?.displayName || member.user?.username || 'Unknown User';

      if (!userId) {
        console.error('[User Cleanup] No user ID available for member who left');
        return;
      }

      // Process user data cleanup
      await this.processUserCleanup(member as GuildMember, userId, displayName, guildName);

    } catch (error) {
      console.error(`[User Cleanup] Error in guildMemberRemove handler:`, error);
    }
  }

  /**
   * Handle guild member update events
   */
  async handleMemberUpdate(
    oldMember: GuildMember | PartialGuildMember, 
    newMember: GuildMember
  ): Promise<void> {
    try {
      // Handle partial members
      const fullOldMember = await this.ensureFullMember(oldMember);
      if (!fullOldMember) return;

      // Check for role changes
      const addedRoles = newMember.roles.cache.filter(role => !fullOldMember.roles.cache.has(role.id));
      const removedRoles = fullOldMember.roles.cache.filter(role => !newMember.roles.cache.has(role.id));

      // Process added roles
      if (addedRoles.size > 0) {
        await this.processAddedRoles(newMember, addedRoles);
      }

      // Process removed roles
      if (removedRoles.size > 0) {
        await this.processRemovedRoles(newMember, removedRoles);
      }

    } catch (error) {
      console.error('[Member Update] Error in guildMemberUpdate handler:', error);
    }
  }

  /**
   * Handle voice state update events
   */
  async handleVoiceStateUpdate(oldState: VoiceState, newState: VoiceState): Promise<void> {
    try {
      // Skip bot users
      if (newState.member?.user.bot) return;

      const userId = newState.member?.user.id;
      const guildId = newState.guild.id;

      if (!userId) return;

      // Track voice activity for milestones
      // We'll track when users join voice channels
      if (!oldState.channel && newState.channel) {
        // User joined a voice channel
        await this.trackVoiceActivity(userId, guildId, newState);
      }
    } catch (error) {
      console.error('[Voice State] Error in voiceStateUpdate handler:', error);
    }
  }

  /**
   * Process join messages
   */
  private async processJoinMessages(member: GuildMember): Promise<void> {
    try {
      const joinResult = await processJoinMessage(member);
      if (joinResult.sent) {
        console.log(`[AutoMessage] Sent ${joinResult.templatesProcessed} join message(s) to ${member.displayName} in ${member.guild.name}`);
      }
      if (joinResult.errors.length > 0) {
        console.error(`[AutoMessage] Errors processing join messages for ${member.displayName}:`, joinResult.errors);
      }
    } catch (error) {
      console.error('[Member Add] Error processing join messages:', error);
    }
  }

  /**
   * Track join activity for milestones
   */
  private async trackJoinActivity(member: GuildMember): Promise<void> {
    try {
      const milestoneResults = await checkAndProcessMilestones(
        this.client,
        member.user.id,
        member.guild.id,
        'login',
        { timestamp: new Date() }
      );

      if (milestoneResults.length > 0) {
        console.log(`[Milestone] New member ${member.displayName} achieved ${milestoneResults.length} milestone(s) on join`);
      }
    } catch (error) {
      console.error('[Milestone] Error processing join milestones:', error);
    }
  }

  /**
   * Process user data cleanup
   */
  private async processUserCleanup(
    member: GuildMember,
    userId: string,
    displayName: string,
    guildName: string
  ): Promise<void> {
    try {
      // Check if we have user data before attempting cleanup
      const userData = await UserCleanupService.checkUserData(userId);
      const hasData = userData.hasUserRecord || userData.transactionCount > 0 || userData.reactionRewardCount > 0;

      if (!hasData) {
        console.log(`[User Cleanup] No data found for ${displayName} (${userId}) who left ${guildName}, skipping cleanup`);
        return;
      }

      console.log(`[User Cleanup] User ${displayName} left ${guildName} - found data: ${userData.hasUserRecord ? 'balance' : ''} ${userData.transactionCount > 0 ? `${userData.transactionCount} transactions` : ''} ${userData.reactionRewardCount > 0 ? `${userData.reactionRewardCount} reaction rewards` : ''}`.trim());

      // Perform cleanup
      const cleanupResult = await UserCleanupService.cleanupUserData(member);

      if (cleanupResult.success) {
        const removedItems = [];
        if (cleanupResult.userDataRemoved) removedItems.push('user balance');
        if (cleanupResult.transactionsRemoved > 0) removedItems.push(`${cleanupResult.transactionsRemoved} transactions`);
        if (cleanupResult.reactionRewardsRemoved > 0) removedItems.push(`${cleanupResult.reactionRewardsRemoved} reaction rewards`);

        console.log(`[User Cleanup] Successfully cleaned up data for ${displayName}: ${removedItems.join(', ')} (${cleanupResult.timeTaken}ms)`);
      } else {
        console.error(`[User Cleanup] Failed to clean up data for ${displayName}:`, cleanupResult.errors);
      }
    } catch (error) {
      console.error('[Member Handler] Error processing user cleanup:', error);
    }
  }

  /**
   * Process added roles
   */
  private async processAddedRoles(member: GuildMember, addedRoles: any): Promise<void> {
    for (const [roleId, role] of addedRoles) {
      try {
        // Process starter balance
        const granted = await processStarterBalance(member, role);
        if (granted) {
          console.log(`[Starter Balance] Granted starter balance to ${member.displayName} for role ${role.name}`);
        }

        // Process role add messages
        const roleAddResult = await processRoleChangeMessage(member, role, 'role_add');
        if (roleAddResult.sent) {
          console.log(`[AutoMessage] Sent ${roleAddResult.templatesProcessed} role add message(s) to ${member.displayName} for role ${role.name}`);
        }
        if (roleAddResult.errors.length > 0) {
          console.error(`[AutoMessage] Errors processing role add messages for ${member.displayName}:`, roleAddResult.errors);
        }
      } catch (error) {
        console.error(`[Member Update] Failed to process role add for ${member.displayName} and role ${role.name}:`, error);
      }
    }
  }

  /**
   * Process removed roles
   */
  private async processRemovedRoles(member: GuildMember, removedRoles: any): Promise<void> {
    for (const [roleId, role] of removedRoles) {
      try {
        // Process role remove messages
        const roleRemoveResult = await processRoleChangeMessage(member, role, 'role_remove');
        if (roleRemoveResult.sent) {
          console.log(`[AutoMessage] Sent ${roleRemoveResult.templatesProcessed} role remove message(s) to ${member.displayName} for role ${role.name}`);
        }
        if (roleRemoveResult.errors.length > 0) {
          console.error(`[AutoMessage] Errors processing role remove messages for ${member.displayName}:`, roleRemoveResult.errors);
        }
      } catch (error) {
        console.error(`[Member Update] Failed to process role remove for ${member.displayName} and role ${role.name}:`, error);
      }
    }
  }

  /**
   * Track voice activity for milestones
   */
  private async trackVoiceActivity(userId: string, guildId: string, newState: VoiceState): Promise<void> {
    try {
      const milestoneResults = await checkAndProcessMilestones(
        this.client,
        userId,
        guildId,
        'voice',
        {
          channelId: newState.channel!.id,
          minutes: 1, // Initial join counts as 1 minute
          timestamp: new Date()
        }
      );

      if (milestoneResults.length > 0) {
        console.log(`[Milestone] User ${newState.member?.displayName} achieved ${milestoneResults.length} milestone(s) from voice activity`);
      }
    } catch (error) {
      console.error('[Milestone] Error processing voice milestones:', error);
    }
  }

  /**
   * Ensure member is fully fetched
   */
  private async ensureFullMember(member: GuildMember | PartialGuildMember): Promise<GuildMember | null> {
    if (member.partial) {
      try {
        return await member.fetch();
      } catch (error) {
        console.error('[Member Handler] Failed to fetch member:', error);
        return null;
      }
    }
    return member as GuildMember;
  }

  /**
   * Get member handler statistics
   */
  getStats(): any {
    return {
      handlerType: 'LegacyMemberHandler',
      clientReady: this.client.isReady(),
      guildCount: this.client.guilds.cache.size,
    };
  }
}

export default LegacyMemberHandler;
