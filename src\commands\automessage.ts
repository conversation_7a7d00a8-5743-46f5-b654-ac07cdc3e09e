import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits, ChannelType, Role, GuildMember } from 'discord.js';
import { withErrorHandler, ValidationError, PermissionError, DatabaseError } from '../utils/errorHandler';
import { createSuccessEmbed, createErrorEmbed, createAdminEmbed, EMOJIS } from '../utils/embedBuilder';
import { WelcomeTemplate } from '../models/WelcomeTemplate';
import { resolveRole } from '../utils/roleResolver';
import { processTestMessage } from '../services/automessageService';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('automessage')
        .setDescription('Manage automated messages for various server events')
        .addStringOption(option => 
            option.setName('action')
                .setDescription('Action to perform')
                .setRequired(true)
                .addChoices(
                    { name: 'Create Message', value: 'create' },
                    { name: 'Remove Message', value: 'remove' },
                    { name: 'List Messages', value: 'list' },
                    { name: 'Test Message', value: 'test' }
                )
        )
        .addStringOption(option =>
            option.setName('trigger')
                .setDescription('Event that triggers the message')
                .setRequired(false)
                .addChoices(
                    { name: 'Member Join', value: 'member_join' },
                    { name: 'Role Added', value: 'role_add' },
                    { name: 'Role Removed', value: 'role_remove' }
                )
        )
        .addStringOption(option =>
            option.setName('delivery')
                .setDescription('How to deliver the message')
                .setRequired(false)
                .addChoices(
                    { name: 'Direct Message', value: 'dm' },
                    { name: 'Channel', value: 'channel' },
                    { name: 'Both DM and Channel', value: 'both' }
                )
        )
        .addStringOption(option =>
            option.setName('name')
                .setDescription('Name for the message template')
                .setRequired(false)
        )
        .addRoleOption(option =>
            option.setName('role')
                .setDescription('Specific role for role_add/role_remove triggers')
                .setRequired(false)
        )
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to send messages to (for channel/both delivery)')
                .setRequired(false)
                .addChannelTypes(ChannelType.GuildText)
        )
        .addStringOption(option =>
            option.setName('title')
                .setDescription('Title for the embed message (max 256 characters)')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('description')
                .setDescription('Main content of the message (max 4000 characters)')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('image')
                .setDescription('URL to an image to display in the embed')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('color')
                .setDescription('Hex color code for the embed (e.g., #dd7d00)')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('buttons')
                .setDescription('Buttons in format: Name1|URL1 Name2|URL2 (max 5 buttons)')
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('embed')
                .setDescription('Send as embed (true) or plain text (false). Default: true')
                .setRequired(false)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Check permissions
        if (!interaction.guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError('You need Administrator permissions to use this command.');
        }

        const action = interaction.options.getString('action', true);

        try {
            switch (action) {
                case 'create':
                    await handleCreate(interaction);
                    break;
                case 'remove':
                    await handleRemove(interaction);
                    break;
                case 'list':
                    await handleList(interaction);
                    break;
                case 'test':
                    await handleTest(interaction);
                    break;
                default:
                    throw new ValidationError('Invalid action specified.');
            }
        } catch (error) {
            if (error instanceof ValidationError || error instanceof PermissionError || error instanceof DatabaseError) {
                throw error;
            }
            throw new DatabaseError('An unexpected error occurred while processing the automessage command.');
        }
    })
};

/**
 * Handles creating a new automated message
 */
async function handleCreate(interaction: ChatInputCommandInteraction): Promise<void> {
    const trigger = interaction.options.getString('trigger');
    const delivery = interaction.options.getString('delivery');
    const name = interaction.options.getString('name');
    const role = interaction.options.getRole('role') as Role | null;
    const channel = interaction.options.getChannel('channel');
    const title = interaction.options.getString('title');
    const description = interaction.options.getString('description');
    const image = interaction.options.getString('image');
    const color = interaction.options.getString('color');
    const buttons = interaction.options.getString('buttons');
    const useEmbed = interaction.options.getBoolean('embed') ?? true;

    // Validation
    if (!trigger) {
        throw new ValidationError('Trigger type is required for creating messages.');
    }
    if (!delivery) {
        throw new ValidationError('Delivery method is required for creating messages.');
    }
    if (!name) {
        throw new ValidationError('Message name is required for creating messages.');
    }

    // Validate name length and uniqueness
    if (name.length > 100) {
        throw new ValidationError('Message name cannot exceed 100 characters.');
    }

    const existingTemplate = await WelcomeTemplate.findOne({
        guildId: interaction.guild!.id,
        name: name
    });

    if (existingTemplate) {
        throw new ValidationError(`A message template with the name "${name}" already exists.`);
    }

    // Validate role requirement for role triggers
    if ((trigger === 'role_add' || trigger === 'role_remove') && !role) {
        throw new ValidationError('A specific role must be selected for role_add and role_remove triggers.');
    }

    // Validate channel requirement for channel delivery
    if ((delivery === 'channel' || delivery === 'both') && !channel) {
        throw new ValidationError('A channel must be selected for channel or both delivery methods.');
    }

    // Validate channel type
    if (channel && channel.type !== ChannelType.GuildText) {
        throw new ValidationError('The specified channel must be a text channel.');
    }

    // Validate content based on format
    if (useEmbed) {
        // For embeds, require either title or description
        if (!title && !description) {
            throw new ValidationError('Either title or description must be provided for embed messages.');
        }
    } else {
        // For plain text, require description (title is ignored)
        if (!description) {
            throw new ValidationError('Description is required for plain text messages.');
        }
        // Warn about ignored embed-specific fields for plain text
        if (title || color || image) {
            // Note: We'll still save these but they won't be used in plain text mode
        }
    }

    if (title && title.length > 256) {
        throw new ValidationError('Title cannot exceed 256 characters.');
    }

    if (description && description.length > 4000) {
        throw new ValidationError('Description cannot exceed 4000 characters.');
    }

    // Validate color format
    if (color && !/^#[0-9A-Fa-f]{6}$/.test(color)) {
        throw new ValidationError('Color must be a valid hex code (e.g., #dd7d00).');
    }

    // Validate image URL
    if (image && !isValidUrl(image)) {
        throw new ValidationError('Image must be a valid URL.');
    }

    // Parse and validate buttons
    const parsedButtons = parseButtons(buttons);

    // Map trigger types
    const triggerTypeMap: { [key: string]: string } = {
        'member_join': 'join',
        'role_add': 'role_add',
        'role_remove': 'role_remove'
    };

    // Map delivery types
    const deliveryTypeMap: { [key: string]: string } = {
        'dm': 'dm',
        'channel': 'channel',
        'both': 'both'
    };

    // Create the template
    const template = new WelcomeTemplate({
        guildId: interaction.guild!.id,
        name: name,
        triggerType: triggerTypeMap[trigger],
        triggerRoleId: role?.id,
        deliveryType: deliveryTypeMap[delivery],
        channelId: channel?.id,
        useEmbed: useEmbed,
        title: title || undefined,
        description: description || undefined,
        color: color || undefined,
        imageUrl: image || undefined,
        buttons: parsedButtons,
        enabled: true,
        showTimestamp: true,
        delaySeconds: 0,
        priority: 1,
        fields: []
    });

    await template.save();

    // Create success response
    const embed = createSuccessEmbed('Automated Message Created')
        .setDescription(`${EMOJIS.SUCCESS.PARTY} **Message Template Ready!**\n\nYour automated message "${name}" has been created successfully.`)
        .addFields(
            {
                name: `${EMOJIS.ADMIN.SETTINGS} Configuration`,
                value:
                    `**Name:** ${name}\n` +
                    `**Trigger:** ${getTriggerDisplayName(trigger, role)}\n` +
                    `**Delivery:** ${getDeliveryDisplayName(delivery, channel)}\n` +
                    `**Format:** ${useEmbed ? 'Embed' : 'Plain Text'}\n` +
                    `**Status:** Enabled`,
                inline: false
            },
            {
                name: `${EMOJIS.ADMIN.INFO} Content Preview`,
                value: 
                    `**Title:** ${title || 'None'}\n` +
                    `**Description:** ${description ? (description.length > 100 ? description.substring(0, 100) + '...' : description) : 'None'}\n` +
                    `**Buttons:** ${parsedButtons.length} button(s)`,
                inline: false
            },
            {
                name: `${EMOJIS.MISC.LIGHTBULB} Next Steps`,
                value: 
                    `• Use \`/automessage action:test name:${name}\` to preview the message\n` +
                    `• Use \`/automessage action:list\` to see all your messages\n` +
                    `• Use \`/automessage action:remove name:${name}\` to delete this message`,
                inline: false
            }
        );

    await interaction.reply({ embeds: [embed], ephemeral: false });
}

/**
 * Handles removing an automated message
 */
async function handleRemove(interaction: ChatInputCommandInteraction): Promise<void> {
    const name = interaction.options.getString('name');

    if (!name) {
        throw new ValidationError('Message name is required for removing messages.');
    }

    const template = await WelcomeTemplate.findOne({
        guildId: interaction.guild!.id,
        name: name
    });

    if (!template) {
        throw new ValidationError(`No message template found with the name "${name}".`);
    }

    await WelcomeTemplate.findByIdAndDelete(template._id);

    const embed = createSuccessEmbed('Automated Message Removed')
        .setDescription(`${EMOJIS.SUCCESS.CHECK} **Message Deleted!**\n\nThe automated message "${name}" has been removed successfully.`)
        .addFields({
            name: `${EMOJIS.ADMIN.INFO} What was removed`,
            value:
                `**Name:** ${template.name}\n` +
                `**Trigger:** ${getTriggerDisplayName(template.triggerType === 'join' ? 'member_join' : template.triggerType, null)}\n` +
                `**Delivery:** ${template.deliveryType}`,
            inline: false
        });

    await interaction.reply({ embeds: [embed], ephemeral: false });
}

/**
 * Handles listing all automated messages
 */
async function handleList(interaction: ChatInputCommandInteraction): Promise<void> {
    const templates = await WelcomeTemplate.find({
        guildId: interaction.guild!.id
    }).sort({ name: 1 });

    if (templates.length === 0) {
        const embed = createAdminEmbed('No Automated Messages')
            .setDescription(`${EMOJIS.ADMIN.INFO} **No Messages Found**\n\nYou haven't created any automated messages yet.`)
            .addFields({
                name: `${EMOJIS.MISC.LIGHTBULB} Getting Started`,
                value:
                    `Use \`/automessage action:create\` to create your first automated message.\n\n` +
                    `**Example:**\n` +
                    `\`/automessage action:create trigger:member_join delivery:channel name:welcome title:Welcome! description:Hello {user}!\``,
                inline: false
            });

        await interaction.reply({ embeds: [embed], ephemeral: false });
        return;
    }

    const embed = createAdminEmbed('Automated Messages')
        .setDescription(`${EMOJIS.ADMIN.SETTINGS} **Server Message Templates**\n\nFound ${templates.length} automated message(s) configured for this server.`);

    // Group templates by trigger type
    const groupedTemplates: { [key: string]: any[] } = {};
    templates.forEach(template => {
        const triggerKey = template.triggerType === 'join' ? 'member_join' : template.triggerType;
        if (!groupedTemplates[triggerKey]) {
            groupedTemplates[triggerKey] = [];
        }
        groupedTemplates[triggerKey].push(template);
    });

    // Add fields for each trigger type
    Object.entries(groupedTemplates).forEach(([triggerType, templateList]) => {
        const triggerEmoji = getTriggerEmoji(triggerType);
        const triggerName = getTriggerDisplayName(triggerType, null);

        const templateInfo = templateList.map(template => {
            const status = template.enabled ? '🟢' : '🔴';
            const delivery = template.deliveryType;
            const roleInfo = template.triggerRoleId ? ` (${template.triggerRoleId})` : '';
            return `${status} **${template.name}** - ${delivery}${roleInfo}`;
        }).join('\n');

        embed.addFields({
            name: `${triggerEmoji} ${triggerName}`,
            value: templateInfo,
            inline: false
        });
    });

    embed.addFields({
        name: `${EMOJIS.MISC.LIGHTBULB} Management Commands`,
        value:
            `• \`/automessage action:test name:<name>\` - Preview a message\n` +
            `• \`/automessage action:remove name:<name>\` - Delete a message\n` +
            `• \`/placeholders\` - View available placeholders`,
        inline: false
    });

    await interaction.reply({ embeds: [embed], ephemeral: false });
}

/**
 * Handles testing an automated message
 */
async function handleTest(interaction: ChatInputCommandInteraction): Promise<void> {
    const name = interaction.options.getString('name');

    if (!name) {
        throw new ValidationError('Message name is required for testing messages.');
    }

    const template = await WelcomeTemplate.findOne({
        guildId: interaction.guild!.id,
        name: name
    });

    if (!template) {
        throw new ValidationError(`No message template found with the name "${name}".`);
    }

    try {
        const testResult = await processTestMessage(interaction.member! as GuildMember, template);

        const embed = createSuccessEmbed('Message Test Preview')
            .setDescription(`${EMOJIS.SUCCESS.PARTY} **Test Successful!**\n\nHere's how your "${name}" message will look:`)
            .addFields({
                name: `${EMOJIS.ADMIN.INFO} Template Info`,
                value:
                    `**Trigger:** ${getTriggerDisplayName(template.triggerType === 'join' ? 'member_join' : template.triggerType, null)}\n` +
                    `**Delivery:** ${template.deliveryType}\n` +
                    `**Format:** ${template.useEmbed ? 'Embed' : 'Plain Text'}\n` +
                    `**Status:** ${template.enabled ? 'Enabled' : 'Disabled'}`,
                inline: false
            });

        await interaction.reply({ embeds: [embed], ephemeral: true });

        // Send the actual test message
        if (testResult.embed || testResult.content) {
            const messageOptions: any = {
                components: testResult.components || [],
                ephemeral: true
            };

            // Add embed if present
            if (testResult.embed) {
                messageOptions.embeds = [testResult.embed];
            }

            // Add content if present (for role-based triggers with mentions or plain text)
            if (testResult.content) {
                messageOptions.content = testResult.content;
            }

            await interaction.followUp(messageOptions);
        }
    } catch (error) {
        throw new DatabaseError(`Failed to test message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Utility function to validate URLs
 */
function isValidUrl(string: string): boolean {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

/**
 * Utility function to parse button strings
 */
function parseButtons(buttonString: string | null): any[] {
    if (!buttonString) return [];

    const buttons: any[] = [];
    const buttonPairs = buttonString.split(' ');

    for (const pair of buttonPairs) {
        if (buttons.length >= 5) {
            throw new ValidationError('Maximum of 5 buttons allowed.');
        }

        const [name, url] = pair.split('|');
        if (!name || !url) {
            throw new ValidationError('Button format must be: Name|URL (e.g., Discord|https://discord.com)');
        }

        if (name.length > 80) {
            throw new ValidationError('Button names cannot exceed 80 characters.');
        }

        if (!isValidUrl(url)) {
            throw new ValidationError(`Invalid URL for button "${name}": ${url}`);
        }

        buttons.push({
            label: name,
            url: url,
            style: 'Link'
        });
    }

    return buttons;
}

/**
 * Utility function to get display name for triggers
 */
function getTriggerDisplayName(trigger: string, role: Role | null): string {
    switch (trigger) {
        case 'member_join':
            return 'Member Join';
        case 'role_add':
            return `Role Added${role ? ` (${role.name})` : ''}`;
        case 'role_remove':
            return `Role Removed${role ? ` (${role.name})` : ''}`;
        default:
            return trigger;
    }
}

/**
 * Utility function to get display name for delivery methods
 */
function getDeliveryDisplayName(delivery: string, channel: any): string {
    switch (delivery) {
        case 'dm':
            return 'Direct Message';
        case 'channel':
            return `Channel${channel ? ` (${channel.name})` : ''}`;
        case 'both':
            return `Both DM and Channel${channel ? ` (${channel.name})` : ''}`;
        default:
            return delivery;
    }
}

/**
 * Utility function to get emoji for trigger types
 */
function getTriggerEmoji(trigger: string): string {
    switch (trigger) {
        case 'member_join':
            return '👋';
        case 'role_add':
            return '🎭';
        case 'role_remove':
            return '🗑️';
        default:
            return '📝';
    }
}
