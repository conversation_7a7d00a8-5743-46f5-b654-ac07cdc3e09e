/**
 * Trade Command
 * Main command for handling all trade operations
 */

import { SlashCommandBuilder, ChatInputCommandInteraction, User as DiscordUser } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { createErrorEmbed, createSuccessEmbed, createInfoEmbed, formatCoins, EMOJIS } from '../../utils/embedBuilder';
import { ValidationError } from '../../utils/errorHandler';
import { TRADE } from '../../config/constants';
import { TradeService, TradeCreationParams } from '../../services/trade';

/**
 * Trade command implementation
 */
export class TradeCommand extends BaseCommand {
  private tradeService: TradeService;

  constructor() {
    super({
      name: 'trade',
      description: 'Secure trading system for PLC and items',
      category: CommandCategory.ECONOMY,
      requiredFeatures: ['TRADE_SYSTEM'],
      cooldown: 5,
    });
    
    // TradeService will be injected when the command is registered
    this.tradeService = null as any;
  }

  /**
   * Set the trade service (dependency injection)
   */
  setTradeService(tradeService: TradeService): void {
    this.tradeService = tradeService;
  }

  /**
   * Customize the command builder with subcommands
   */
  protected customizeCommand(command: SlashCommandBuilder): void {
    command
      .addSubcommand(subcommand =>
        subcommand
          .setName('sell')
          .setDescription('Create a trade proposal as a seller')
          .addUserOption(option =>
            option.setName('buyer')
              .setDescription('The user who will buy your item')
              .setRequired(true))
          .addIntegerOption(option =>
            option.setName('amount')
              .setDescription('Amount of PLC to receive')
              .setRequired(true)
              .setMinValue(TRADE.MIN_TRADE_AMOUNT)
              .setMaxValue(TRADE.MAX_TRADE_AMOUNT))
          .addStringOption(option =>
            option.setName('item')
              .setDescription('Description of the item you are selling')
              .setRequired(true)
              .setMaxLength(500))
          .addStringOption(option =>
            option.setName('notes')
              .setDescription('Additional notes or terms (optional)')
              .setRequired(false)
              .setMaxLength(200)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('buy')
          .setDescription('Create a trade proposal as a buyer')
          .addUserOption(option =>
            option.setName('seller')
              .setDescription('The user who will sell you an item')
              .setRequired(true))
          .addIntegerOption(option =>
            option.setName('amount')
              .setDescription('Amount of PLC to pay')
              .setRequired(true)
              .setMinValue(TRADE.MIN_TRADE_AMOUNT)
              .setMaxValue(TRADE.MAX_TRADE_AMOUNT))
          .addStringOption(option =>
            option.setName('item')
              .setDescription('Description of the item you want to buy')
              .setRequired(true)
              .setMaxLength(500))
          .addStringOption(option =>
            option.setName('notes')
              .setDescription('Additional notes or terms (optional)')
              .setRequired(false)
              .setMaxLength(200)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('status')
          .setDescription('Check the status of a trade')
          .addStringOption(option =>
            option.setName('trade_id')
              .setDescription('Trade ID to check (optional - shows your active trades if not provided)')
              .setRequired(false)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('history')
          .setDescription('View your trade history')
          .addUserOption(option =>
            option.setName('user')
              .setDescription('View another user\'s trade history (optional)')
              .setRequired(false))
          .addIntegerOption(option =>
            option.setName('limit')
              .setDescription('Number of trades to show (default: 10)')
              .setRequired(false)
              .setMinValue(1)
              .setMaxValue(50)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('cancel')
          .setDescription('Cancel a trade')
          .addStringOption(option =>
            option.setName('trade_id')
              .setDescription('Trade ID to cancel')
              .setRequired(true))
          .addStringOption(option =>
            option.setName('reason')
              .setDescription('Reason for cancellation (optional)')
              .setRequired(false)
              .setMaxLength(200)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('confirm')
          .setDescription('Confirm that you have completed your part of the trade')
          .addStringOption(option =>
            option.setName('trade_id')
              .setDescription('Trade ID to confirm')
              .setRequired(true)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('dispute')
          .setDescription('Initiate a dispute for a trade')
          .addStringOption(option =>
            option.setName('trade_id')
              .setDescription('Trade ID to dispute')
              .setRequired(true))
          .addStringOption(option =>
            option.setName('reason')
              .setDescription('Reason for the dispute')
              .setRequired(true)
              .setMaxLength(200)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('help')
          .setDescription('Get help with the trading system'));
  }

  /**
   * Execute the trade command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction } = context;
    
    if (!this.tradeService) {
      throw new Error('TradeService not initialized');
    }

    const subcommand = interaction.options.getSubcommand();

    try {
      switch (subcommand) {
        case 'sell':
          await this.handleSell(interaction);
          break;
        case 'buy':
          await this.handleBuy(interaction);
          break;
        case 'status':
          await this.handleStatus(interaction);
          break;
        case 'history':
          await this.handleHistory(interaction);
          break;
        case 'cancel':
          await this.handleCancel(interaction);
          break;
        case 'confirm':
          await this.handleConfirm(interaction);
          break;
        case 'dispute':
          await this.handleDispute(interaction);
          break;
        case 'help':
          await this.handleHelp(interaction);
          break;
        default:
          throw new ValidationError(`Unknown subcommand: ${subcommand}`);
      }
    } catch (error) {
      this.logger.error(`Error executing trade ${subcommand}`, { error, userId: interaction.user.id });
      throw error;
    }
  }

  /**
   * Handle sell subcommand
   */
  private async handleSell(interaction: ChatInputCommandInteraction): Promise<void> {
    const buyer = interaction.options.getUser('buyer', true);
    const amount = interaction.options.getInteger('amount', true);
    const itemDescription = interaction.options.getString('item', true);
    const notes = interaction.options.getString('notes');

    if (!interaction.guild) {
      throw new ValidationError('This command can only be used in a server');
    }

    const params: TradeCreationParams = {
      sellerId: interaction.user.id,
      buyerId: buyer.id,
      guildId: interaction.guild.id,
      amount,
      itemDescription,
      notes: notes || undefined,
      initiatedBy: 'SELLER'
    };

    const trade = await this.tradeService.createTrade(params, interaction.client);

    const embed = createSuccessEmbed('Trade Proposal Created!')
      .setDescription(
        `${EMOJIS.ECONOMY.TRANSFER} **Trade Proposal Sent**\n\n` +
        `**Trade ID:** \`${trade.tradeId}\`\n` +
        `**Seller:** ${interaction.user}\n` +
        `**Buyer:** ${buyer}\n` +
        `**Amount:** ${formatCoins(amount)}\n` +
        `**Item:** ${itemDescription}\n` +
        `${notes ? `**Notes:** ${notes}\n` : ''}` +
        `**Expires:** <t:${Math.floor(trade.expiresAt.getTime() / 1000)}:R>\n\n` +
        `${buyer} will receive a notification to accept this trade.`
      );

    await interaction.reply({ embeds: [embed] });
  }

  /**
   * Handle buy subcommand
   */
  private async handleBuy(interaction: ChatInputCommandInteraction): Promise<void> {
    const seller = interaction.options.getUser('seller', true);
    const amount = interaction.options.getInteger('amount', true);
    const itemDescription = interaction.options.getString('item', true);
    const notes = interaction.options.getString('notes');

    if (!interaction.guild) {
      throw new ValidationError('This command can only be used in a server');
    }

    const params: TradeCreationParams = {
      sellerId: seller.id,
      buyerId: interaction.user.id,
      guildId: interaction.guild.id,
      amount,
      itemDescription,
      notes: notes || undefined,
      initiatedBy: 'BUYER'
    };

    const trade = await this.tradeService.createTrade(params, interaction.client);

    const embed = createSuccessEmbed('Trade Proposal Created!')
      .setDescription(
        `${EMOJIS.ECONOMY.TRANSFER} **Trade Proposal Sent**\n\n` +
        `**Trade ID:** \`${trade.tradeId}\`\n` +
        `**Buyer:** ${interaction.user}\n` +
        `**Seller:** ${seller}\n` +
        `**Amount:** ${formatCoins(amount)}\n` +
        `**Item:** ${itemDescription}\n` +
        `${notes ? `**Notes:** ${notes}\n` : ''}` +
        `**Expires:** <t:${Math.floor(trade.expiresAt.getTime() / 1000)}:R>\n\n` +
        `${seller} will receive a notification to accept this trade.`
      );

    await interaction.reply({ embeds: [embed] });
  }

  /**
   * Handle status subcommand
   */
  private async handleStatus(interaction: ChatInputCommandInteraction): Promise<void> {
    const tradeId = interaction.options.getString('trade_id');

    if (tradeId) {
      // Show specific trade status
      const trade = await this.tradeService.getTrade(tradeId);
      if (!trade) {
        throw new ValidationError('Trade not found');
      }

      if (!trade.involvesUser(interaction.user.id)) {
        throw new ValidationError('You are not a party to this trade');
      }

      // Implementation for specific trade status will be added with UI components
      const embed = createInfoEmbed('Trade Status')
        .setDescription(`Trade status for \`${tradeId}\` will be implemented with UI components`);
      
      await interaction.reply({ embeds: [embed] });
    } else {
      // Show user's active trades
      const activeTrades = await this.tradeService.getUserActiveTrades(
        interaction.user.id, 
        interaction.guild?.id
      );

      if (activeTrades.length === 0) {
        const embed = createInfoEmbed('No Active Trades')
          .setDescription('You have no active trades at the moment.');
        
        await interaction.reply({ embeds: [embed] });
        return;
      }

      // Implementation for active trades list will be added with UI components
      const embed = createInfoEmbed('Your Active Trades')
        .setDescription(`You have ${activeTrades.length} active trade(s). Detailed view will be implemented with UI components.`);
      
      await interaction.reply({ embeds: [embed] });
    }
  }

  /**
   * Handle history subcommand
   */
  private async handleHistory(interaction: ChatInputCommandInteraction): Promise<void> {
    // Implementation will be added with UI components
    const embed = createInfoEmbed('Trade History')
      .setDescription('Trade history feature will be implemented with UI components');
    
    await interaction.reply({ embeds: [embed] });
  }

  /**
   * Handle cancel subcommand
   */
  private async handleCancel(interaction: ChatInputCommandInteraction): Promise<void> {
    const tradeId = interaction.options.getString('trade_id', true);
    const reason = interaction.options.getString('reason');

    const trade = await this.tradeService.cancelTrade(
      tradeId, 
      interaction.user.id, 
      reason || undefined, 
      interaction.client
    );

    const embed = createSuccessEmbed('Trade Cancelled')
      .setDescription(
        `${EMOJIS.SUCCESS.CHECK} **Trade Cancelled Successfully**\n\n` +
        `**Trade ID:** \`${trade.tradeId}\`\n` +
        `${reason ? `**Reason:** ${reason}\n` : ''}` +
        `Any escrowed funds have been refunded.`
      );

    await interaction.reply({ embeds: [embed] });
  }

  /**
   * Handle confirm subcommand
   */
  private async handleConfirm(interaction: ChatInputCommandInteraction): Promise<void> {
    const tradeId = interaction.options.getString('trade_id', true);

    const result = await this.tradeService.confirmTrade(
      tradeId, 
      interaction.user.id, 
      interaction.client
    );

    if (result.completed) {
      const embed = createSuccessEmbed('Trade Completed!')
        .setDescription(
          `${EMOJIS.SUCCESS.PARTY} **Trade Successfully Completed**\n\n` +
          `**Trade ID:** \`${result.trade.tradeId}\`\n` +
          `Both parties have confirmed completion. Funds have been released to the seller.`
        );
      
      await interaction.reply({ embeds: [embed] });
    } else {
      const embed = createSuccessEmbed('Confirmation Recorded')
        .setDescription(
          `${EMOJIS.SUCCESS.CHECK} **Your Confirmation Recorded**\n\n` +
          `**Trade ID:** \`${result.trade.tradeId}\`\n` +
          `Waiting for the other party to confirm. The trade expiration has been extended.`
        );
      
      await interaction.reply({ embeds: [embed] });
    }
  }

  /**
   * Handle dispute subcommand
   */
  private async handleDispute(interaction: ChatInputCommandInteraction): Promise<void> {
    // Implementation will be added with dispute resolution system
    const embed = createInfoEmbed('Dispute System')
      .setDescription('Dispute system will be implemented in the next phase');
    
    await interaction.reply({ embeds: [embed] });
  }

  /**
   * Handle help subcommand
   */
  private async handleHelp(interaction: ChatInputCommandInteraction): Promise<void> {
    const embed = createInfoEmbed('Trade System Help')
      .setDescription(
        `${EMOJIS.MISC.BOOK} **Secure Trading System**\n\n` +
        `**Commands:**\n` +
        `• \`/trade sell @user amount item\` - Create a sell proposal\n` +
        `• \`/trade buy @user amount item\` - Create a buy proposal\n` +
        `• \`/trade status [trade_id]\` - Check trade status\n` +
        `• \`/trade confirm trade_id\` - Confirm trade completion\n` +
        `• \`/trade cancel trade_id\` - Cancel a trade\n` +
        `• \`/trade dispute trade_id reason\` - Initiate dispute\n\n` +
        `**How it works:**\n` +
        `1. Create a trade proposal\n` +
        `2. Other party accepts (funds locked in escrow)\n` +
        `3. Complete the trade outside Discord\n` +
        `4. Both parties confirm completion\n` +
        `5. Funds automatically released\n\n` +
        `**Limits:**\n` +
        `• Min trade: ${formatCoins(TRADE.MIN_TRADE_AMOUNT)}\n` +
        `• Max trade: ${formatCoins(TRADE.MAX_TRADE_AMOUNT)}\n` +
        `• Max active trades: ${TRADE.MAX_ACTIVE_TRADES_PER_USER}\n` +
        `• Daily proposals: ${TRADE.MAX_TRADE_PROPOSALS_PER_DAY}`
      );

    await interaction.reply({ embeds: [embed] });
  }
}
