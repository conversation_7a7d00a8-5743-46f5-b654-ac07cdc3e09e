import { Document } from 'mongoose';
export interface IMonetizedChannel extends Document {
    channelId: string;
    enabled: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare const MonetizedChannel: import("mongoose").Model<IMonetizedChannel, {}, {}, {}, Document<unknown, {}, IMonetizedChannel, {}> & IMonetizedChannel & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=MonetizedChannel.d.ts.map