# 🔧 Milestone System Troubleshooting Guide

## 🚨 Common Issues and Solutions

### **Issue 1: Milestones Not Being Awarded**

#### **Symptoms:**
- Users report not receiving milestone rewards despite meeting requirements
- No milestone achievements showing in `/milestones achievements`
- Activity tracking appears to work but no rewards are given

#### **Diagnosis Steps:**
1. **Check System Status**
   ```
   /milestonestatus system
   ```
   Look for: System health, milestone configurations, integration status

2. **Verify Milestone Configuration**
   ```
   /milestone status
   ```
   Look for: Enabled milestones, correct thresholds, reward amounts

3. **Check User Activity**
   ```
   /milestones progress
   ```
   Look for: Current streaks, daily/weekly activity, threshold progress

4. **Review Security Status**
   ```
   /milestonestatus user user:@username
   ```
   Look for: Blacklist status, suspicious activity, rate limits

#### **Common Causes and Solutions:**

**Cause: Milestone Type Disabled**
```
Solution: /milestone enable type:login_streak
```

**Cause: User Hit Rate Limits**
```
Check: /milestonestatus user user:@username
Solution: Wait for cooldown period or adjust limits
```

**Cause: Threshold Too High**
```
Check: /milestone status
Solution: /milestone configure type:login_streak threshold:3
```

**Cause: User Blacklisted**
```
Check: /milestonestatus user user:@username
Solution: Review security report and remove blacklist if appropriate
```

---

### **Issue 2: Too Many Security Alerts**

#### **Symptoms:**
- High number of security alerts in `/milestonestatus security`
- Users getting blacklisted frequently
- Legitimate users unable to earn milestones

#### **Diagnosis Steps:**
1. **Review Security Alerts**
   ```
   /milestonestatus security hours:24
   ```

2. **Check Audit Logs**
   ```
   /milestonestatus audit category:security limit:50
   ```

3. **Analyze User Patterns**
   ```
   /milestonestatus user user:@suspicious_user
   ```

#### **Common Causes and Solutions:**

**Cause: Anti-Spam Too Sensitive**
```
Issue: Legitimate short messages being flagged
Solution: Adjust message quality thresholds in code or disable for certain channels
```

**Cause: Rapid Activity Detection Too Strict**
```
Issue: Active users hitting rapid activity limits
Solution: Increase RAPID_ACTIVITY_THRESHOLD in milestoneService.ts
```

**Cause: Legitimate Bot Usage**
```
Issue: Users using legitimate automation tools
Solution: Whitelist specific users or adjust detection parameters
```

---

### **Issue 3: Performance Problems**

#### **Symptoms:**
- Bot responding slowly to commands
- High memory usage
- Database connection issues

#### **Diagnosis Steps:**
1. **Check Integration Status**
   ```
   /milestonestatus integration
   ```

2. **Monitor System Health**
   ```
   /milestonestatus system
   ```

3. **Review Error Logs**
   Check bot console for database errors, memory warnings, or timeout issues

#### **Solutions:**

**Database Optimization:**
```javascript
// Run cleanup manually if needed
await MilestoneAuditService.cleanupOldLogs(30); // Keep only 30 days
```

**Memory Management:**
- Restart bot if memory usage is high
- Check for memory leaks in custom code
- Ensure proper database connection pooling

**Rate Limiting Adjustment:**
```
/milestone configure type:login_streak cooldown_hours:48
```
Increase cooldowns to reduce database load

---

### **Issue 4: Integration Conflicts**

#### **Symptoms:**
- Milestone rewards not triggering role assignments
- Transaction history showing incorrect amounts
- Existing systems (reaction rewards, taxes) not working properly

#### **Diagnosis Steps:**
1. **Check Integration Health**
   ```
   /milestonestatus integration
   ```

2. **Verify Transaction Types**
   Check that 'milestone' is included in transaction enum

3. **Test Role Assignment**
   Manually give PLC and verify role assignment works

#### **Solutions:**

**Transaction Type Missing:**
```typescript
// Ensure dist/models/Transaction.js includes 'milestone' in enum
type: { type: String, enum: [..., 'milestone'], required: true }
```

**Role Assignment Not Triggering:**
```typescript
// Verify adjustBalance is called with correct parameters
await adjustBalance(userId, amount, 'milestone', details, client, guildId);
```

**Economy Service Compatibility:**
```typescript
// Ensure economyService.ts includes milestone type
type: 'give' | 'fine' | 'pay' | 'role_achievement' | 'reaction' | 'tax' | 'starter_balance' | 'content_submission' | 'content_reward' | 'milestone'
```

---

## 🛠️ Advanced Troubleshooting

### **Database Issues**

#### **Connection Problems:**
```bash
# Check MongoDB connection
mongo --eval "db.adminCommand('ismaster')"

# Verify collections exist
mongo your_database --eval "show collections"
```

#### **Index Optimization:**
```javascript
// Create missing indexes manually
db.milestoneachievements.createIndex({ discordId: 1, guildId: 1, timestamp: -1 });
db.useractivity.createIndex({ discordId: 1, guildId: 1 });
db.milestoneconfigurations.createIndex({ guildId: 1, enabled: 1 });
```

#### **Data Corruption:**
```javascript
// Check for invalid data
db.useractivity.find({ loginStreak: { $lt: 0 } });
db.milestoneachievements.find({ rewardAmount: { $lt: 0 } });

// Clean up if needed
db.useractivity.updateMany({ loginStreak: { $lt: 0 } }, { $set: { loginStreak: 0 } });
```

### **Performance Tuning**

#### **Query Optimization:**
```javascript
// Add explain() to slow queries to analyze performance
db.milestoneachievements.find({ discordId: "123", guildId: "456" }).explain("executionStats");
```

#### **Memory Usage:**
```javascript
// Monitor memory usage
process.memoryUsage();

// Implement garbage collection if needed
if (global.gc) {
    global.gc();
}
```

#### **Rate Limiting Adjustment:**
```typescript
// Adjust constants in milestoneService.ts for better performance
const MAX_WEEKLY_MILESTONE_REWARDS = 25; // Reduce from 50
const MAX_DAILY_MILESTONE_REWARDS = 5;   // Reduce from 10
const RAPID_ACTIVITY_THRESHOLD = 5;      // Reduce from 10
```

### **Security Tuning**

#### **Blacklist Management:**
```javascript
// Remove expired blacklists manually
await MilestoneRateLimit.updateMany(
    { isBlacklisted: true, blacklistUntil: { $lt: new Date() } },
    { $unset: { blacklistUntil: 1, blacklistReason: 1 }, $set: { isBlacklisted: false } }
);
```

#### **Suspicious Activity Reset:**
```javascript
// Reset suspicious activity counts for legitimate users
await MilestoneRateLimit.updateMany(
    { suspiciousActivityCount: { $gt: 0 }, lastSuspiciousActivity: { $lt: new Date(Date.now() - 7*24*60*60*1000) } },
    { $set: { suspiciousActivityCount: 0 } }
);
```

#### **Audit Log Cleanup:**
```javascript
// Manual cleanup of old audit logs
await MilestoneAuditLog.deleteMany({
    timestamp: { $lt: new Date(Date.now() - 30*24*60*60*1000) },
    severity: { $in: ['low', 'medium'] }
});
```

---

## 📊 Monitoring and Maintenance

### **Daily Checks**
1. **Security Status**: `/milestonestatus security hours:24`
2. **System Health**: `/milestonestatus system`
3. **Recent Activity**: `/milestonestatus audit category:achievement limit:10`

### **Weekly Reviews**
1. **Integration Status**: `/milestonestatus integration`
2. **User Security Reports**: Check high-activity users
3. **Performance Metrics**: Monitor response times and error rates
4. **Configuration Adjustments**: Based on community feedback

### **Monthly Maintenance**
1. **Database Cleanup**: Run audit log cleanup
2. **Performance Analysis**: Review slow queries and optimize
3. **Security Review**: Analyze patterns and adjust thresholds
4. **Community Feedback**: Gather input on milestone difficulty and rewards

---

## 🔍 Diagnostic Commands Reference

### **System Diagnostics**
```
/milestonestatus system          # Overall system health
/milestonestatus integration     # Integration with existing systems
/milestone status                # Milestone configurations
```

### **Security Diagnostics**
```
/milestonestatus security hours:24           # Recent security alerts
/milestonestatus user user:@username         # User security report
/milestonestatus audit category:security     # Security audit logs
```

### **User Diagnostics**
```
/milestones progress             # User activity and streaks
/milestones achievements         # Recent milestone rewards
/milestones stats               # Overall user statistics
/milestones available           # Available milestone types
```

### **Admin Diagnostics**
```
/milestone leaderboard          # Top milestone achievers
/milestonestatus audit          # Recent system activity
```

---

## 🚀 Performance Optimization Tips

### **Database Optimization**
- Ensure proper indexing on frequently queried fields
- Regular cleanup of old audit logs and expired rate limits
- Monitor query performance with explain plans

### **Memory Management**
- Restart bot periodically if memory usage grows
- Implement proper error handling to prevent memory leaks
- Use efficient data structures for caching

### **Rate Limiting Tuning**
- Adjust thresholds based on server activity patterns
- Implement progressive rate limiting for repeat offenders
- Balance security with user experience

### **Integration Efficiency**
- Minimize database queries in milestone checking
- Use batch operations where possible
- Implement proper caching for configuration data

---

## 📞 Getting Help

### **Log Analysis**
When reporting issues, include:
- Bot console logs (last 50 lines)
- Specific error messages
- User IDs and timestamps of issues
- Server configuration details

### **Common Log Patterns**
```
[Milestone Service] Error checking milestones: DatabaseError
[Milestone Audit] Failed to log achievement: MongoError
[Integration] Milestone processing error: ValidationError
```

### **Debug Mode**
Enable verbose logging by setting environment variable:
```bash
DEBUG=milestone:*
```

This comprehensive troubleshooting guide should help administrators quickly identify and resolve common issues with the milestone system while maintaining optimal performance and security.

---

## 🧪 Testing Scenarios

### **Basic Functionality Tests**

#### **Test 1: Milestone Setup**
```
1. Run: /milestone setup
2. Verify: Default configurations created
3. Check: /milestone status shows enabled milestones
4. Expected: 5+ milestone types configured and enabled
```

#### **Test 2: User Activity Tracking**
```
1. Have test user send messages in different channels
2. Check: /milestones progress shows updated activity
3. Expected: Daily message count increases, unique channels tracked
```

#### **Test 3: Milestone Achievement**
```
1. Configure low threshold: /milestone configure type:login_streak threshold:1 reward:5
2. Have user be active for consecutive days
3. Check: /milestones achievements shows new milestone
4. Verify: User balance increased by reward amount
```

### **Security Tests**

#### **Test 4: Spam Detection**
```
1. Have user send multiple identical short messages
2. Expected: Messages flagged as spam, no milestone progress
3. Check: /milestonestatus user shows suspicious activity
```

#### **Test 5: Rate Limiting**
```
1. Configure low daily limit: /milestone configure type:login_streak daily_limit:1
2. Trigger milestone multiple times in one day
3. Expected: Only first achievement awards PLC
4. Check: /milestonestatus user shows rate limit hit
```

#### **Test 6: Blacklist System**
```
1. Generate multiple suspicious activities for test user
2. Expected: User automatically blacklisted after threshold
3. Check: /milestonestatus user shows blacklist status
4. Verify: No milestones awarded while blacklisted
```

### **Integration Tests**

#### **Test 7: Role Assignment Integration**
```
1. Set up role achievement at low PLC amount
2. Award milestone that brings user to role threshold
3. Expected: Role automatically assigned after milestone
4. Verify: Transaction history shows both milestone and role achievement
```

#### **Test 8: Economy System Compatibility**
```
1. Award milestone to user
2. Check: /balance shows increased PLC
3. Check: /history shows milestone transaction
4. Expected: Milestone rewards integrate seamlessly with economy
```

### **Performance Tests**

#### **Test 9: High Activity Load**
```
1. Simulate multiple users achieving milestones simultaneously
2. Monitor: Bot response times and memory usage
3. Expected: System handles load without significant degradation
```

#### **Test 10: Database Stress Test**
```
1. Generate large number of audit logs and achievements
2. Run: Database cleanup operations
3. Expected: System maintains performance with large datasets
```
