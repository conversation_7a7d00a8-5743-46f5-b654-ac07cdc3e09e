const mongoose = require('mongoose');
require('dotenv').config();

const uri = process.env.MONGODB_URI;

console.log('Testing Mongoose connection...');
console.log('URI:', uri);

async function testConnection() {
  try {
    console.log('Attempting to connect...');
    
    // Test with corrected configuration (removed deprecated options)
    await mongoose.connect(uri, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      // bufferMaxEntries: 0,  // REMOVED - not supported in newer MongoDB driver
      // bufferCommands: false, // REMOVED - not supported in newer MongoDB driver
    });
    
    console.log('✅ Successfully connected to MongoDB with Mongoose!');
    
    // Test a simple operation
    const testSchema = new mongoose.Schema({ test: String });
    const TestModel = mongoose.model('Test', testSchema);
    
    console.log('Testing database operation...');
    const doc = new TestModel({ test: 'connection test' });
    await doc.save();
    console.log('✅ Successfully saved test document!');
    
    await TestModel.deleteOne({ _id: doc._id });
    console.log('✅ Successfully deleted test document!');
    
  } catch (error) {
    console.error('❌ Connection failed:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      code: error.code,
      reason: error.reason
    });
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

testConnection();
