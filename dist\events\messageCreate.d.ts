/**
 * Message Create Event Handler
 * Handles Discord message creation events for mention reactions and milestone tracking
 */
import { Message } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';
/**
 * Message create event handler
 */
export declare class MessageCreateEventHandler extends BaseEventHandler {
    readonly name = "messageCreate";
    constructor(app: IApplicationContext);
    /**
     * Execute message create event
     */
    execute(message: Message): Promise<void>;
    /**
     * Handle bot mentions with reaction
     */
    private handleBotMention;
    /**
     * Track message activity for milestones
     */
    private trackMessageActivity;
}
//# sourceMappingURL=messageCreate.d.ts.map