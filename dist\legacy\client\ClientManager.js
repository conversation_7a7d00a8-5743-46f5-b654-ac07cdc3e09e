"use strict";
/**
 * Legacy Client Manager
 * Extracted Discord client setup from monolithic index.ts
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyClientManager = void 0;
const discord_js_1 = require("discord.js");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const memoryManager_1 = __importDefault(require("../../services/memoryManager"));
/**
 * Discord client manager for legacy compatibility
 */
class LegacyClientManager {
    constructor() {
        this.client = this.createClient();
        this.memoryManager = memoryManager_1.default.getInstance();
    }
    /**
     * Create Discord client with proper intents
     */
    createClient() {
        const client = new discord_js_1.Client({
            intents: [
                discord_js_1.GatewayIntentBits.Guilds,
                discord_js_1.GatewayIntentBits.GuildMessages,
                discord_js_1.GatewayIntentBits.MessageContent,
                discord_js_1.GatewayIntentBits.GuildMembers,
                discord_js_1.GatewayIntentBits.GuildMessageReactions
            ]
        });
        // Add commands collection for legacy compatibility
        client.commands = new discord_js_1.Collection();
        return client;
    }
    /**
     * Load commands from the commands directory
     */
    loadCommands() {
        const commandsPath = path_1.default.join(__dirname, '../../commands');
        if (!fs_1.default.existsSync(commandsPath)) {
            console.warn(`Commands directory not found: ${commandsPath}`);
            return;
        }
        const commandFiles = fs_1.default.readdirSync(commandsPath).filter(file => file.endsWith('.js') || file.endsWith('.ts'));
        let loadedCount = 0;
        for (const file of commandFiles) {
            try {
                const filePath = path_1.default.join(commandsPath, file);
                const command = require(filePath);
                if (command.data && command.execute) {
                    this.client.commands.set(command.data.name, command);
                    loadedCount++;
                    console.log(`[Legacy Client] Loaded command: ${command.data.name}`);
                }
                else {
                    console.warn(`[Legacy Client] Invalid command file: ${file}`);
                }
            }
            catch (error) {
                console.error(`[Legacy Client] Failed to load command file: ${file}`, error);
            }
        }
        console.log(`[Legacy Client] Loaded ${loadedCount} commands`);
    }
    /**
     * Get the Discord client instance
     */
    getClient() {
        return this.client;
    }
    /**
     * Get memory manager instance
     */
    getMemoryManager() {
        return this.memoryManager;
    }
    /**
     * Login to Discord
     */
    async login() {
        try {
            const token = process.env.BOT_TOKEN;
            if (!token) {
                throw new Error('BOT_TOKEN environment variable is not set');
            }
            await this.client.login(token);
            console.log(`[Legacy Client] Logged in as ${this.client.user?.tag}`);
        }
        catch (error) {
            console.error('[Legacy Client] Failed to login:', error);
            throw error;
        }
    }
    /**
     * Destroy the client connection
     */
    destroy() {
        this.client.destroy();
        console.log('[Legacy Client] Client destroyed');
    }
    /**
     * Check if client is ready
     */
    isReady() {
        return this.client.isReady();
    }
    /**
     * Get client uptime in milliseconds
     */
    getUptime() {
        return this.client.uptime;
    }
    /**
     * Get guild count
     */
    getGuildCount() {
        return this.client.guilds.cache.size;
    }
    /**
     * Get user count across all guilds
     */
    getUserCount() {
        return this.client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);
    }
}
exports.LegacyClientManager = LegacyClientManager;
exports.default = LegacyClientManager;
//# sourceMappingURL=ClientManager.js.map