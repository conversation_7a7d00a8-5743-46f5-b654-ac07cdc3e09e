/**
 * Legacy Application Manager
 * Orchestrates all legacy components for backward compatibility
 */

import { Client } from 'discord.js';
import { LegacyDatabaseInitializer } from './database/DatabaseInitializer';
import { LegacyClientManager } from './client/ClientManager';
import { LegacyCronManager } from './cron/CronManager';
import { LegacyInteractionHandler } from './events/InteractionHandler';
import { LegacyMessageHandler } from './events/MessageHandler';
import { LegacyReactionHandler } from './events/ReactionHandler';
import { LegacyMemberHandler } from './events/MemberHandler';

/**
 * Legacy application manager that orchestrates all components
 */
export class LegacyApplication {
  private clientManager: LegacyClientManager;
  private cronManager: LegacyCronManager;
  private interactionHandler: LegacyInteractionHandler;
  private messageHandler: LegacyMessageHandler;
  private reactionHandler: LegacyReactionHandler;
  private memberHandler: LegacyMemberHandler;
  private client: Client;
  private isInitialized = false;

  constructor() {
    this.clientManager = new LegacyClientManager();
    this.client = this.clientManager.getClient();
    
    // Initialize handlers
    this.cronManager = new LegacyCronManager(this.client);
    this.interactionHandler = new LegacyInteractionHandler(this.client);
    this.messageHandler = new LegacyMessageHandler(this.client);
    this.reactionHandler = new LegacyReactionHandler(this.client);
    this.memberHandler = new LegacyMemberHandler(this.client);
  }

  /**
   * Initialize the legacy application
   */
  async initialize(): Promise<void> {
    try {
      console.log('[Legacy App] Initializing Economy Bot (Legacy Mode)...');

      // Initialize database
      await LegacyDatabaseInitializer.initialize();

      // Load commands
      this.clientManager.loadCommands();

      // Setup event handlers
      this.setupEventHandlers();

      // Login to Discord
      await this.clientManager.login();

      this.isInitialized = true;
      console.log('[Legacy App] Economy Bot initialized successfully in legacy mode');
    } catch (error) {
      console.error('[Legacy App] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Setup Discord event handlers
   */
  private setupEventHandlers(): void {
    // Ready event
    this.client.once('ready', () => {
      console.log(`[Legacy App] Logged in as ${this.client.user?.tag}`);
      
      // Initialize cron jobs after client is ready
      this.cronManager.initializeJobs();
    });

    // Interaction events
    this.client.on('interactionCreate', async (interaction) => {
      await this.interactionHandler.handleInteraction(interaction);
    });

    // Message events
    this.client.on('messageCreate', async (message) => {
      await this.messageHandler.handleMessageCreate(message);
    });

    // Reaction events
    this.client.on('messageReactionAdd', async (reaction, user) => {
      await this.reactionHandler.handleReactionAdd(reaction, user);
    });

    // Member lifecycle events
    this.client.on('guildMemberAdd', async (member) => {
      await this.memberHandler.handleMemberAdd(member);
    });

    this.client.on('guildMemberRemove', async (member) => {
      await this.memberHandler.handleMemberRemove(member);
    });

    this.client.on('guildMemberUpdate', async (oldMember, newMember) => {
      await this.memberHandler.handleMemberUpdate(oldMember, newMember);
    });

    // Voice state events
    this.client.on('voiceStateUpdate', async (oldState, newState) => {
      await this.memberHandler.handleVoiceStateUpdate(oldState, newState);
    });

    console.log('[Legacy App] Event handlers registered');
  }

  /**
   * Shutdown the legacy application
   */
  async shutdown(): Promise<void> {
    try {
      console.log('[Legacy App] Shutting down...');

      // Stop cron jobs
      this.cronManager.destroyAllJobs();

      // Destroy client
      this.clientManager.destroy();

      // Close database connection
      await LegacyDatabaseInitializer.close();

      this.isInitialized = false;
      console.log('[Legacy App] Shutdown complete');
    } catch (error) {
      console.error('[Legacy App] Error during shutdown:', error);
    }
  }

  /**
   * Get application status
   */
  getStatus(): any {
    return {
      initialized: this.isInitialized,
      clientReady: this.clientManager.isReady(),
      uptime: this.clientManager.getUptime(),
      guildCount: this.clientManager.getGuildCount(),
      userCount: this.clientManager.getUserCount(),
      databaseStatus: LegacyDatabaseInitializer.getConnectionStatus(),
      cronJobs: this.cronManager.getAllJobStatuses(),
    };
  }

  /**
   * Get the Discord client
   */
  getClient(): Client {
    return this.client;
  }

  /**
   * Get component managers
   */
  getManagers() {
    return {
      client: this.clientManager,
      cron: this.cronManager,
      interaction: this.interactionHandler,
      message: this.messageHandler,
      reaction: this.reactionHandler,
      member: this.memberHandler,
    };
  }

  /**
   * Check if application is ready
   */
  isReady(): boolean {
    return this.isInitialized && this.clientManager.isReady();
  }
}

export default LegacyApplication;
