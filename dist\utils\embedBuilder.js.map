{"version": 3, "file": "embedBuilder.js", "sourceRoot": "", "sources": ["../../src/utils/embedBuilder.ts"], "names": [], "mappings": ";;;AAgGA,0CASC;AAKD,gDAEC;AAKD,4CAEC;AAKD,gDAEC;AAKD,4CAEC;AAKD,kCAOC;AAKD,4DAkBC;AAKD,0DA2BC;AAKD,8DAaC;AAKD,kCAEC;AAKD,gDAEC;AAxOD,2CAA+G;AAE/G,eAAe;AACF,QAAA,MAAM,GAAG;IAClB,OAAO,EAAE,SAA4B;IACrC,OAAO,EAAE,SAA4B;IACrC,KAAK,EAAE,SAA4B;IACnC,OAAO,EAAE,SAA4B;IACrC,IAAI,EAAE,SAA4B;IAClC,IAAI,EAAE,SAA4B;CACrC,CAAC;AAEF,oBAAoB;AACP,QAAA,MAAM,GAAG;IAClB,OAAO,EAAE;QACL,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,IAAI;QACZ,QAAQ,EAAE,GAAG;QACb,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,IAAI;QACX,QAAQ,EAAE,IAAI,CAAC,qCAAqC;KACvD;IACD,OAAO,EAAE;QACL,KAAK,EAAE,GAAG;QACV,KAAK,EAAE,IAAI;QACX,IAAI,EAAE,GAAG;QACT,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,IAAI,CAAC,0BAA0B;KAC3C;IACD,KAAK,EAAE;QACH,KAAK,EAAE,IAAI;QACX,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;KACf;IACD,OAAO,EAAE;QACL,SAAS,EAAE,GAAG;QACd,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,IAAI;QACX,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,IAAI,CAAC,yBAAyB;KACzC;IACD,KAAK,EAAE;QACH,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,IAAI;QACb,KAAK,EAAE,KAAK;QACZ,IAAI,EAAE,IAAI;QACV,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,IAAI;QACX,QAAQ,EAAE,IAAI;KACjB;IACD,SAAS,EAAE;QACP,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE,GAAG;QACT,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,KAAK,EAAE,IAAI;QACX,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,IAAI;QACjB,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,IAAI;KACpB;IACD,IAAI,EAAE;QACF,KAAK,EAAE,IAAI;QACX,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE,IAAI;QAChB,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,IAAI;QACf,EAAE,EAAE,IAAI;QACR,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,IAAI;QACX,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,GAAG;QACb,IAAI,EAAE,IAAI;QACV,GAAG,EAAE,KAAK;QACV,UAAU,EAAE,IAAI;KACnB;CACJ,CAAC;AAEF;;GAEG;AACH,SAAgB,eAAe,CAAC,KAAc,EAAE,WAAoB,EAAE,QAAyB,cAAM,CAAC,OAAO;IACzG,MAAM,KAAK,GAAG,IAAI,yBAAY,EAAE;SAC3B,QAAQ,CAAC,KAAK,CAAC;SACf,YAAY,EAAE,CAAC;IAEpB,IAAI,KAAK;QAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,WAAW;QAAE,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAEnD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,KAAa,EAAE,WAAoB;IAClE,OAAO,eAAe,CAAC,GAAG,cAAM,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,EAAE,EAAE,WAAW,EAAE,cAAM,CAAC,OAAO,CAAC,CAAC;AAC5F,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,KAAa,EAAE,WAAoB;IAChE,OAAO,eAAe,CAAC,GAAG,cAAM,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,WAAW,EAAE,cAAM,CAAC,KAAK,CAAC,CAAC;AAC1F,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,KAAa,EAAE,WAAoB;IAClE,OAAO,eAAe,CAAC,GAAG,cAAM,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,EAAE,EAAE,WAAW,EAAE,cAAM,CAAC,OAAO,CAAC,CAAC;AAC5F,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,KAAa,EAAE,WAAoB;IAChE,OAAO,eAAe,CAAC,GAAG,cAAM,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,EAAE,EAAE,WAAW,EAAE,cAAM,CAAC,OAAO,CAAC,CAAC;AAC3F,CAAC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,KAAmB,EAAE,IAAU;IACvD,OAAO,KAAK;SACP,SAAS,CAAC;QACP,IAAI,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ;QACvC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE;KACnC,CAAC;SACD,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC5D,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB;IACpC,OAAO,IAAI,6BAAgB,EAAiB,CAAC,aAAa,CACtD,IAAI,0BAAa,EAAE;SACd,WAAW,CAAC,eAAe,CAAC;SAC5B,QAAQ,CAAC,SAAS,CAAC;SACnB,QAAQ,CAAC,cAAM,CAAC,OAAO,CAAC,KAAK,CAAC;SAC9B,QAAQ,CAAC,wBAAW,CAAC,SAAS,CAAC,EACpC,IAAI,0BAAa,EAAE;SACd,WAAW,CAAC,mBAAmB,CAAC;SAChC,QAAQ,CAAC,aAAa,CAAC;SACvB,QAAQ,CAAC,cAAM,CAAC,OAAO,CAAC,MAAM,CAAC;SAC/B,QAAQ,CAAC,wBAAW,CAAC,SAAS,CAAC,EACpC,IAAI,0BAAa,EAAE;SACd,WAAW,CAAC,aAAa,CAAC;SAC1B,QAAQ,CAAC,OAAO,CAAC;SACjB,QAAQ,CAAC,cAAM,CAAC,KAAK,CAAC,KAAK,CAAC;SAC5B,QAAQ,CAAC,wBAAW,CAAC,SAAS,CAAC,CACvC,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CAAC,WAAmB,EAAE,UAAkB,EAAE,WAAoB,KAAK;IACtG,OAAO,IAAI,6BAAgB,EAAiB,CAAC,aAAa,CACtD,IAAI,0BAAa,EAAE;SACd,WAAW,CAAC,WAAW,CAAC;SACxB,QAAQ,CAAC,OAAO,CAAC;SACjB,QAAQ,CAAC,IAAI,CAAC;SACd,QAAQ,CAAC,wBAAW,CAAC,SAAS,CAAC;SAC/B,WAAW,CAAC,QAAQ,IAAI,WAAW,KAAK,CAAC,CAAC,EAC/C,IAAI,0BAAa,EAAE;SACd,WAAW,CAAC,UAAU,CAAC;SACvB,QAAQ,CAAC,UAAU,CAAC;SACpB,QAAQ,CAAC,IAAI,CAAC;SACd,QAAQ,CAAC,wBAAW,CAAC,SAAS,CAAC;SAC/B,WAAW,CAAC,QAAQ,IAAI,WAAW,KAAK,CAAC,CAAC,EAC/C,IAAI,0BAAa,EAAE;SACd,WAAW,CAAC,UAAU,CAAC;SACvB,QAAQ,CAAC,MAAM,CAAC;SAChB,QAAQ,CAAC,IAAI,CAAC;SACd,QAAQ,CAAC,wBAAW,CAAC,SAAS,CAAC;SAC/B,WAAW,CAAC,QAAQ,IAAI,WAAW,KAAK,UAAU,CAAC,EACxD,IAAI,0BAAa,EAAE;SACd,WAAW,CAAC,UAAU,CAAC;SACvB,QAAQ,CAAC,MAAM,CAAC;SAChB,QAAQ,CAAC,IAAI,CAAC;SACd,QAAQ,CAAC,wBAAW,CAAC,SAAS,CAAC;SAC/B,WAAW,CAAC,QAAQ,IAAI,WAAW,KAAK,UAAU,CAAC,CAC3D,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,yBAAyB,CAAC,SAAiB,EAAE,QAAgB;IACzE,OAAO,IAAI,6BAAgB,EAAiB,CAAC,aAAa,CACtD,IAAI,0BAAa,EAAE;SACd,WAAW,CAAC,SAAS,CAAC;SACtB,QAAQ,CAAC,SAAS,CAAC;SACnB,QAAQ,CAAC,cAAM,CAAC,OAAO,CAAC,KAAK,CAAC;SAC9B,QAAQ,CAAC,wBAAW,CAAC,OAAO,CAAC,EAClC,IAAI,0BAAa,EAAE;SACd,WAAW,CAAC,QAAQ,CAAC;SACrB,QAAQ,CAAC,QAAQ,CAAC;SAClB,QAAQ,CAAC,GAAG,CAAC;SACb,QAAQ,CAAC,wBAAW,CAAC,MAAM,CAAC,CACpC,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,MAAc;IACtC,OAAO,GAAG,cAAM,CAAC,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC,cAAc,EAAE,0BAA0B,CAAC;AAC1F,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,UAAkB,eAAe;IAChE,OAAO,eAAe,CAAC,GAAG,cAAM,CAAC,IAAI,CAAC,KAAK,IAAI,OAAO,EAAE,EAAE,4CAA4C,EAAE,cAAM,CAAC,IAAI,CAAC,CAAC;AACzH,CAAC"}