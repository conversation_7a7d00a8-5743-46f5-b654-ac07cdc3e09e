/**
 * Legacy Client Manager
 * Extracted Discord client setup from monolithic index.ts
 */
import { Client } from 'discord.js';
import MemoryManager from '../../services/memoryManager';
/**
 * Discord client manager for legacy compatibility
 */
export declare class LegacyClientManager {
    private client;
    private memoryManager;
    constructor();
    /**
     * Create Discord client with proper intents
     */
    private createClient;
    /**
     * Load commands from the commands directory
     */
    loadCommands(): void;
    /**
     * Get the Discord client instance
     */
    getClient(): Client;
    /**
     * Get memory manager instance
     */
    getMemoryManager(): MemoryManager;
    /**
     * Login to Discord
     */
    login(): Promise<void>;
    /**
     * Destroy the client connection
     */
    destroy(): void;
    /**
     * Check if client is ready
     */
    isReady(): boolean;
    /**
     * Get client uptime in milliseconds
     */
    getUptime(): number | null;
    /**
     * Get guild count
     */
    getGuildCount(): number;
    /**
     * Get user count across all guilds
     */
    getUserCount(): number;
}
export default LegacyClientManager;
//# sourceMappingURL=ClientManager.d.ts.map