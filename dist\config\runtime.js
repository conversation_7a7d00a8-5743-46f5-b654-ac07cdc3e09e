"use strict";
/**
 * Runtime Configuration Management
 * Dynamic feature toggle and configuration management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.runtimeConfig = exports.RuntimeConfigManager = void 0;
exports.isFeatureActiveRuntime = isFeatureActiveRuntime;
exports.requireFeatureRuntime = requireFeatureRuntime;
const events_1 = require("events");
const logger_1 = require("../core/logger");
const features_1 = require("./features");
/**
 * Runtime configuration manager
 */
class RuntimeConfigManager extends events_1.EventEmitter {
    constructor() {
        super();
        this.featureOverrides = new Map();
        this.configOverrides = new Map();
        this.logger = (0, logger_1.createLogger)('runtime-config');
    }
    /**
     * Override a feature's enabled state
     */
    setFeatureOverride(featureName, enabled, reason, source = 'manual', expiresAt) {
        const oldOverride = this.featureOverrides.get(featureName);
        const oldValue = oldOverride?.enabled;
        const override = {
            enabled,
            reason,
            timestamp: new Date(),
            expiresAt,
            source,
        };
        this.featureOverrides.set(featureName, override);
        this.logger.info(`Feature override set: ${featureName} = ${enabled}`, {
            featureName,
            enabled,
            reason,
            source,
            expiresAt,
        });
        // Emit change event
        this.emit('configChange', {
            type: 'feature_toggle',
            feature: featureName,
            oldValue,
            newValue: enabled,
            timestamp: new Date(),
            source,
        });
    }
    /**
     * Remove feature override
     */
    removeFeatureOverride(featureName, source = 'manual') {
        const override = this.featureOverrides.get(featureName);
        if (!override) {
            return false;
        }
        this.featureOverrides.delete(featureName);
        this.logger.info(`Feature override removed: ${featureName}`, {
            featureName,
            source,
            previousOverride: override,
        });
        // Emit change event
        this.emit('configChange', {
            type: 'feature_toggle',
            feature: featureName,
            oldValue: override.enabled,
            newValue: undefined,
            timestamp: new Date(),
            source,
        });
        return true;
    }
    /**
     * Check if a feature is enabled (considering overrides)
     */
    isFeatureEnabled(featureName) {
        // Check for expired overrides
        this.cleanupExpiredOverrides();
        // Check for runtime override first
        const override = this.featureOverrides.get(featureName);
        if (override) {
            return override.enabled;
        }
        // Fall back to static configuration
        const config = features_1.FEATURE_REGISTRY[featureName];
        return config?.enabled || false;
    }
    /**
     * Get feature configuration with runtime overrides
     */
    getFeatureConfig(featureName) {
        const baseConfig = features_1.FEATURE_REGISTRY[featureName];
        const override = this.featureOverrides.get(featureName);
        if (!baseConfig) {
            throw new Error(`Feature not found: ${featureName}`);
        }
        const config = { ...baseConfig };
        if (override) {
            config.enabled = override.enabled;
            return { ...config, override };
        }
        return config;
    }
    /**
     * Get all feature overrides
     */
    getFeatureOverrides() {
        this.cleanupExpiredOverrides();
        return new Map(this.featureOverrides);
    }
    /**
     * Set configuration override
     */
    setConfigOverride(key, value, source = 'manual') {
        const oldValue = this.configOverrides.get(key);
        this.configOverrides.set(key, value);
        this.logger.info(`Configuration override set: ${key}`, {
            key,
            oldValue,
            newValue: value,
            source,
        });
        // Emit change event
        this.emit('configChange', {
            type: 'config_update',
            feature: key,
            oldValue,
            newValue: value,
            timestamp: new Date(),
            source,
        });
    }
    /**
     * Get configuration value with overrides
     */
    getConfigValue(key, defaultValue) {
        return this.configOverrides.get(key) ?? defaultValue;
    }
    /**
     * Remove configuration override
     */
    removeConfigOverride(key, source = 'manual') {
        const oldValue = this.configOverrides.get(key);
        const removed = this.configOverrides.delete(key);
        if (removed) {
            this.logger.info(`Configuration override removed: ${key}`, {
                key,
                oldValue,
                source,
            });
            // Emit change event
            this.emit('configChange', {
                type: 'config_update',
                feature: key,
                oldValue,
                newValue: undefined,
                timestamp: new Date(),
                source,
            });
        }
        return removed;
    }
    /**
     * Get runtime statistics
     */
    getStats() {
        this.cleanupExpiredOverrides();
        return {
            featureOverrides: this.featureOverrides.size,
            configOverrides: this.configOverrides.size,
            totalFeatures: Object.keys(features_1.FEATURE_REGISTRY).length,
            enabledFeatures: Object.entries(features_1.FEATURE_REGISTRY)
                .filter(([name]) => this.isFeatureEnabled(name))
                .length,
            overriddenFeatures: Array.from(this.featureOverrides.keys()),
        };
    }
    /**
     * Export current configuration
     */
    exportConfig() {
        return {
            timestamp: new Date().toISOString(),
            featureOverrides: Object.fromEntries(Array.from(this.featureOverrides.entries()).map(([key, override]) => [
                key,
                {
                    enabled: override.enabled,
                    reason: override.reason,
                    timestamp: override.timestamp.toISOString(),
                    expiresAt: override.expiresAt?.toISOString(),
                    source: override.source,
                },
            ])),
            configOverrides: Object.fromEntries(this.configOverrides),
        };
    }
    /**
     * Import configuration
     */
    importConfig(config, source = 'import') {
        // Import feature overrides
        if (config.featureOverrides) {
            for (const [featureName, overrideDataRaw] of Object.entries(config.featureOverrides)) {
                const overrideData = overrideDataRaw;
                this.setFeatureOverride(featureName, overrideData.enabled ?? false, overrideData.reason || 'Imported configuration', source, overrideData.expiresAt ? new Date(overrideData.expiresAt) : undefined);
            }
        }
        // Import config overrides
        if (config.configOverrides) {
            for (const [key, value] of Object.entries(config.configOverrides)) {
                this.setConfigOverride(key, value, source);
            }
        }
        this.logger.info('Configuration imported', {
            featureOverrides: Object.keys(config.featureOverrides || {}).length,
            configOverrides: Object.keys(config.configOverrides || {}).length,
            source,
        });
    }
    /**
     * Reset all overrides
     */
    resetAll(source = 'manual') {
        const featureCount = this.featureOverrides.size;
        const configCount = this.configOverrides.size;
        this.featureOverrides.clear();
        this.configOverrides.clear();
        this.logger.info('All configuration overrides reset', {
            featureOverrides: featureCount,
            configOverrides: configCount,
            source,
        });
        // Emit reset event
        this.emit('configChange', {
            type: 'config_update',
            oldValue: { featureCount, configCount },
            newValue: { featureCount: 0, configCount: 0 },
            timestamp: new Date(),
            source,
        });
    }
    /**
     * Clean up expired overrides
     */
    cleanupExpiredOverrides() {
        const now = new Date();
        const expiredFeatures = [];
        for (const [featureName, override] of this.featureOverrides.entries()) {
            if (override.expiresAt && override.expiresAt <= now) {
                expiredFeatures.push(featureName);
            }
        }
        for (const featureName of expiredFeatures) {
            this.removeFeatureOverride(featureName, 'auto-expire');
        }
    }
    /**
     * Schedule automatic cleanup
     */
    startCleanupSchedule(intervalMs = 60000) {
        setInterval(() => {
            this.cleanupExpiredOverrides();
        }, intervalMs);
        this.logger.info('Cleanup schedule started', { intervalMs });
    }
}
exports.RuntimeConfigManager = RuntimeConfigManager;
/**
 * Global runtime config manager instance
 */
exports.runtimeConfig = new RuntimeConfigManager();
/**
 * Enhanced feature checking with runtime overrides
 */
function isFeatureActiveRuntime(featureName) {
    return exports.runtimeConfig.isFeatureEnabled(featureName);
}
/**
 * Feature toggle decorator with runtime support
 */
function requireFeatureRuntime(featureName) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = function (...args) {
            if (!isFeatureActiveRuntime(featureName)) {
                throw new Error(`Feature ${featureName} is not enabled`);
            }
            return originalMethod.apply(this, args);
        };
        return descriptor;
    };
}
exports.default = exports.runtimeConfig;
//# sourceMappingURL=runtime.js.map