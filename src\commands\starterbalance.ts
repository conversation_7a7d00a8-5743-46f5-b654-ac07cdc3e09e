import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { withE<PERSON>r<PERSON><PERSON><PERSON>, ValidationError, DatabaseError, PermissionError } from '../utils/errorHandler';
import { createAdminEmbed, createSuccessEmbed, createErrorEmbed, EMOJIS, COLORS } from '../utils/embedBuilder';
import { resolveRole, validateRolePermissions } from '../utils/roleResolver';
import { 
    createStarterBalanceRule, 
    updateStarterBalanceRule, 
    removeStarterBalanceRule, 
    getStarterBalanceRules,
    hasStarterBalanceRule 
} from '../services/starterBalanceService';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('starterbalance')
        .setDescription('Manage starter balance rules for roles (admin only)')
        .addStringOption(option =>
            option.setName('action')
                .setDescription('Action to perform')
                .setRequired(true)
                .addChoices(
                    { name: 'Add Rule', value: 'add' },
                    { name: 'Edit Rule', value: 'edit' },
                    { name: 'Remove Rule', value: 'remove' },
                    { name: 'List Rules', value: 'list' }
                ))
        .addStringOption(option =>
            option.setName('role')
                .setDescription('Discord role for the starter balance rule')
                .setRequired(false))
        .addIntegerOption(option =>
            option.setName('amount')
                .setDescription('Amount of PLC to give when role is assigned (1-10000)')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(10000))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Check permissions
        if (!interaction.guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError('You need Administrator permissions to use this command.');
        }

        const action = interaction.options.getString('action', true);
        const roleInput = interaction.options.getString('role');
        const amount = interaction.options.getInteger('amount');

        try {
            if (action === 'list') {
                // List all starter balance rules
                const rules = await getStarterBalanceRules(interaction.guild.id);

                if (rules.length === 0) {
                    const embed = createAdminEmbed('Starter Balance Rules')
                        .setDescription(
                            `${EMOJIS.ADMIN.INFO} **No Starter Balance Rules**\n\n` +
                            `There are currently no starter balance rules configured for this server.\n\n` +
                            `Use \`/starterbalance action:add\` to create your first rule.`
                        );

                    await interaction.reply({ embeds: [embed], ephemeral: false });
                    return;
                }

                const embed = createAdminEmbed('Starter Balance Rules')
                    .setDescription(
                        `${EMOJIS.ADMIN.LIST} **Current Starter Balance Rules**\n\n` +
                        `The following roles automatically grant PLC when assigned:`
                    );

                // Add fields for each rule (max 25 fields)
                const maxFields = Math.min(rules.length, 25);
                for (let i = 0; i < maxFields; i++) {
                    const rule = rules[i];
                    embed.addFields({
                        name: `${EMOJIS.ROLES.MEDAL} ${rule.roleName}`,
                        value: `**${rule.amount} PLC** granted on assignment`,
                        inline: true
                    });
                }

                if (rules.length > 25) {
                    embed.setFooter({ text: `Showing first 25 of ${rules.length} rules` });
                }

                await interaction.reply({ embeds: [embed], ephemeral: false });
                return;
            }

            // For add, edit, and remove actions, role is required
            if (!roleInput) {
                throw new ValidationError(`Role is required for the ${action} action. Please specify which role to ${action}.`);
            }

            // Resolve and validate the role
            const roleResolution = await resolveRole(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;

            // Validate role permissions
            validateRolePermissions(interaction.guild, role);

            if (action === 'add') {
                // Add new starter balance rule
                if (!amount) {
                    throw new ValidationError('Amount is required when adding a starter balance rule. Please specify how many PLC to give (1-10000).');
                }

                // Check if rule already exists
                if (await hasStarterBalanceRule(interaction.guild.id, role.id)) {
                    throw new ValidationError(`A starter balance rule already exists for role "${role.name}". Use the edit action to modify it.`);
                }

                await createStarterBalanceRule(interaction.guild.id, role.id, role.name, amount);

                const embed = createSuccessEmbed('Starter Balance Rule Added')
                    .setDescription(
                        `${EMOJIS.SUCCESS.CHECK} **Rule Created Successfully**\n\n` +
                        `Users will now automatically receive **${amount} PLC** when they are assigned the **${role.name}** role.`
                    )
                    .addFields({
                        name: `${EMOJIS.ADMIN.HAMMER} Configuration`,
                        value: 
                            `**Role:** ${role.name}\n` +
                            `**Amount:** ${amount} PLC\n` +
                            `**Action:** Granted when role is assigned`,
                        inline: false
                    });

                // Add resolution info if role was resolved by fuzzy matching
                if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                    embed.setFooter({ text: `Note: Resolved "${roleInput}" to "${role.name}"` });
                }

                await interaction.reply({ embeds: [embed], ephemeral: false });

            } else if (action === 'edit') {
                // Edit existing starter balance rule
                if (!amount) {
                    throw new ValidationError('Amount is required when editing a starter balance rule. Please specify the new PLC amount (1-10000).');
                }

                const updatedRule = await updateStarterBalanceRule(interaction.guild.id, role.id, amount);
                if (!updatedRule) {
                    throw new ValidationError(`No starter balance rule exists for role "${role.name}". Use the add action to create one.`);
                }

                const embed = createSuccessEmbed('Starter Balance Rule Updated')
                    .setDescription(
                        `${EMOJIS.SUCCESS.CHECK} **Rule Updated Successfully**\n\n` +
                        `The starter balance for **${role.name}** has been updated to **${amount} PLC**.`
                    )
                    .addFields({
                        name: `${EMOJIS.ADMIN.HAMMER} Updated Configuration`,
                        value: 
                            `**Role:** ${role.name}\n` +
                            `**New Amount:** ${amount} PLC\n` +
                            `**Action:** Granted when role is assigned`,
                        inline: false
                    });

                // Add resolution info if role was resolved by fuzzy matching
                if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                    embed.setFooter({ text: `Note: Resolved "${roleInput}" to "${role.name}"` });
                }

                await interaction.reply({ embeds: [embed], ephemeral: false });

            } else if (action === 'remove') {
                // Remove starter balance rule
                const removed = await removeStarterBalanceRule(interaction.guild.id, role.id);
                if (!removed) {
                    throw new ValidationError(`No starter balance rule exists for role "${role.name}".`);
                }

                const embed = createSuccessEmbed('Starter Balance Rule Removed')
                    .setDescription(
                        `${EMOJIS.SUCCESS.CHECK} **Rule Removed Successfully**\n\n` +
                        `The starter balance rule for **${role.name}** has been removed.\n\n` +
                        `${EMOJIS.ADMIN.INFO} Users will no longer receive PLC when assigned this role.`
                    );

                // Add resolution info if role was resolved by fuzzy matching
                if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                    embed.setFooter({ text: `Note: Resolved "${roleInput}" to "${role.name}"` });
                }

                await interaction.reply({ embeds: [embed], ephemeral: false });
            }

        } catch (error: unknown) {
            if (error instanceof ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new DatabaseError('Failed to manage starter balance rule. Please try again.', error);
            }
            throw new DatabaseError('An unexpected error occurred while managing the starter balance rule.');
        }
    })
};
