/**
 * Trade Service Module Index
 * Centralized exports for the trade system
 */

// Main Service
export { TradeService, TradeCreationParams, TradeStateTransition } from './TradeService';

// Managers
export { EscrowManager, EscrowOperation } from './managers/EscrowManager';
export { TradeValidator } from './managers/TradeValidator';
export { TradeNotificationManager } from './managers/TradeNotificationManager';

// Re-export trade-related models and types
export {
  Trade,
  ITrade,
  EscrowTransaction,
  IEscrowTransaction,
  DisputeCase,
  IDisputeCase,
  TradeConfirmation,
  ITradeConfirmation,
  UserTradeStats,
  IUserTradeStats,
  TradeState,
  EscrowTransactionType,
  DisputeStatus,
  DisputeResolution,
  ConfirmationType
} from '../../models';

// Trade system constants
export { TRADE } from '../../config/constants';
