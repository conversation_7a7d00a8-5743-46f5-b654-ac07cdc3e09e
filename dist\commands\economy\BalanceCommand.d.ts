/**
 * Balance Command
 * Refactored balance command using the new command architecture
 */
import { BaseCommand } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
/**
 * Balance command implementation
 */
export declare class BalanceCommand extends BaseCommand {
    constructor();
    /**
     * Execute the balance command
     */
    protected executeCommand(context: CommandContext): Promise<void>;
}
//# sourceMappingURL=BalanceCommand.d.ts.map