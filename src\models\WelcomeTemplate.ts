import { Schema, model, Document } from 'mongoose';

export type TriggerType = 'join' | 'role_add' | 'role_remove';
export type DeliveryType = 'dm' | 'channel' | 'both';

export interface IWelcomeField {
    name: string;
    value: string;
    inline: boolean;
}

export interface IWelcomeButton {
    label: string;
    style: 'Link';
    url: string; // For link buttons
}

export interface IWelcomeTemplate extends Document {
    guildId: string;
    name: string;
    triggerType: TriggerType;
    triggerRoleId?: string; // Specific role for role_add/role_remove triggers
    deliveryType: DeliveryType;
    channelId?: string; // Specific channel override

    // Message format configuration
    useEmbed: boolean; // Whether to send as embed or plain text

    // Embed configuration
    title?: string;
    description?: string;
    color?: string; // Hex color code
    thumbnailUrl?: string;
    imageUrl?: string;
    footerText?: string;
    showTimestamp: boolean;

    // Dynamic fields and buttons
    fields: IWelcomeField[];
    buttons: IWelcomeButton[];

    // Advanced options
    delaySeconds: number; // Delay before sending message
    enabled: boolean;
    priority: number; // For multiple templates with same trigger
    
    // Conditional logic
    conditions?: {
        minAccountAge?: number; // Minimum account age in days
        requireVerification?: boolean;
        excludeRoles?: string[]; // Role IDs to exclude
        includeRoles?: string[]; // Role IDs to include
    };
    
    createdAt: Date;
    updatedAt: Date;
}

const welcomeFieldSchema = new Schema<IWelcomeField>({
    name: {
        type: String,
        required: [true, 'Field name is required'],
        maxlength: [256, 'Field name cannot exceed 256 characters']
    },
    value: {
        type: String,
        required: [true, 'Field value is required'],
        maxlength: [1024, 'Field value cannot exceed 1024 characters']
    },
    inline: {
        type: Boolean,
        default: false
    }
});

const welcomeButtonSchema = new Schema<IWelcomeButton>({
    label: {
        type: String,
        required: [true, 'Button label is required'],
        maxlength: [80, 'Button label cannot exceed 80 characters']
    },
    style: {
        type: String,
        enum: ['Link'],
        required: [true, 'Button style is required'],
        default: 'Link'
    },
    url: {
        type: String,
        required: [true, 'URL is required for link buttons'],
        validate: {
            validator: function(v: string): boolean {
                try {
                    new URL(v);
                    return true;
                } catch {
                    return false;
                }
            },
            message: 'URL must be a valid URL'
        }
    }
});

const welcomeTemplateSchema = new Schema<IWelcomeTemplate>({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function(v: string): boolean {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    name: {
        type: String,
        required: [true, 'Template name is required'],
        maxlength: [100, 'Template name cannot exceed 100 characters'],
        validate: {
            validator: function(v: string): boolean {
                return !!(v && v.trim().length > 0);
            },
            message: 'Template name cannot be empty'
        }
    },
    triggerType: {
        type: String,
        enum: ['join', 'role_add', 'role_remove'],
        required: [true, 'Trigger type is required']
    },
    triggerRoleId: {
        type: String,
        validate: {
            validator: function(v: string): boolean {
                return !v || (v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Trigger role ID must be a valid Discord snowflake'
        }
    },
    deliveryType: {
        type: String,
        enum: ['dm', 'channel', 'both'],
        required: [true, 'Delivery type is required']
    },
    channelId: {
        type: String,
        validate: {
            validator: function(v: string): boolean {
                return !v || (v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Channel ID must be a valid Discord snowflake'
        }
    },
    useEmbed: {
        type: Boolean,
        default: true
    },
    title: {
        type: String,
        maxlength: [256, 'Title cannot exceed 256 characters']
    },
    description: {
        type: String,
        maxlength: [4096, 'Description cannot exceed 4096 characters']
    },
    color: {
        type: String,
        validate: {
            validator: function(v: string): boolean {
                return !v || /^#[0-9A-Fa-f]{6}$/.test(v);
            },
            message: 'Color must be a valid hex color code (e.g., #dd7d00)'
        }
    },
    thumbnailUrl: {
        type: String,
        validate: {
            validator: function(v: string): boolean {
                if (!v) return true;
                try {
                    new URL(v);
                    return true;
                } catch {
                    return false;
                }
            },
            message: 'Thumbnail URL must be a valid URL'
        }
    },
    imageUrl: {
        type: String,
        validate: {
            validator: function(v: string): boolean {
                if (!v) return true;
                try {
                    new URL(v);
                    return true;
                } catch {
                    return false;
                }
            },
            message: 'Image URL must be a valid URL'
        }
    },
    footerText: {
        type: String,
        maxlength: [2048, 'Footer text cannot exceed 2048 characters']
    },
    showTimestamp: {
        type: Boolean,
        default: true
    },
    fields: [welcomeFieldSchema],
    buttons: [welcomeButtonSchema],
    delaySeconds: {
        type: Number,
        default: 0,
        min: [0, 'Delay cannot be negative'],
        max: [3600, 'Delay cannot exceed 1 hour (3600 seconds)']
    },
    enabled: {
        type: Boolean,
        default: true
    },
    priority: {
        type: Number,
        default: 0,
        min: [0, 'Priority cannot be negative']
    },
    conditions: {
        minAccountAge: {
            type: Number,
            min: [0, 'Minimum account age cannot be negative'],
            max: [365, 'Minimum account age cannot exceed 365 days']
        },
        requireVerification: {
            type: Boolean,
            default: false
        },
        excludeRoles: [{
            type: String,
            validate: {
                validator: function(v: string): boolean {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }],
        includeRoles: [{
            type: String,
            validate: {
                validator: function(v: string): boolean {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }]
    }
}, {
    timestamps: true
});

// Compound index for efficient template lookups
welcomeTemplateSchema.index({ guildId: 1, triggerType: 1, enabled: 1 });

// Index for role-specific triggers
welcomeTemplateSchema.index({ guildId: 1, triggerRoleId: 1 });

// Unique index for template names within a guild
welcomeTemplateSchema.index({ guildId: 1, name: 1 }, { unique: true });

export const WelcomeTemplate = model<IWelcomeTemplate>('WelcomeTemplate', welcomeTemplateSchema);
