{"version": 3, "file": "LegacyApplication.js", "sourceRoot": "", "sources": ["../../src/legacy/LegacyApplication.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,wEAA2E;AAC3E,0DAA6D;AAC7D,oDAAuD;AACvD,oEAAuE;AACvE,4DAA+D;AAC/D,8DAAiE;AACjE,0DAA6D;AAE7D;;GAEG;AACH,MAAa,iBAAiB;IAU5B;QAFQ,kBAAa,GAAG,KAAK,CAAC;QAG5B,IAAI,CAAC,aAAa,GAAG,IAAI,mCAAmB,EAAE,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAE7C,sBAAsB;QACtB,IAAI,CAAC,WAAW,GAAG,IAAI,+BAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtD,IAAI,CAAC,kBAAkB,GAAG,IAAI,6CAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpE,IAAI,CAAC,cAAc,GAAG,IAAI,qCAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAI,CAAC,eAAe,GAAG,IAAI,uCAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,CAAC,aAAa,GAAG,IAAI,mCAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;YAEtE,sBAAsB;YACtB,MAAM,+CAAyB,CAAC,UAAU,EAAE,CAAC;YAE7C,gBAAgB;YAChB,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;YAElC,uBAAuB;YACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,mBAAmB;YACnB,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAEjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,cAAc;QACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;YAElE,6CAA6C;YAC7C,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE;YACxD,MAAM,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,iBAAiB;QACjB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YAChD,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,kBAAkB;QAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,oBAAoB,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;YAC5D,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;YAChD,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;YACnD,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE;YACjE,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE;YAC9D,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAE7C,iBAAiB;YACjB,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YAElC,iBAAiB;YACjB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAE7B,4BAA4B;YAC5B,MAAM,+CAAyB,CAAC,KAAK,EAAE,CAAC;YAExC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,aAAa;YAC/B,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;YACzC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;YACtC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;YAC9C,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;YAC5C,cAAc,EAAE,+CAAyB,CAAC,mBAAmB,EAAE;YAC/D,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE;SAC/C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,aAAa;YAC1B,IAAI,EAAE,IAAI,CAAC,WAAW;YACtB,WAAW,EAAE,IAAI,CAAC,kBAAkB;YACpC,OAAO,EAAE,IAAI,CAAC,cAAc;YAC5B,QAAQ,EAAE,IAAI,CAAC,eAAe;YAC9B,MAAM,EAAE,IAAI,CAAC,aAAa;SAC3B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IAC5D,CAAC;CACF;AAlKD,8CAkKC;AAED,kBAAe,iBAAiB,CAAC"}