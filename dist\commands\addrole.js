"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const errorHandler_1 = require("../utils/errorHandler");
const roleResolver_1 = require("../utils/roleResolver");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('addrole')
        .setDescription('Add a role achievement (admin only)')
        .addStringOption(option => option.setName('role').setDescription('Role name or ID').setRequired(true))
        .addIntegerOption(option => option.setName('price').setDescription('Required PLC balance to unlock').setRequired(true))
        .addStringOption(option => option.setName('description').setDescription('Achievement description').setRequired(false))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError();
        }
        const roleInput = interaction.options.getString('role', true);
        const price = interaction.options.getInteger('price', true);
        const description = interaction.options.getString('description') || '';
        // Validation
        if (price <= 0) {
            throw new errorHandler_1.ValidationError('Price must be greater than zero.');
        }
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        try {
            // Resolve the role by name or ID
            const roleResolution = await (0, roleResolver_1.resolveRole)(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;
            // Validate role permissions
            (0, roleResolver_1.validateRolePermissions)(interaction.guild, role);
            // Check if role achievement already exists
            const exists = await User_1.RoleForSale.findOne({ roleId: role.id });
            if (exists) {
                throw new errorHandler_1.ValidationError(`Role achievement "${role.name}" already exists.`);
            }
            // Add role achievement
            await User_1.RoleForSale.create({
                roleId: role.id,
                name: role.name,
                price,
                description
            });
            // Create success message with resolution info
            let successMessage = `Role achievement **${role.name}** added with requirement of **${price}** Phalanx Loyalty Coins! 🏆`;
            if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                successMessage += `\n*Note: Resolved "${roleInput}" to "${role.name}"*`;
            }
            await interaction.reply({
                content: successMessage,
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to add role achievement.');
            }
        }
    })
};
//# sourceMappingURL=addrole.js.map