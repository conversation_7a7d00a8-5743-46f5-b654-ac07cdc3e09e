import { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, User, ColorResolvable } from 'discord.js';

// Brand colors
export const COLORS = {
    PRIMARY: '#dd7d00' as ColorResolvable,
    SUCCESS: '#00ff00' as ColorResolvable,
    ERROR: '#ff0000' as ColorResolvable,
    WARNING: '#ffaa00' as ColorResolvable,
    INFO: '#0099ff' as ColorResolvable,
    GOLD: '#ffd700' as ColorResolvable
};

// Emoji collections
export const EMOJIS = {
    ECONOMY: {
        COINS: '🪙',
        MONEY: '💰',
        DIAMOND: '💎',
        DOLLAR: '💲',
        SPARKLES: '✨',
        BANK: '🏦',
        CHART: '📈',
        TRANSFER: '🔄' // Added for payment/transfer actions
    },
    SUCCESS: {
        CHECK: '✅',
        PARTY: '🎉',
        STAR: '⭐',
        CROWN: '👑',
        TROPHY: '🏆',
        THUMBS_UP: '👍',
        REFRESH: '🔄' // Added for feature reset
    },
    ROLES: {
        MEDAL: '🏅',
        MASK: '🎭',
        SHIELD: '🔰',
        ARMOR: '🛡️',
        BADGE: '📛',
        RIBBON: '🎀'
    },
    ACTIONS: {
        LIGHTNING: '⚡',
        ROCKET: '🚀',
        TARGET: '🎯',
        FIRE: '🔥',
        MAGIC: '🪄',
        GEAR: '⚙️',
        SENDER: '👤' // Added for sender field
    },
    ADMIN: {
        SCALES: '⚖️',
        HAMMER: '🔨',
        WARNING: '⚠️',
        TOOLS: '🛠️',
        LOCK: '🔒',
        KEY: '🔑',
        INFO: 'ℹ️',
        LIST: '📋',
        CLOCK: '🕐',
        SETTINGS: '⚙️'
    },
    MILESTONE: {
        TROPHY: '🏆',
        STAR: '⭐',
        MEDAL: '🏅',
        STREAK: '🔥',
        DIVERSITY: '🌈',
        VOICE: '🎤',
        MESSAGE: '💬',
        ANNIVERSARY: '🎂',
        PROGRESS: '📊',
        ACHIEVEMENT: '🎯'
    },
    TRADE: {
        HANDSHAKE: '🤝',
        PACKAGE: '📦',
        SCALES: '⚖️',
        HOURGLASS: '⏳',
        TIMER: '⏰',
        PENDING: '🟡',
        ACTIVE: '🟢',
        COMPLETED: '✅',
        CANCELLED: '❌',
        EXPIRED: '⏰',
        DISPUTED: '⚠️',
        ESCROW: '🔒',
        RELEASE: '🔓',
        PROPOSAL: '📋',
        CONFIRMATION: '✔️'
    },
    MISC: {
        CLOCK: '🕐',
        CALENDAR: '📅',
        BOOK: '📖',
        SCROLL: '📜',
        MAGNIFYING: '🔍',
        BELL: '🔔',
        LIGHTBULB: '💡',
        ID: '🆔',
        USER: '👤',
        GUILD: '🏰',
        ROLE: '🎭',
        SPARKLES: '✨',
        LINK: '🔗',
        TAG: '🏷️',
        ATTACHMENT: '📎'
    }
};

/**
 * Creates a base embed with consistent branding
 */
export function createBaseEmbed(title?: string, description?: string, color: ColorResolvable = COLORS.PRIMARY): EmbedBuilder {
    const embed = new EmbedBuilder()
        .setColor(color)
        .setTimestamp();

    if (title) embed.setTitle(title);
    if (description) embed.setDescription(description);

    return embed;
}

/**
 * Creates a success embed
 */
export function createSuccessEmbed(title: string, description?: string): EmbedBuilder {
    return createBaseEmbed(`${EMOJIS.SUCCESS.CHECK} ${title}`, description, COLORS.SUCCESS);
}

/**
 * Creates an error embed
 */
export function createErrorEmbed(title: string, description?: string): EmbedBuilder {
    return createBaseEmbed(`${EMOJIS.ADMIN.WARNING} ${title}`, description, COLORS.ERROR);
}

/**
 * Creates an economy-themed embed
 */
export function createEconomyEmbed(title: string, description?: string): EmbedBuilder {
    return createBaseEmbed(`${EMOJIS.ECONOMY.COINS} ${title}`, description, COLORS.PRIMARY);
}

/**
 * Creates an admin-themed embed
 */
export function createAdminEmbed(title: string, description?: string): EmbedBuilder {
    return createBaseEmbed(`${EMOJIS.ADMIN.HAMMER} ${title}`, description, COLORS.WARNING);
}

/**
 * Adds user information to an embed
 */
export function addUserInfo(embed: EmbedBuilder, user: User): EmbedBuilder {
    return embed
        .setAuthor({
            name: user.displayName || user.username,
            iconURL: user.displayAvatarURL()
        })
        .setThumbnail(user.displayAvatarURL({ size: 128 }));
}

/**
 * Creates quick action buttons
 */
export function createQuickActionButtons(): ActionRowBuilder<ButtonBuilder> {
    return new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
            .setCustomId('quick_balance')
            .setLabel('Balance')
            .setEmoji(EMOJIS.ECONOMY.COINS)
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('quick_leaderboard')
            .setLabel('Leaderboard')
            .setEmoji(EMOJIS.SUCCESS.TROPHY)
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('quick_roles')
            .setLabel('Roles')
            .setEmoji(EMOJIS.ROLES.MEDAL)
            .setStyle(ButtonStyle.Secondary)
    );
}

/**
 * Creates navigation buttons for paginated content
 */
export function createNavigationButtons(currentPage: number, totalPages: number, disabled: boolean = false): ActionRowBuilder<ButtonBuilder> {
    return new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
            .setCustomId('nav_first')
            .setLabel('First')
            .setEmoji('⏮️')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(disabled || currentPage === 1),
        new ButtonBuilder()
            .setCustomId('nav_prev')
            .setLabel('Previous')
            .setEmoji('◀️')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(disabled || currentPage === 1),
        new ButtonBuilder()
            .setCustomId('nav_next')
            .setLabel('Next')
            .setEmoji('▶️')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(disabled || currentPage === totalPages),
        new ButtonBuilder()
            .setCustomId('nav_last')
            .setLabel('Last')
            .setEmoji('⏭️')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(disabled || currentPage === totalPages)
    );
}

/**
 * Creates confirmation buttons
 */
export function createConfirmationButtons(confirmId: string, cancelId: string): ActionRowBuilder<ButtonBuilder> {
    return new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
            .setCustomId(confirmId)
            .setLabel('Confirm')
            .setEmoji(EMOJIS.SUCCESS.CHECK)
            .setStyle(ButtonStyle.Success),
        new ButtonBuilder()
            .setCustomId(cancelId)
            .setLabel('Cancel')
            .setEmoji('❌')
            .setStyle(ButtonStyle.Danger)
    );
}

/**
 * Formats a number with appropriate emoji and styling
 */
export function formatCoins(amount: number): string {
    return `${EMOJIS.ECONOMY.COINS} **${amount.toLocaleString()}** Phalanx Loyalty Coins`;
}

/**
 * Creates a loading embed
 */
export function createLoadingEmbed(message: string = 'Processing...'): EmbedBuilder {
    return createBaseEmbed(`${EMOJIS.MISC.CLOCK} ${message}`, 'Please wait while we process your request.', COLORS.INFO);
}
