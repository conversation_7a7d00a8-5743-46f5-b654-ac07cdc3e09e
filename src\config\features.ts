/**
 * Feature Configuration System
 * Centralized feature toggle management with runtime configuration
 */

import { ENV, isFeatureEnabled } from './environment';
import { FEATURES } from './constants';

/**
 * Feature configuration interface
 */
export interface FeatureConfig {
  enabled: boolean;
  description: string;
  dependencies?: string[];
  adminOnly?: boolean;
  guildSpecific?: boolean;
}

/**
 * Feature registry with metadata
 */
export const FEATURE_REGISTRY: Record<string, FeatureConfig> = {
  // Core Economy System
  ECONOMY_SYSTEM: {
    enabled: FEATURES.ECONOMY_SYSTEM,
    description: 'Core Phalanx Loyalty Coin economy system',
    dependencies: [],
  },
  
  // Milestone System
  MILESTONE_SYSTEM: {
    enabled: FEATURES.MILESTONE_SYSTEM && (ENV.ENABLE_MILESTONE_SYSTEM ?? true),
    description: 'Automated milestone rewards for user activities',
    dependencies: ['ECONOMY_SYSTEM'],
  },
  
  // Dynasty System
  DYNASTY_SYSTEM: {
    enabled: FEATURES.DYNASTY_SYSTEM && (ENV.ENABLE_DYNASTY_SYSTEM ?? false),
    description: 'Dynasty groups with shared progression and benefits',
    dependencies: ['ECONOMY_SYSTEM', 'MILESTONE_SYSTEM'],
  },
  
  // Reaction Rewards
  REACTION_REWARDS: {
    enabled: FEATURES.REACTION_REWARDS && (ENV.ENABLE_REACTION_REWARDS ?? true),
    description: 'Coin rewards for message reactions in monetized channels',
    dependencies: ['ECONOMY_SYSTEM'],
  },
  
  // Tax System
  TAX_SYSTEM: {
    enabled: FEATURES.TAX_SYSTEM && (ENV.ENABLE_TAX_SYSTEM ?? true),
    description: 'Automated taxation system for role maintenance',
    dependencies: ['ECONOMY_SYSTEM'],
    adminOnly: true,
  },
  
  // Starter Balance
  STARTER_BALANCE: {
    enabled: FEATURES.STARTER_BALANCE,
    description: 'Automatic starter balance for new role assignments',
    dependencies: ['ECONOMY_SYSTEM'],
    adminOnly: true,
  },
  
  // Auto Messages
  AUTO_MESSAGES: {
    enabled: FEATURES.AUTO_MESSAGES,
    description: 'Automated messages for server events',
    dependencies: [],
    adminOnly: true,
    guildSpecific: true,
  },
  
  // Role Automation
  ROLE_AUTOMATION: {
    enabled: FEATURES.ROLE_AUTOMATION,
    description: 'Automatic role assignment based on PLC balance',
    dependencies: ['ECONOMY_SYSTEM'],
  },
  
  // User Cleanup
  USER_CLEANUP: {
    enabled: FEATURES.USER_CLEANUP,
    description: 'Automatic cleanup of user data when members leave',
    dependencies: [],
    adminOnly: true,
  },
  
  // Audit Logging
  AUDIT_LOGGING: {
    enabled: FEATURES.AUDIT_LOGGING,
    description: 'Comprehensive audit logging for admin actions',
    dependencies: [],
    adminOnly: true,
  },

  // Trade System
  TRADE_SYSTEM: {
    enabled: FEATURES.TRADE_SYSTEM && (ENV.ENABLE_TRADE_SYSTEM ?? true),
    description: 'Secure peer-to-peer trading system with escrow protection',
    dependencies: ['ECONOMY_SYSTEM'],
  },
};

/**
 * Feature dependency resolver
 */
class FeatureDependencyResolver {
  private resolved = new Set<string>();
  private resolving = new Set<string>();
  
  /**
   * Resolves feature dependencies and returns enabled features
   */
  resolve(): Set<string> {
    const enabledFeatures = new Set<string>();
    
    for (const [featureName, config] of Object.entries(FEATURE_REGISTRY)) {
      if (this.isFeatureEnabled(featureName)) {
        enabledFeatures.add(featureName);
      }
    }
    
    return enabledFeatures;
  }
  
  /**
   * Checks if a feature is enabled, considering dependencies
   */
  private isFeatureEnabled(featureName: string): boolean {
    if (this.resolved.has(featureName)) {
      return true;
    }
    
    if (this.resolving.has(featureName)) {
      throw new Error(`Circular dependency detected for feature: ${featureName}`);
    }
    
    const config = FEATURE_REGISTRY[featureName];
    if (!config) {
      return false;
    }
    
    // Check if feature is explicitly disabled
    if (!config.enabled) {
      return false;
    }
    
    // Check dependencies
    this.resolving.add(featureName);
    
    if (config.dependencies) {
      for (const dependency of config.dependencies) {
        if (!this.isFeatureEnabled(dependency)) {
          this.resolving.delete(featureName);
          return false;
        }
      }
    }
    
    this.resolving.delete(featureName);
    this.resolved.add(featureName);
    return true;
  }
}

/**
 * Global feature manager instance
 */
class FeatureManager {
  private enabledFeatures: Set<string>;
  
  constructor() {
    const resolver = new FeatureDependencyResolver();
    this.enabledFeatures = resolver.resolve();
    
    // Log enabled features
    console.log('[Feature Manager] Enabled features:', Array.from(this.enabledFeatures).join(', '));
  }
  
  /**
   * Check if a feature is enabled
   */
  isEnabled(featureName: string): boolean {
    return this.enabledFeatures.has(featureName);
  }
  
  /**
   * Get all enabled features
   */
  getEnabledFeatures(): string[] {
    return Array.from(this.enabledFeatures);
  }
  
  /**
   * Get feature configuration
   */
  getFeatureConfig(featureName: string): FeatureConfig | undefined {
    return FEATURE_REGISTRY[featureName];
  }
  
  /**
   * Check if feature requires admin permissions
   */
  requiresAdmin(featureName: string): boolean {
    const config = this.getFeatureConfig(featureName);
    return config?.adminOnly ?? false;
  }
  
  /**
   * Check if feature is guild-specific
   */
  isGuildSpecific(featureName: string): boolean {
    const config = this.getFeatureConfig(featureName);
    return config?.guildSpecific ?? false;
  }
  
  /**
   * Get features by category
   */
  getFeaturesByCategory(category: 'core' | 'admin' | 'advanced'): string[] {
    const coreFeatures = ['ECONOMY_SYSTEM', 'REACTION_REWARDS', 'ROLE_AUTOMATION'];
    const adminFeatures = ['TAX_SYSTEM', 'STARTER_BALANCE', 'AUTO_MESSAGES', 'USER_CLEANUP', 'AUDIT_LOGGING'];
    const advancedFeatures = ['MILESTONE_SYSTEM', 'DYNASTY_SYSTEM'];
    
    const categoryMap = {
      core: coreFeatures,
      admin: adminFeatures,
      advanced: advancedFeatures,
    };
    
    return categoryMap[category].filter(feature => this.isEnabled(feature));
  }
}

/**
 * Global feature manager instance
 */
export const featureManager = new FeatureManager();

/**
 * Helper functions for feature checking
 */
export const isFeatureActive = (featureName: string): boolean => {
  return featureManager.isEnabled(featureName);
};

export const requiresAdminPermission = (featureName: string): boolean => {
  return featureManager.requiresAdmin(featureName);
};

export const isGuildSpecificFeature = (featureName: string): boolean => {
  return featureManager.isGuildSpecific(featureName);
};

/**
 * Feature guard decorator for commands and services
 */
export function requireFeature(featureName: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      if (!isFeatureActive(featureName)) {
        throw new Error(`Feature ${featureName} is not enabled`);
      }
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
}

export default featureManager;
