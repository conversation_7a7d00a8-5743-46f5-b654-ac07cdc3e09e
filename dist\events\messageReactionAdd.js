"use strict";
/**
 * Message Reaction Add Event Handler
 * Handles Discord message reaction events for rewards and milestone tracking
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageReactionAddEventHandler = void 0;
const base_1 = require("./base");
/**
 * Message reaction add event handler
 */
class MessageReactionAddEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'messageReactionAdd');
        this.name = 'messageReactionAdd';
    }
    /**
     * Execute message reaction add event
     */
    async execute(reaction, user) {
        try {
            // Handle partial reactions and users
            const fullReaction = await this.ensureFullReaction(reaction);
            const fullUser = await this.ensureFullUser(user);
            if (!fullReaction || !fullUser) {
                return;
            }
            // Skip bot users
            if (fullUser.bot)
                return;
            // Process reaction rewards
            if (this.isFeatureEnabled('REACTION_REWARDS')) {
                await this.processReactionReward(fullReaction, fullUser);
            }
            // Track reaction activity for milestones
            if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
                await this.trackReactionActivity(fullReaction, fullUser);
            }
        }
        catch (error) {
            this.handleError(error, {
                messageId: reaction.message.id,
                channelId: reaction.message.channel.id,
                guildId: reaction.message.guild?.id,
                userId: user.id,
                emoji: reaction.emoji.name,
            });
        }
    }
    /**
     * Ensure reaction is fully fetched
     */
    async ensureFullReaction(reaction) {
        if (reaction.partial) {
            try {
                return await reaction.fetch();
            }
            catch (error) {
                this.logger.error('[MessageReactionAdd] Failed to fetch reaction', { error });
                return null;
            }
        }
        return reaction;
    }
    /**
     * Ensure user is fully fetched
     */
    async ensureFullUser(user) {
        if (user.partial) {
            try {
                return await user.fetch();
            }
            catch (error) {
                this.logger.error('[MessageReactionAdd] Failed to fetch user', { error });
                return null;
            }
        }
        return user;
    }
    /**
     * Process reaction reward
     */
    async processReactionReward(reaction, user) {
        try {
            // Import here to avoid circular dependencies
            const { processReactionReward } = await Promise.resolve().then(() => __importStar(require('../services/reactionRewardsService')));
            await processReactionReward(reaction, user);
        }
        catch (error) {
            this.logger.error('[MessageReactionAdd] Error processing reaction reward', {
                error,
                messageId: reaction.message.id,
                userId: user.id,
                emoji: reaction.emoji.name,
            });
        }
    }
    /**
     * Track reaction activity for milestones
     */
    async trackReactionActivity(reaction, user) {
        try {
            // Import here to avoid circular dependencies
            const { checkAndProcessMilestones } = await Promise.resolve().then(() => __importStar(require('../services/milestoneService')));
            const milestoneResults = await checkAndProcessMilestones(this.app.client, user.id, reaction.message.guild?.id || '', 'reaction', {
                channelId: reaction.message.channel.id,
                emoji: reaction.emoji,
                timestamp: new Date()
            });
            if (milestoneResults.length > 0) {
                this.logger.info(`[MessageReactionAdd] User ${user.tag} achieved ${milestoneResults.length} milestone(s) from reaction activity`);
            }
        }
        catch (error) {
            this.logger.error('[MessageReactionAdd] Error processing reaction milestones', {
                error,
                messageId: reaction.message.id,
                userId: user.id,
                guildId: reaction.message.guild?.id,
            });
        }
    }
}
exports.MessageReactionAddEventHandler = MessageReactionAddEventHandler;
//# sourceMappingURL=messageReactionAdd.js.map