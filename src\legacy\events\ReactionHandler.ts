/**
 * Legacy Reaction Handler
 * Extracted reaction handling from monolithic index.ts
 */

import { Client, MessageReaction, User, PartialMessageReaction, PartialUser } from 'discord.js';
import { processReactionReward } from '../../services/reactionRewardsService';
import { checkAndProcessMilestones } from '../../services/milestoneService';

/**
 * Reaction handler for legacy compatibility
 */
export class LegacyReactionHandler {
  private client: Client;

  constructor(client: Client) {
    this.client = client;
  }

  /**
   * Handle message reaction add events
   */
  async handleReactionAdd(
    reaction: MessageReaction | PartialMessageReaction, 
    user: User | PartialUser
  ): Promise<void> {
    try {
      // Handle partial reactions and users
      const fullReaction = await this.ensureFullReaction(reaction);
      const fullUser = await this.ensureFullUser(user);

      if (!fullReaction || !fullUser) {
        return;
      }

      // Skip bot users
      if (fullUser.bot) return;

      // Process the reaction reward
      await this.processReactionReward(fullReaction, fullUser);

      // Track reaction activity for milestones
      await this.trackReactionActivity(fullReaction, fullUser);

    } catch (error) {
      console.error('[Reaction Handler] Error in messageReactionAdd handler:', error);
    }
  }

  /**
   * Ensure reaction is fully fetched
   */
  private async ensureFullReaction(
    reaction: MessageReaction | PartialMessageReaction
  ): Promise<MessageReaction | null> {
    if (reaction.partial) {
      try {
        return await reaction.fetch();
      } catch (error) {
        console.error('[Reaction Handler] Failed to fetch reaction:', error);
        return null;
      }
    }
    return reaction as MessageReaction;
  }

  /**
   * Ensure user is fully fetched
   */
  private async ensureFullUser(user: User | PartialUser): Promise<User | null> {
    if (user.partial) {
      try {
        return await user.fetch();
      } catch (error) {
        console.error('[Reaction Handler] Failed to fetch user:', error);
        return null;
      }
    }
    return user as User;
  }

  /**
   * Process reaction reward
   */
  private async processReactionReward(reaction: MessageReaction, user: User): Promise<void> {
    try {
      await processReactionReward(reaction, user);
    } catch (error) {
      console.error('[Reaction Handler] Error processing reaction reward:', error);
    }
  }

  /**
   * Track reaction activity for milestones
   */
  private async trackReactionActivity(reaction: MessageReaction, user: User): Promise<void> {
    try {
      const milestoneResults = await checkAndProcessMilestones(
        this.client,
        user.id,
        reaction.message.guild?.id || '',
        'reaction',
        {
          channelId: reaction.message.channel.id,
          emoji: reaction.emoji,
          timestamp: new Date()
        }
      );

      if (milestoneResults.length > 0) {
        console.log(`[Milestone] User ${user.tag} achieved ${milestoneResults.length} milestone(s) from reaction activity`);
      }
    } catch (error) {
      console.error('[Milestone] Error processing reaction milestones:', error);
    }
  }

  /**
   * Get reaction handler statistics
   */
  getStats(): any {
    return {
      handlerType: 'LegacyReactionHandler',
      clientReady: this.client.isReady(),
      guildCount: this.client.guilds.cache.size,
    };
  }
}

export default LegacyReactionHandler;
