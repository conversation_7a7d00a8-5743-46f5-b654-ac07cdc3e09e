{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/utils/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,2BAA2B,EAAE,iBAAiB,EAAqC,MAAM,YAAY,CAAC;AA0B/G,qBAAa,YAAa,SAAQ,KAAK;IACnC,SAAgB,SAAS,EAAE,OAAO,CAAC;IACnC,SAAgB,KAAK,CAAC,EAAE,MAAM,CAAC;IAC/B,SAAgB,QAAQ,EAAE,MAAM,CAAC;IACjC,SAAgB,YAAY,CAAC,EAAE,MAAM,CAAC;gBAE1B,OAAO,EAAE,MAAM,EAAE,SAAS,UAAO,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,SAAkB,EAAE,YAAY,CAAC,EAAE,MAAM;CAQnH;AAED,qBAAa,aAAc,SAAQ,YAAY;gBAC/B,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,KAAK,GAAG,OAAO;CAgC/D;AAED,qBAAa,eAAgB,SAAQ,YAAY;gBACjC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,MAAM;CASvE;AAED,qBAAa,eAAgB,SAAQ,YAAY;gBACjC,OAAO,GAAE,MAA0D,EAAE,kBAAkB,CAAC,EAAE,MAAM;CAQ/G;AAED,qBAAa,sBAAuB,SAAQ,YAAY;gBACxC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;CAOlD;AAED,qBAAa,cAAe,SAAQ,YAAY;gBAChC,UAAU,EAAE,MAAM;CAOjC;AAGD,qBAAa,YAAa,SAAQ,sBAAsB;gBACxC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM;CAUrE;AA0LD,wBAAsB,kBAAkB,CAAC,WAAW,EAAE,2BAA2B,EAAE,KAAK,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAkChH;AAED,wBAAsB,iBAAiB,CAAC,WAAW,EAAE,iBAAiB,EAAE,KAAK,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAkCrG;AAED,wBAAgB,gBAAgB,CAC5B,SAAS,EAAE,CAAC,WAAW,EAAE,2BAA2B,KAAK,OAAO,CAAC,IAAI,CAAC,GACvE,CAAC,WAAW,EAAE,2BAA2B,KAAK,OAAO,CAAC,IAAI,CAAC,CAQ7D"}