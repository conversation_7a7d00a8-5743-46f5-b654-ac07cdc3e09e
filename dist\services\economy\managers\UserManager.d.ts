/**
 * User Manager
 * Handles user creation and management operations
 */
import { ILogger } from '../../../core/interfaces';
/**
 * User management operations
 */
export declare class UserManager {
    private logger;
    constructor(logger: ILogger);
    /**
     * Ensure user exists in database
     */
    ensureUser(discordId: string): Promise<any>;
    /**
     * Get user by Discord ID
     */
    getUser(discordId: string): Promise<any | null>;
    /**
     * Create new user
     */
    createUser(discordId: string, initialBalance?: number): Promise<any>;
    /**
     * Update user data
     */
    updateUser(discordId: string, updateData: any): Promise<any>;
    /**
     * Delete user
     */
    deleteUser(discordId: string): Promise<boolean>;
    /**
     * Validate and sanitize Discord ID
     */
    private validateAndSanitizeDiscordId;
    /**
     * Log operation
     */
    private logOperation;
    /**
     * Handle errors
     */
    private handleError;
}
//# sourceMappingURL=UserManager.d.ts.map