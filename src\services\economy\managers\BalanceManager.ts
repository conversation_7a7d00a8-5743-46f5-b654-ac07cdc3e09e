/**
 * Balance Manager
 * Handles balance adjustments and transaction processing
 */

import mongoose from 'mongoose';
import { Client } from 'discord.js';
import { TransactionType, ILogger } from '../../../core/interfaces';
import { DatabaseError } from '../../../utils/errorHandler';
import { requireFeature } from '../../../config/features';
import User from '../../../models/User';
import Transaction from '../../../models/Transaction';

/**
 * Balance management operations
 */
export class BalanceManager {
  private logger: ILogger;

  constructor(logger: ILogger) {
    this.logger = logger;
  }

  /**
   * Adjust user balance with transaction logging
   */
  @requireFeature('ECONOMY_SYSTEM')
  async adjustBalance(
    discordId: string,
    amount: number,
    type: TransactionType,
    details?: string,
    client?: Client,
    guildId?: string,
    dynastyId?: string
  ): Promise<void> {
    if (mongoose.connection.readyState !== 1) {
      throw new DatabaseError('Database is not connected. Please try again in a moment.');
    }
    const session = await mongoose.startSession();
    this.logOperation('Starting balance adjustment transaction', { discordId, amount, type, details });

    try {
      await session.withTransaction(async () => {
        // Validate and sanitize discordId
        const trimmedDiscordId = discordId.trim();
        if (!trimmedDiscordId) {
          throw new Error('Discord ID cannot be empty after trimming');
        }

        // Atomic update or insert
        const user = await User.findOneAndUpdate(
          { discordId: trimmedDiscordId },
          {
            $inc: { balance: amount },
            $setOnInsert: { discordId: trimmedDiscordId }
          },
          {
            new: true,
            upsert: true,
            runValidators: true,
            session
          }
        );

        this.logOperation('Creating transaction record', {
          discordId: trimmedDiscordId,
          type,
          amount,
          details,
          dynastyId
        });

        // Create transaction record in same transaction
        await Transaction.create([{
          discordId: trimmedDiscordId,
          type,
          amount,
          details,
          dynastyId,
          timestamp: new Date()
        }], { session });

        this.logOperation('Transaction complete', { 
          userId: user?._id, 
          newBalance: user?.balance 
        });

        // Check for role achievements if balance increased and we have client/guild info
        if (amount > 0 && client && guildId && user) {
          // Schedule role checking after transaction completes
          setImmediate(async () => {
            try {
              await this.checkRoleAchievements(client, trimmedDiscordId, guildId, user.balance);
            } catch (error) {
              this.handleError(error, { operation: 'role_achievement_check' });
            }
          });
        }
      });
    } catch (error) {
      this.handleError(error, { discordId, amount, type });
      throw new DatabaseError(`Failed to adjust balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      await session.endSession();
    }
  }

  /**
   * Get user balance
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getBalance(discordId: string): Promise<number> {
    try {
      const user = await this.ensureUser(discordId);
      return user.balance;
    } catch (error) {
      this.handleError(error, { discordId });
      throw new DatabaseError(`Failed to get balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Ensure user exists in database
   */
  private async ensureUser(discordId: string): Promise<any> {
    try {
      const trimmedDiscordId = discordId.trim();
      if (!trimmedDiscordId) {
        throw new Error('Discord ID cannot be empty');
      }

      const user = await User.findOneAndUpdate(
        { discordId: trimmedDiscordId },
        { $setOnInsert: { discordId: trimmedDiscordId, balance: 0 } },
        { new: true, upsert: true, runValidators: true }
      );

      return user;
    } catch (error) {
      this.handleError(error, { discordId });
      throw new DatabaseError(`Failed to ensure user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check and assign role achievements
   */
  private async checkRoleAchievements(client: Client, discordId: string, guildId: string, balance: number): Promise<void> {
    try {
      // Import here to avoid circular dependencies
      const { checkAndAssignRoles, sendRoleAchievementNotifications } = await import('../../role/RoleService');

      const roleResult = await checkAndAssignRoles(client, discordId, guildId, balance);
      if (roleResult) {
        await sendRoleAchievementNotifications(roleResult, client);
      }
    } catch (error) {
      this.handleError(error, { operation: 'role_achievement_check', discordId, guildId, balance });
    }
  }

  /**
   * Log operation
   */
  private logOperation(operation: string, details?: any): void {
    this.logger.debug(`[BalanceManager] ${operation}`, details);
  }

  /**
   * Handle errors
   */
  private handleError(error: any, context?: any): void {
    this.logger.error('[BalanceManager] Error', {
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
      context,
    });
  }
}
