
// Ensure .env variables are loaded before anything else
import 'dotenv/config';
/**
 * Main Application Entry Point
 * Refactored main entry point using the new modular architecture
 *
 * This entry point now includes automated build and deployment processes
 * for self-contained deployment on platforms like Discloud.
 */

import { startApplication } from './core/application';
import { EventManager } from './events';
import { EconomyService } from './services/economy/EconomyService';
import { RoleService } from './services/role/RoleService';
import { TradeService } from './services/trade/TradeService';
import { DisputeService } from './services/trade/DisputeService';
import { TradeBackgroundService } from './services/trade/TradeBackgroundService';
import { getLogger } from './core/logger';
// TEMPORARILY DISABLED - Auto-deployment imports
// Uncomment when ready to re-enable auto-deployment system
/*
import {
  isProductionEnvironment,
  needsBuild,
  runBuild,
  deployCommands,
  deployRoleCommands,
  validateEnvironment,
  checkDependencies
} from './utils/auto-deployment';
*/
import fs from 'fs';
import path from 'path';

/**
 * Main application startup function
 */
async function main(): Promise<void> {
  // Prevent the bot from doing anything for the first 10 seconds after startup
  // This helps ensure all services (especially DB) are ready before handling commands/events
  const startupLogger = getLogger();
  startupLogger.info('[Main] Waiting 10 seconds before allowing any bot actions (startup delay)...');
  await new Promise(resolve => setTimeout(resolve, 10000));
  startupLogger.info('[Main] Startup delay complete. Continuing with initialization.');

  // Print all environment variables for debugging (especially on Discloud)
  // Remove this in production!
  // eslint-disable-next-line no-console
  console.log('[DEBUG] process.env:', process.env);

  // Check for required environment variables
  const requiredEnv = ['BOT_TOKEN', 'MONGODB_URI'];
  const missingEnv = requiredEnv.filter((key) => !process.env[key]);
  if (missingEnv.length > 0) {
    // eslint-disable-next-line no-console
    console.error('[ENV][Missing] The following required environment variables are missing:', missingEnv);
    throw new Error(`Missing required environment variables: ${missingEnv.join(', ')}`);
  }

  // Auto-deployment process for self-contained hosting
  // TEMPORARILY DISABLED - Uncomment when ready to re-enable auto-deployment
  // await runAutoDeploymentProcess();

  const logger = getLogger();

  try {
    logger.info('[Main] Starting Economy Bot with refactored architecture...');

    // Initialize the application
    const app = await startApplication();

    // Register additional services
    await registerServices(app);

    // Load and register commands
    await loadCommands(app);

    // Initialize event handlers
    const eventManager = new EventManager(app);
    eventManager.initialize();

    logger.info('[Main] Economy Bot started successfully!');
    logger.info(`[Main] Bot is ready as ${app.client.user?.tag}`);

  } catch (error) {
    // Print any startup error directly to the console for visibility
    // eslint-disable-next-line no-console
    console.error('[MAIN][Raw Error]', error);
    logger.error('[Main] Failed to start application', { error });
    process.exit(1);
  }
}

/**
 * Run automated build and deployment processes
 * This ensures the bot is self-contained and can deploy without manual intervention
 *
 * TEMPORARILY DISABLED - Uncomment when ready to re-enable auto-deployment
 */
/*
async function runAutoDeploymentProcess(): Promise<void> {
  const logger = getLogger();

  try {
    logger.info('[Main] Starting auto-deployment process...');

    // Validate environment variables
    const envValidation = validateEnvironment();
    if (!envValidation.valid) {
      logger.error('[Main] Environment validation failed', { missing: envValidation.missing });
      throw new Error(`Missing required environment variables: ${envValidation.missing.join(', ')}`);
    }
    logger.info('[Main] Environment variables validated successfully');

    // Check dependencies
    const depCheck = await checkDependencies();
    if (!depCheck.valid) {
      logger.error('[Main] Dependency check failed', { issues: depCheck.issues });

      // In production, fail fast on dependency issues
      if (isProductionEnvironment()) {
        throw new Error(`Dependency issues: ${depCheck.issues.join(', ')}`);
      } else {
        logger.warn('[Main] Continuing despite dependency issues (development mode)');
      }
    } else {
      logger.info('[Main] Dependencies validated successfully');
    }

    // Check if we're in a production environment that needs auto-deployment
    const isProduction = isProductionEnvironment();
    logger.info(`[Main] Production environment detected: ${isProduction}`);

    // Always check if build is needed, regardless of environment
    const buildNeeded = await needsBuild();
    logger.info(`[Main] Build needed: ${buildNeeded}`);

    if (buildNeeded) {
      logger.info('[Main] Running TypeScript build process...');
      const buildSuccess = await runBuild();

      if (!buildSuccess) {
        const errorMsg = 'Build process failed - cannot continue startup';
        logger.error(`[Main] ${errorMsg}`);

        // In production, fail fast on build failures
        if (isProduction) {
          throw new Error(errorMsg);
        } else {
          logger.warn('[Main] Continuing despite build failure (development mode)');
        }
      } else {
        logger.info('[Main] Build process completed successfully');
      }
    } else {
      logger.info('[Main] Build not needed, using existing compiled files');
    }

    // Deploy commands if environment variables are available
    if (process.env.CLIENT_ID && process.env.BOT_TOKEN) {
      logger.info('[Main] Deploying slash commands...');

      const commandsDeployed = await deployCommands();
      if (!commandsDeployed) {
        const warningMsg = 'Command deployment failed';
        logger.warn(`[Main] ${warningMsg}, but continuing startup...`);

        // In production, we might want to fail on command deployment failure
        // but for now, we'll continue to allow the bot to start
        if (isProduction) {
          logger.warn('[Main] Command deployment failed in production - bot may not function correctly');
        }
      } else {
        logger.info('[Main] Slash commands deployed successfully');
      }

      // Deploy role commands
      logger.info('[Main] Deploying role commands...');
      const roleCommandsDeployed = await deployRoleCommands();
      if (!roleCommandsDeployed) {
        const warningMsg = 'Role command deployment failed';
        logger.warn(`[Main] ${warningMsg}, but continuing startup...`);

        if (isProduction) {
          logger.warn('[Main] Role command deployment failed in production - some features may not work');
        }
      } else {
        logger.info('[Main] Role commands deployed successfully');
      }
    } else {
      logger.warn('[Main] Skipping command deployment - CLIENT_ID or BOT_TOKEN not available');
    }

    logger.info('[Main] Auto-deployment process completed successfully');
  } catch (error) {
    logger.error('[Main] Auto-deployment process failed', {
      error: error.message,
      stack: error.stack,
      isProduction: isProductionEnvironment()
    });

    // In production, we want to fail fast if critical deployment fails
    if (isProductionEnvironment()) {
      // eslint-disable-next-line no-console
      console.error('[MAIN][CRITICAL] Auto-deployment failed in production:', error);
      throw error;
    } else {
      // In development, we can continue even if deployment fails
      logger.warn('[Main] Continuing startup despite deployment failure (development mode)');
      // eslint-disable-next-line no-console
      console.warn('[MAIN][DEV] Auto-deployment failed, but continuing in development mode:', error.message);
    }
  }
}
*/

/**
 * Register additional services
 */
async function registerServices(app: any): Promise<void> {
  const logger = getLogger();
  
  try {
    // Register economy service
    const economyService = new EconomyService(app);
    app.registerService(economyService, { 
      autoStart: true, 
      dependencies: ['DatabaseService'],
      priority: 2 
    });

    // Register role service
    const roleService = new RoleService(app);
    app.registerService(roleService, {
      autoStart: true,
      dependencies: ['DatabaseService', 'EconomyService'],
      priority: 3
    });

    // Register trade service
    const tradeService = new TradeService(app);
    app.registerService(tradeService, {
      autoStart: true,
      dependencies: ['DatabaseService', 'EconomyService'],
      priority: 4
    });

    // Register dispute service
    const disputeService = new DisputeService(app);
    app.registerService(disputeService, {
      autoStart: true,
      dependencies: ['DatabaseService', 'EconomyService', 'TradeService'],
      priority: 5
    });

    // Register trade background service
    const tradeBackgroundService = new TradeBackgroundService(app);
    app.registerService(tradeBackgroundService, {
      autoStart: true,
      dependencies: ['DatabaseService', 'EconomyService', 'TradeService'],
      priority: 6
    });

    logger.info('[Main] Additional services registered');
  } catch (error) {
    logger.error('[Main] Failed to register services', { error });
    throw error;
  }
}

/**
 * Load and register commands
 */
async function loadCommands(app: any): Promise<void> {
  const logger = getLogger();

  try {
    // Initialize commands collection if it doesn't exist
    if (!(app.client as any).commands) {
      (app.client as any).commands = new Map();
    }

    let loadedCommands = 0;

    // Load legacy commands (individual files)
    const commandsPath = path.join(__dirname, 'commands');
    const commandFiles = fs.readdirSync(commandsPath).filter(file =>
      (file.endsWith('.js') || file.endsWith('.ts')) &&
      !file.endsWith('.d.ts') &&
      !file.includes('index') &&
      !file.includes('Manager') &&
      !file.includes('Base')
    );

    // Skip files that are handled by new architecture
    // NOTE: Only skip commands that are confirmed to be working in new architecture
    const skipFiles = new Set([
      'enhancerole.js', 'enhancerole.ts',
      'updatenames.js', 'updatenames.ts'
      // Temporarily removed balance, pay, give to ensure legacy versions work
      // Will re-add once new architecture versions are confirmed working
    ]);

    for (const file of commandFiles) {
      if (skipFiles.has(file)) {
        logger.debug(`[Main] Skipping ${file} (handled by new architecture)`);
        continue;
      }

      try {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);

        if (command.data && command.execute) {
          (app.client as any).commands.set(command.data.name, command);
          loadedCommands++;
          logger.debug(`[Main] Loaded legacy command: ${command.data.name}`);
        } else {
          logger.warn(`[Main] Invalid command file: ${file}`);
        }
      } catch (error) {
        logger.error(`[Main] Failed to load command file: ${file}`, { error });
      }
    }

    // Load new architecture commands
    try {
      const { commandManager } = require('./commands/CommandManager');

      // Inject application context for dependency injection
      commandManager.setApplicationContext(app);

      const stats = await commandManager.loadCommands();

      const newCommands = commandManager.getDiscordCommands();
      for (const [name, command] of newCommands) {
        // Check if legacy command already exists
        if ((app.client as any).commands.has(name)) {
          logger.warn(`[Main] Skipping new architecture command '${name}' - legacy version already loaded`);
          continue;
        }

        (app.client as any).commands.set(name, command);
        loadedCommands++;
        logger.debug(`[Main] Loaded new architecture command: ${name}`);
      }

      logger.info(`[Main] CommandManager loaded ${stats.newArchitecture} new architecture commands`);
    } catch (error) {
      logger.error('[Main] Failed to load new architecture commands', { error });
    }

    logger.info(`[Main] Loaded ${loadedCommands} commands`);
  } catch (error) {
    logger.error('[Main] Failed to load commands', { error });
    throw error;
  }
}

/**
 * Handle uncaught exceptions and rejections
 */
process.on('uncaughtException', (error) => {
  const logger = getLogger();
  logger.error('[Main] Uncaught exception', { error });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  const logger = getLogger();
  logger.error('[Main] Unhandled rejection', { reason, promise });
});

// Start the application
if (require.main === module) {
  main().catch((error) => {
    // Print any error directly to the console for maximum visibility
    // eslint-disable-next-line no-console
    console.error('[MAIN][Startup Error]', error);
    if (error && error.stack) {
      // eslint-disable-next-line no-console
      console.error('[MAIN][Startup Error Stack]', error.stack);
    }
    process.exit(1);
  });
}

export default main;
