#!/usr/bin/env ts-node

/**
 * Migration CLI Tool
 * Command-line interface for running database migrations
 */

import mongoose from 'mongoose';
import { Command } from 'commander';
import { MigrationRunner } from '../migrations/MigrationRunner';
import { createLogger } from '../src/core/logger';
import { config } from '../src/config';

const logger = createLogger('migration-cli');

/**
 * Connect to database
 */
async function connectDatabase(): Promise<mongoose.Connection> {
  try {
    const mongoUri = process.env.MONGODB_URI || config.database.uri;
    
    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is required');
    }

    await mongoose.connect(mongoUri);
    logger.info('Connected to database');
    
    return mongoose.connection;
  } catch (error) {
    logger.error('Failed to connect to database', { error });
    process.exit(1);
  }
}

/**
 * Disconnect from database
 */
async function disconnectDatabase(): Promise<void> {
  try {
    await mongoose.disconnect();
    logger.info('Disconnected from database');
  } catch (error) {
    logger.error('Error disconnecting from database', { error });
  }
}

/**
 * Format migration results for display
 */
function formatResults(results: any[]): void {
  console.log('\n📊 Migration Results:');
  console.log('═'.repeat(50));
  
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} Migration ${index + 1}: ${result.message}`);
    
    if (result.changes.length > 0) {
      console.log('   Changes:');
      result.changes.forEach((change: string) => {
        console.log(`   • ${change}`);
      });
    }
    
    if (result.errors.length > 0) {
      console.log('   Errors:');
      result.errors.forEach((error: string) => {
        console.log(`   ❌ ${error}`);
      });
    }
    
    console.log('');
  });
}

/**
 * Main CLI program
 */
async function main() {
  const program = new Command();
  
  program
    .name('migrate')
    .description('Database migration tool for the trade system')
    .version('1.0.0');

  // Status command
  program
    .command('status')
    .description('Show migration status')
    .action(async () => {
      const db = await connectDatabase();
      const runner = new MigrationRunner(db, logger);
      
      try {
        await runner.initialize();
        const status = await runner.getStatus();
        
        console.log('\n📋 Migration Status:');
        console.log('═'.repeat(50));
        console.log(`Total migrations: ${status.total}`);
        console.log(`Executed: ${status.executed.length}`);
        console.log(`Pending: ${status.pending.length}`);
        
        if (status.executed.length > 0) {
          console.log('\n✅ Executed Migrations:');
          status.executed.forEach(migration => {
            console.log(`   ${migration.migrationId}: ${migration.name} (${migration.executedAt.toISOString()})`);
          });
        }
        
        if (status.pending.length > 0) {
          console.log('\n⏳ Pending Migrations:');
          status.pending.forEach(migration => {
            console.log(`   ${migration.id}: ${migration.name}`);
          });
        }
        
      } catch (error) {
        logger.error('Failed to get migration status', { error });
        process.exit(1);
      } finally {
        await disconnectDatabase();
      }
    });

  // Up command
  program
    .command('up')
    .description('Run pending migrations')
    .option('-d, --dry-run', 'Show what would be done without executing')
    .option('-t, --target <migration>', 'Run migrations up to specific target')
    .action(async (options) => {
      const db = await connectDatabase();
      const runner = new MigrationRunner(db, logger);
      
      try {
        await runner.initialize();
        
        if (options.dryRun) {
          console.log('🔍 DRY RUN MODE - No changes will be made\n');
        }
        
        const results = await runner.migrate({
          dryRun: options.dryRun,
          target: options.target
        });
        
        if (results.length === 0) {
          console.log('✅ No migrations to run');
        } else {
          formatResults(results);
          
          const successful = results.filter(r => r.success).length;
          const failed = results.filter(r => !r.success).length;
          
          if (failed > 0) {
            console.log(`❌ ${failed} migration(s) failed`);
            process.exit(1);
          } else {
            console.log(`✅ ${successful} migration(s) completed successfully`);
          }
        }
        
      } catch (error) {
        logger.error('Migration failed', { error });
        process.exit(1);
      } finally {
        await disconnectDatabase();
      }
    });

  // Down command
  program
    .command('down')
    .description('Rollback migrations')
    .option('-d, --dry-run', 'Show what would be done without executing')
    .option('-t, --target <migration>', 'Rollback to specific target migration')
    .action(async (options) => {
      const db = await connectDatabase();
      const runner = new MigrationRunner(db, logger);
      
      try {
        await runner.initialize();
        
        if (options.dryRun) {
          console.log('🔍 DRY RUN MODE - No changes will be made\n');
        }
        
        const results = await runner.rollback({
          dryRun: options.dryRun,
          target: options.target
        });
        
        if (results.length === 0) {
          console.log('✅ No migrations to rollback');
        } else {
          formatResults(results);
          
          const successful = results.filter(r => r.success).length;
          const failed = results.filter(r => !r.success).length;
          
          if (failed > 0) {
            console.log(`❌ ${failed} rollback(s) failed`);
            process.exit(1);
          } else {
            console.log(`✅ ${successful} rollback(s) completed successfully`);
          }
        }
        
      } catch (error) {
        logger.error('Rollback failed', { error });
        process.exit(1);
      } finally {
        await disconnectDatabase();
      }
    });

  // Create command
  program
    .command('create <name>')
    .description('Create a new migration file')
    .action(async (name) => {
      const fs = require('fs').promises;
      const path = require('path');
      
      try {
        // Get next migration number
        const migrationsDir = path.join(__dirname, '../migrations');
        const files = await fs.readdir(migrationsDir);
        const migrationFiles = files.filter((f: string) => f.match(/^\d{3}_.*\.ts$/));
        
        const nextNumber = migrationFiles.length > 0 
          ? String(Math.max(...migrationFiles.map((f: string) => parseInt(f.substring(0, 3)))) + 1).padStart(3, '0')
          : '002';
        
        const fileName = `${nextNumber}_${name.toLowerCase().replace(/\s+/g, '_')}.ts`;
        const filePath = path.join(migrationsDir, fileName);
        
        const template = `/**
 * Migration ${nextNumber}: ${name}
 * TODO: Add description
 */

import mongoose from 'mongoose';
import { MigrationContext, MigrationResult } from './MigrationRunner';

export class ${name.replace(/\s+/g, '')}Migration {
  private context: MigrationContext;

  constructor(context: MigrationContext) {
    this.context = context;
  }

  /**
   * Execute the migration
   */
  async up(): Promise<MigrationResult> {
    const { db, logger, dryRun = false } = this.context;
    const changes: string[] = [];
    const errors: string[] = [];

    try {
      logger.info('[Migration ${nextNumber}] Starting ${name.toLowerCase()}...');

      // TODO: Implement migration logic here

      if (!dryRun) {
        // TODO: Add actual migration code
        changes.push('TODO: Add migration changes');
      } else {
        changes.push('DRY RUN: Would execute ${name.toLowerCase()}');
      }

      logger.info(\`[Migration ${nextNumber}] ${name} completed. Changes: \${changes.length}\`);

      return {
        success: true,
        message: '${name} completed successfully',
        changes,
        errors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);
      logger.error('[Migration ${nextNumber}] Failed to execute ${name.toLowerCase()}', { error });

      return {
        success: false,
        message: \`Migration failed: \${errorMessage}\`,
        changes,
        errors
      };
    }
  }

  /**
   * Rollback the migration
   */
  async down(): Promise<MigrationResult> {
    const { db, logger, dryRun = false } = this.context;
    const changes: string[] = [];
    const errors: string[] = [];

    try {
      logger.info('[Migration ${nextNumber}] Starting ${name.toLowerCase()} rollback...');

      // TODO: Implement rollback logic here

      if (!dryRun) {
        // TODO: Add actual rollback code
        changes.push('TODO: Add rollback changes');
      } else {
        changes.push('DRY RUN: Would rollback ${name.toLowerCase()}');
      }

      logger.info(\`[Migration ${nextNumber}] ${name} rollback completed. Changes: \${changes.length}\`);

      return {
        success: true,
        message: '${name} rollback completed successfully',
        changes,
        errors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);
      logger.error('[Migration ${nextNumber}] Failed to rollback ${name.toLowerCase()}', { error });

      return {
        success: false,
        message: \`Rollback failed: \${errorMessage}\`,
        changes,
        errors
      };
    }
  }
}
`;

        await fs.writeFile(filePath, template);
        console.log(`✅ Created migration: ${fileName}`);
        console.log(`📝 Edit the file at: ${filePath}`);
        
      } catch (error) {
        logger.error('Failed to create migration', { error });
        process.exit(1);
      }
    });

  await program.parseAsync(process.argv);
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', { error });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection', { reason, promise });
  process.exit(1);
});

// Run the CLI
main().catch((error) => {
  logger.error('CLI failed', { error });
  process.exit(1);
});
