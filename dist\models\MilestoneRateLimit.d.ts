import { Document } from 'mongoose';
export interface IMilestoneRateLimit extends Document {
    discordId: string;
    guildId: string;
    milestoneType: string;
    lastAchievement: Date;
    cooldownUntil: Date;
    dailyCount: number;
    weeklyCount: number;
    lastDailyReset: Date;
    lastWeeklyReset: Date;
    suspiciousActivityCount: number;
    lastSuspiciousActivity: Date;
    isBlacklisted: boolean;
    blacklistReason?: string;
    blacklistUntil?: Date;
    createdAt: Date;
    updatedAt: Date;
}
declare const _default: import("mongoose").Model<IMilestoneRateLimit, {}, {}, {}, Document<unknown, {}, IMilestoneRateLimit, {}> & IMilestoneRateLimit & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=MilestoneRateLimit.d.ts.map