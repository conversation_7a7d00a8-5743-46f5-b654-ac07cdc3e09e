/**
 * Ready Event Handler
 * Handles Discord client ready event and initializes scheduled tasks
 */
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';
/**
 * Ready event handler
 */
export declare class ReadyEventHandler extends BaseEventHandler {
    readonly name = "ready";
    readonly once = true;
    constructor(app: IApplicationContext);
    /**
     * Execute ready event
     */
    execute(): Promise<void>;
    /**
     * Initialize scheduled cron jobs
     */
    private initializeScheduledTasks;
    /**
     * Initialize tax collection cron job
     */
    private initializeTaxCollection;
    /**
     * Initialize milestone tracking cron job
     */
    private initializeMilestoneTracking;
    /**
     * Initialize user cleanup cron job
     */
    private initializeUserCleanup;
    /**
     * Initialize audit cleanup cron job
     */
    private initializeAuditCleanup;
}
//# sourceMappingURL=ready.d.ts.map