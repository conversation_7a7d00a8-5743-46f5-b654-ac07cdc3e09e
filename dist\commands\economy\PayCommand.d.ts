/**
 * Pay Command
 * Refactored pay command using the new command architecture
 */
import { SlashCommandBuilder } from 'discord.js';
import { BaseCommand } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
/**
 * Pay command implementation
 */
export declare class PayCommand extends BaseCommand {
    constructor();
    /**
     * Customize the command builder
     */
    protected customizeCommand(command: SlashCommandBuilder): void;
    /**
     * Execute the pay command
     */
    protected executeCommand(context: CommandContext): Promise<void>;
    /**
     * Validate payment parameters
     */
    private validatePayment;
}
//# sourceMappingURL=PayCommand.d.ts.map