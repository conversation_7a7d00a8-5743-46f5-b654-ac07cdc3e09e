"use strict";
/**
 * Centralized Logging System
 * Winston-based logger with structured logging and multiple transports
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loggers = exports.CategoryLogger = exports.Logger = void 0;
exports.getLogger = getLogger;
exports.createLogger = createLogger;
exports.shutdownLogger = shutdownLogger;
const winston_1 = __importDefault(require("winston"));
const config_1 = require("../config");
const constants_1 = require("../config/constants");
/**
 * Custom log format for development
 */
const developmentFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.colorize(), winston_1.default.format.printf(({ timestamp, level, message, category, ...meta }) => {
    const categoryStr = category ? `[${category}]` : '';
    const metaStr = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
    return `${timestamp} ${level} ${categoryStr} ${message}${metaStr}`;
}));
/**
 * Custom log format for production
 */
const productionFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
/**
 * Logger implementation using Winston
 */
class Logger {
    constructor(config) {
        this.config = config || (0, config_1.getLoggingConfig)();
        this.winston = this.createLogger();
    }
    /**
     * Create Winston logger instance
     */
    createLogger() {
        const transports = [];
        // Always add Console transport to avoid no transports error
        transports.push(new winston_1.default.transports.Console({
            level: this.config.level || 'info',
            format: (0, config_1.isDevelopment)() ? developmentFormat : productionFormat,
        }));
        // File transport (optional)
        if (this.config.file && this.config.filePath) {
            transports.push(new winston_1.default.transports.File({
                filename: this.config.filePath,
                level: this.config.level,
                format: productionFormat,
                maxsize: 10 * 1024 * 1024, // 10MB
                maxFiles: 5,
                tailable: true,
            }));
        }
        return winston_1.default.createLogger({
            level: this.config.level,
            format: this.config.format === 'json' ? productionFormat : developmentFormat,
            transports,
            exitOnError: false,
        });
    }
    /**
     * Log error message
     */
    error(message, meta) {
        // Always print errors to the console for visibility, even if Winston swallows them
        if (meta && (meta instanceof Error || meta?.error || meta?.stack || meta?.name || meta?.code || meta?.reason || meta?.errorObject)) {
            // Print the raw error/meta to the console
            // eslint-disable-next-line no-console
            console.error('[Logger][Raw Error]', message, meta);
        }
        this.winston.error(message, this.formatMeta(meta));
    }
    /**
     * Log warning message
     */
    warn(message, meta) {
        this.winston.warn(message, this.formatMeta(meta));
    }
    /**
     * Log info message
     */
    info(message, meta) {
        this.winston.info(message, this.formatMeta(meta));
    }
    /**
     * Log debug message
     */
    debug(message, meta) {
        this.winston.debug(message, this.formatMeta(meta));
    }
    /**
     * Log with specific category
     */
    logWithCategory(level, category, message, meta) {
        this.winston.log(level, message, { ...this.formatMeta(meta), category });
    }
    /**
     * Format metadata for logging
     */
    formatMeta(meta) {
        if (!meta)
            return {};
        let errorSource = null;
        if (meta instanceof Error) {
            errorSource = meta;
        }
        else if (meta && meta.error instanceof Error) {
            errorSource = meta.error;
        }
        if (errorSource) {
            const formattedError = {
                name: errorSource.name,
                message: errorSource.message,
                stack: errorSource.stack,
                // Mongoose connection errors often have a `reason` object.
                // We'll convert it to a string to avoid circular serialization issues.
                ...(errorSource.reason && { reason: errorSource.reason.toString() }),
            };
            // If the original meta was an object containing the error, merge the formatted error back in.
            if (meta && meta.error instanceof Error) {
                return { ...meta, error: formattedError };
            }
            // Otherwise, the meta was the error itself, so just return the formatted version.
            return formattedError;
        }
        // Fallback for non-error objects and handling potential circular references
        try {
            JSON.stringify(meta);
            return meta;
        }
        catch (error) {
            return { serializedMeta: String(meta) };
        }
    }
    /**
     * Create child logger with category
     */
    child(category) {
        return new CategoryLogger(this, category);
    }
    /**
     * Get Winston logger instance (for advanced usage)
     */
    getWinstonLogger() {
        return this.winston;
    }
    /**
     * Update log level
     */
    setLevel(level) {
        this.winston.level = level;
        this.winston.transports.forEach(transport => {
            transport.level = level;
        });
    }
    /**
     * Close logger and cleanup resources
     */
    close() {
        this.winston.close();
    }
}
exports.Logger = Logger;
/**
 * Category-specific logger
 */
class CategoryLogger {
    constructor(parent, category) {
        this.parent = parent;
        this.category = category;
    }
    error(message, meta) {
        this.parent.logWithCategory('error', this.category, message, meta);
    }
    warn(message, meta) {
        this.parent.logWithCategory('warn', this.category, message, meta);
    }
    info(message, meta) {
        this.parent.logWithCategory('info', this.category, message, meta);
    }
    debug(message, meta) {
        this.parent.logWithCategory('debug', this.category, message, meta);
    }
}
exports.CategoryLogger = CategoryLogger;
/**
 * Global logger instance
 */
let globalLogger = null;
/**
 * Get or create global logger
 */
function getLogger() {
    if (!globalLogger) {
        globalLogger = new Logger();
    }
    return globalLogger;
}
/**
 * Create logger with category
 */
function createLogger(category) {
    return getLogger().child(category);
}
/**
 * Logger factory for different categories
 */
exports.loggers = {
    economy: () => createLogger(constants_1.LOGGING.CATEGORIES.ECONOMY),
    milestone: () => createLogger(constants_1.LOGGING.CATEGORIES.MILESTONE),
    dynasty: () => createLogger(constants_1.LOGGING.CATEGORIES.DYNASTY),
    admin: () => createLogger(constants_1.LOGGING.CATEGORIES.ADMIN),
    database: () => createLogger(constants_1.LOGGING.CATEGORIES.DATABASE),
    discord: () => createLogger(constants_1.LOGGING.CATEGORIES.DISCORD),
};
/**
 * Shutdown logger
 */
function shutdownLogger() {
    if (globalLogger) {
        globalLogger.close();
        globalLogger = null;
    }
}
exports.default = Logger;
//# sourceMappingURL=logger.js.map