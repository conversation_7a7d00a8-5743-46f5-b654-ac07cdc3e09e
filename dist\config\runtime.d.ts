/**
 * Runtime Configuration Management
 * Dynamic feature toggle and configuration management
 */
import { EventEmitter } from 'events';
import { FeatureConfig } from './features';
/**
 * Runtime configuration change event
 */
export interface ConfigChangeEvent {
    type: 'feature_toggle' | 'config_update';
    feature?: string;
    oldValue?: any;
    newValue?: any;
    timestamp: Date;
    source: string;
}
/**
 * Runtime feature override
 */
export interface FeatureOverride {
    enabled: boolean;
    reason: string;
    timestamp: Date;
    expiresAt?: Date;
    source: string;
}
/**
 * Runtime configuration manager
 */
export declare class RuntimeConfigManager extends EventEmitter {
    private logger;
    private featureOverrides;
    private configOverrides;
    constructor();
    /**
     * Override a feature's enabled state
     */
    setFeatureOverride(featureName: string, enabled: boolean, reason: string, source?: string, expiresAt?: Date): void;
    /**
     * Remove feature override
     */
    removeFeatureOverride(featureName: string, source?: string): boolean;
    /**
     * Check if a feature is enabled (considering overrides)
     */
    isFeatureEnabled(featureName: string): boolean;
    /**
     * Get feature configuration with runtime overrides
     */
    getFeatureConfig(featureName: string): FeatureConfig & {
        override?: FeatureOverride;
    };
    /**
     * Get all feature overrides
     */
    getFeatureOverrides(): Map<string, FeatureOverride>;
    /**
     * Set configuration override
     */
    setConfigOverride(key: string, value: any, source?: string): void;
    /**
     * Get configuration value with overrides
     */
    getConfigValue(key: string, defaultValue?: any): any;
    /**
     * Remove configuration override
     */
    removeConfigOverride(key: string, source?: string): boolean;
    /**
     * Get runtime statistics
     */
    getStats(): any;
    /**
     * Export current configuration
     */
    exportConfig(): any;
    /**
     * Import configuration
     */
    importConfig(config: any, source?: string): void;
    /**
     * Reset all overrides
     */
    resetAll(source?: string): void;
    /**
     * Clean up expired overrides
     */
    private cleanupExpiredOverrides;
    /**
     * Schedule automatic cleanup
     */
    startCleanupSchedule(intervalMs?: number): void;
}
/**
 * Global runtime config manager instance
 */
export declare const runtimeConfig: RuntimeConfigManager;
/**
 * Enhanced feature checking with runtime overrides
 */
export declare function isFeatureActiveRuntime(featureName: string): boolean;
/**
 * Feature toggle decorator with runtime support
 */
export declare function requireFeatureRuntime(featureName: string): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export default runtimeConfig;
//# sourceMappingURL=runtime.d.ts.map