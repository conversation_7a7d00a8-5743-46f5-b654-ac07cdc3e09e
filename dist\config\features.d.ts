/**
 * Feature Configuration System
 * Centralized feature toggle management with runtime configuration
 */
/**
 * Feature configuration interface
 */
export interface FeatureConfig {
    enabled: boolean;
    description: string;
    dependencies?: string[];
    adminOnly?: boolean;
    guildSpecific?: boolean;
}
/**
 * Feature registry with metadata
 */
export declare const FEATURE_REGISTRY: Record<string, FeatureConfig>;
/**
 * Global feature manager instance
 */
declare class FeatureManager {
    private enabledFeatures;
    constructor();
    /**
     * Check if a feature is enabled
     */
    isEnabled(featureName: string): boolean;
    /**
     * Get all enabled features
     */
    getEnabledFeatures(): string[];
    /**
     * Get feature configuration
     */
    getFeatureConfig(featureName: string): FeatureConfig | undefined;
    /**
     * Check if feature requires admin permissions
     */
    requiresAdmin(featureName: string): boolean;
    /**
     * Check if feature is guild-specific
     */
    isGuildSpecific(featureName: string): boolean;
    /**
     * Get features by category
     */
    getFeaturesByCategory(category: 'core' | 'admin' | 'advanced'): string[];
}
/**
 * Global feature manager instance
 */
export declare const featureManager: FeatureManager;
/**
 * Helper functions for feature checking
 */
export declare const isFeatureActive: (featureName: string) => boolean;
export declare const requiresAdminPermission: (featureName: string) => boolean;
export declare const isGuildSpecificFeature: (featureName: string) => boolean;
/**
 * Feature guard decorator for commands and services
 */
export declare function requireFeature(featureName: string): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export default featureManager;
//# sourceMappingURL=features.d.ts.map