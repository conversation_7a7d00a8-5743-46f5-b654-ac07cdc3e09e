# Trade System Deployment Guide

This guide provides comprehensive instructions for safely deploying the secure trade system to production.

## 📋 Prerequisites

### System Requirements
- Node.js 18+ 
- MongoDB 6.0+
- Discord Bot Token with appropriate permissions
- Sufficient server resources (minimum 2GB RAM recommended)

### Required Permissions
The Discord bot requires the following permissions:
- `Send Messages`
- `Use Slash Commands`
- `Embed Links`
- `Read Message History`
- `Add Reactions`
- `Manage Messages` (for button interactions)

### Environment Variables
```bash
# Required
MONGODB_URI=mongodb://localhost:27017/your-database
DISCORD_TOKEN=your-discord-bot-token
NODE_ENV=production

# Optional
LOG_LEVEL=info
TRADE_SYSTEM_ENABLED=true
```

## 🧪 Pre-Deployment Testing

### 1. Install Dependencies
```bash
npm install
```

### 2. Run Unit Tests
```bash
# Run all tests
npm test

# Run trade-specific tests
npm run test:trade

# Run with coverage
npm run test:coverage
```

### 3. Test Migration Scripts
```bash
# Check migration status
npm run migrate:status

# Dry run migrations
npm run migrate up --dry-run

# Test rollback (dry run)
npm run migrate down --dry-run
```

## 🚀 Deployment Process

### Step 1: Validate Environment
```bash
# Validate deployment prerequisites
npm run deploy:validate -e production
```

This checks:
- ✅ Database connectivity
- ✅ Required environment variables
- ✅ Collection compatibility
- ✅ Migration status
- ✅ Production-specific settings

### Step 2: Backup Database (Production Only)
```bash
# Create backup before deployment
mongodump --uri="$MONGODB_URI" --out="backup-$(date +%Y%m%d-%H%M%S)"
```

### Step 3: Deploy to Staging (Recommended)
```bash
# Deploy to staging environment first
npm run deploy:staging

# Test functionality in staging
# Run integration tests
# Verify all features work correctly
```

### Step 4: Deploy to Production
```bash
# Deploy to production
npm run deploy:prod

# Or with dry run first
npm run deploy deploy -e production --dry-run
npm run deploy deploy -e production
```

### Step 5: Verify Deployment
After deployment, verify:
- ✅ Bot starts successfully
- ✅ Database connections work
- ✅ Trade commands are available
- ✅ All collections and indexes exist
- ✅ No error logs

## 🔄 Rollback Procedure

If issues occur after deployment:

```bash
# Rollback migrations
npm run deploy:rollback -e production

# Or rollback to specific migration
npm run migrate down --target 000
```

## 📊 Monitoring

### Health Checks
Monitor these endpoints/logs:
- Database connection status
- Trade system background tasks
- Error rates in logs
- Memory and CPU usage

### Key Metrics
- Active trades count
- Trade completion rate
- Dispute rate
- System response times

### Log Monitoring
Watch for these log patterns:
```bash
# Error patterns to monitor
grep -i "error\|failed\|exception" /path/to/logs

# Trade system specific
grep "TradeService\|EscrowManager\|DisputeService" /path/to/logs
```

## 🛠 Troubleshooting

### Common Issues

#### 1. Migration Failures
```bash
# Check migration status
npm run migrate:status

# View detailed logs
npm run migrate up --verbose

# Force migration (use with caution)
npm run migrate up --force
```

#### 2. Database Connection Issues
- Verify MongoDB is running
- Check connection string format
- Ensure network connectivity
- Verify authentication credentials

#### 3. Missing Collections
```bash
# Re-run migrations
npm run migrate up

# Check collection creation
mongo $MONGODB_URI --eval "db.adminCommand('listCollections')"
```

#### 4. Permission Errors
- Verify Discord bot permissions
- Check role hierarchy
- Ensure bot has necessary channel access

### Emergency Procedures

#### Complete Rollback
```bash
# 1. Stop the application
pm2 stop discord-bot

# 2. Rollback database
npm run deploy:rollback -e production

# 3. Restore from backup if needed
mongorestore --uri="$MONGODB_URI" --drop backup-folder/

# 4. Restart with previous version
git checkout previous-stable-tag
npm install
npm run build
pm2 start
```

## 📈 Performance Optimization

### Database Optimization
```javascript
// Recommended MongoDB settings
db.adminCommand({
  setParameter: 1,
  internalQueryMaxBlockingSortMemoryUsageBytes: 335544320
});

// Index optimization
db.trades.createIndex({ "expiresAt": 1 }, { expireAfterSeconds: 0 });
```

### Application Optimization
- Enable connection pooling
- Configure appropriate timeouts
- Set up proper logging levels
- Monitor memory usage

## 🔒 Security Considerations

### Production Security
- Use environment variables for secrets
- Enable MongoDB authentication
- Set up proper firewall rules
- Regular security updates
- Monitor for suspicious activity

### Trade System Security
- Rate limiting is automatically enforced
- Escrow transactions are atomic
- All operations are logged
- User restrictions are automatically applied

## 📝 Post-Deployment Checklist

### Immediate (0-1 hour)
- [ ] Bot is online and responding
- [ ] Database connections stable
- [ ] No critical errors in logs
- [ ] Basic commands work (`/trade help`)
- [ ] Admin commands accessible

### Short-term (1-24 hours)
- [ ] Background tasks running (check logs)
- [ ] Trade creation works
- [ ] Escrow system functional
- [ ] Notifications being sent
- [ ] No memory leaks

### Long-term (1-7 days)
- [ ] Trade completion flow works
- [ ] Dispute system functional
- [ ] User statistics updating
- [ ] Performance metrics stable
- [ ] No data corruption

## 🆘 Support Contacts

### Emergency Contacts
- **Database Issues**: [DBA Contact]
- **Application Issues**: [Dev Team Lead]
- **Infrastructure**: [DevOps Team]

### Escalation Procedure
1. Check logs and metrics
2. Attempt standard troubleshooting
3. Contact on-call engineer
4. Escalate to team lead if needed
5. Consider rollback if critical

## 📚 Additional Resources

- [Trade System Architecture](./ARCHITECTURE.md)
- [API Documentation](./API.md)
- [Database Schema](./DATABASE_SCHEMA.md)
- [Testing Guide](./TESTING_GUIDE.md)

---

**⚠️ Important**: Always test in staging environment before production deployment. Keep backups and have a rollback plan ready.
