/**
 * Runtime Configuration Management
 * Dynamic feature toggle and configuration management
 */

import { EventEmitter } from 'events';
import { ILogger } from '../core/interfaces';
import { createLogger } from '../core/logger';
import { FEATURE_REGISTRY, FeatureConfig } from './features';

/**
 * Runtime configuration change event
 */
export interface ConfigChangeEvent {
  type: 'feature_toggle' | 'config_update';
  feature?: string;
  oldValue?: any;
  newValue?: any;
  timestamp: Date;
  source: string;
}

/**
 * Runtime feature override
 */
export interface FeatureOverride {
  enabled: boolean;
  reason: string;
  timestamp: Date;
  expiresAt?: Date;
  source: string;
}

/**
 * Runtime configuration manager
 */
export class RuntimeConfigManager extends EventEmitter {
  private logger: ILogger;
  private featureOverrides = new Map<string, FeatureOverride>();
  private configOverrides = new Map<string, any>();

  constructor() {
    super();
    this.logger = createLogger('runtime-config');
  }

  /**
   * Override a feature's enabled state
   */
  setFeatureOverride(
    featureName: string, 
    enabled: boolean, 
    reason: string, 
    source: string = 'manual',
    expiresAt?: Date
  ): void {
    const oldOverride = this.featureOverrides.get(featureName);
    const oldValue = oldOverride?.enabled;

    const override: FeatureOverride = {
      enabled,
      reason,
      timestamp: new Date(),
      expiresAt,
      source,
    };

    this.featureOverrides.set(featureName, override);

    this.logger.info(`Feature override set: ${featureName} = ${enabled}`, {
      featureName,
      enabled,
      reason,
      source,
      expiresAt,
    });

    // Emit change event
    this.emit('configChange', {
      type: 'feature_toggle',
      feature: featureName,
      oldValue,
      newValue: enabled,
      timestamp: new Date(),
      source,
    } as ConfigChangeEvent);
  }

  /**
   * Remove feature override
   */
  removeFeatureOverride(featureName: string, source: string = 'manual'): boolean {
    const override = this.featureOverrides.get(featureName);
    if (!override) {
      return false;
    }

    this.featureOverrides.delete(featureName);

    this.logger.info(`Feature override removed: ${featureName}`, {
      featureName,
      source,
      previousOverride: override,
    });

    // Emit change event
    this.emit('configChange', {
      type: 'feature_toggle',
      feature: featureName,
      oldValue: override.enabled,
      newValue: undefined,
      timestamp: new Date(),
      source,
    } as ConfigChangeEvent);

    return true;
  }

  /**
   * Check if a feature is enabled (considering overrides)
   */
  isFeatureEnabled(featureName: string): boolean {
    // Check for expired overrides
    this.cleanupExpiredOverrides();

    // Check for runtime override first
    const override = this.featureOverrides.get(featureName);
    if (override) {
      return override.enabled;
    }

    // Fall back to static configuration
    const config = FEATURE_REGISTRY[featureName];
    return config?.enabled || false;
  }

  /**
   * Get feature configuration with runtime overrides
   */
  getFeatureConfig(featureName: string): FeatureConfig & { override?: FeatureOverride } {
    const baseConfig = FEATURE_REGISTRY[featureName];
    const override = this.featureOverrides.get(featureName);

    if (!baseConfig) {
      throw new Error(`Feature not found: ${featureName}`);
    }

    const config = { ...baseConfig };
    
    if (override) {
      config.enabled = override.enabled;
      return { ...config, override };
    }

    return config;
  }

  /**
   * Get all feature overrides
   */
  getFeatureOverrides(): Map<string, FeatureOverride> {
    this.cleanupExpiredOverrides();
    return new Map(this.featureOverrides);
  }

  /**
   * Set configuration override
   */
  setConfigOverride(key: string, value: any, source: string = 'manual'): void {
    const oldValue = this.configOverrides.get(key);
    this.configOverrides.set(key, value);

    this.logger.info(`Configuration override set: ${key}`, {
      key,
      oldValue,
      newValue: value,
      source,
    });

    // Emit change event
    this.emit('configChange', {
      type: 'config_update',
      feature: key,
      oldValue,
      newValue: value,
      timestamp: new Date(),
      source,
    } as ConfigChangeEvent);
  }

  /**
   * Get configuration value with overrides
   */
  getConfigValue(key: string, defaultValue?: any): any {
    return this.configOverrides.get(key) ?? defaultValue;
  }

  /**
   * Remove configuration override
   */
  removeConfigOverride(key: string, source: string = 'manual'): boolean {
    const oldValue = this.configOverrides.get(key);
    const removed = this.configOverrides.delete(key);

    if (removed) {
      this.logger.info(`Configuration override removed: ${key}`, {
        key,
        oldValue,
        source,
      });

      // Emit change event
      this.emit('configChange', {
        type: 'config_update',
        feature: key,
        oldValue,
        newValue: undefined,
        timestamp: new Date(),
        source,
      } as ConfigChangeEvent);
    }

    return removed;
  }

  /**
   * Get runtime statistics
   */
  getStats(): any {
    this.cleanupExpiredOverrides();

    return {
      featureOverrides: this.featureOverrides.size,
      configOverrides: this.configOverrides.size,
      totalFeatures: Object.keys(FEATURE_REGISTRY).length,
      enabledFeatures: Object.entries(FEATURE_REGISTRY)
        .filter(([name]) => this.isFeatureEnabled(name))
        .length,
      overriddenFeatures: Array.from(this.featureOverrides.keys()),
    };
  }

  /**
   * Export current configuration
   */
  exportConfig(): any {
    return {
      timestamp: new Date().toISOString(),
      featureOverrides: Object.fromEntries(
        Array.from(this.featureOverrides.entries()).map(([key, override]) => [
          key,
          {
            enabled: override.enabled,
            reason: override.reason,
            timestamp: override.timestamp.toISOString(),
            expiresAt: override.expiresAt?.toISOString(),
            source: override.source,
          },
        ])
      ),
      configOverrides: Object.fromEntries(this.configOverrides),
    };
  }

  /**
   * Import configuration
   */
  importConfig(config: any, source: string = 'import'): void {
    // Import feature overrides
    if (config.featureOverrides) {
      for (const [featureName, overrideDataRaw] of Object.entries(config.featureOverrides as any)) {
        const overrideData = overrideDataRaw as Partial<FeatureOverride>;
        this.setFeatureOverride(
          featureName,
          overrideData.enabled ?? false,
          overrideData.reason || 'Imported configuration',
          source,
          overrideData.expiresAt ? new Date(overrideData.expiresAt) : undefined
        );
      }
    }

    // Import config overrides
    if (config.configOverrides) {
      for (const [key, value] of Object.entries(config.configOverrides)) {
        this.setConfigOverride(key, value, source);
      }
    }

    this.logger.info('Configuration imported', {
      featureOverrides: Object.keys(config.featureOverrides || {}).length,
      configOverrides: Object.keys(config.configOverrides || {}).length,
      source,
    });
  }

  /**
   * Reset all overrides
   */
  resetAll(source: string = 'manual'): void {
    const featureCount = this.featureOverrides.size;
    const configCount = this.configOverrides.size;

    this.featureOverrides.clear();
    this.configOverrides.clear();

    this.logger.info('All configuration overrides reset', {
      featureOverrides: featureCount,
      configOverrides: configCount,
      source,
    });

    // Emit reset event
    this.emit('configChange', {
      type: 'config_update',
      oldValue: { featureCount, configCount },
      newValue: { featureCount: 0, configCount: 0 },
      timestamp: new Date(),
      source,
    } as ConfigChangeEvent);
  }

  /**
   * Clean up expired overrides
   */
  private cleanupExpiredOverrides(): void {
    const now = new Date();
    const expiredFeatures: string[] = [];

    for (const [featureName, override] of this.featureOverrides.entries()) {
      if (override.expiresAt && override.expiresAt <= now) {
        expiredFeatures.push(featureName);
      }
    }

    for (const featureName of expiredFeatures) {
      this.removeFeatureOverride(featureName, 'auto-expire');
    }
  }

  /**
   * Schedule automatic cleanup
   */
  startCleanupSchedule(intervalMs: number = 60000): void {
    setInterval(() => {
      this.cleanupExpiredOverrides();
    }, intervalMs);

    this.logger.info('Cleanup schedule started', { intervalMs });
  }
}

/**
 * Global runtime config manager instance
 */
export const runtimeConfig = new RuntimeConfigManager();

/**
 * Enhanced feature checking with runtime overrides
 */
export function isFeatureActiveRuntime(featureName: string): boolean {
  return runtimeConfig.isFeatureEnabled(featureName);
}

/**
 * Feature toggle decorator with runtime support
 */
export function requireFeatureRuntime(featureName: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      if (!isFeatureActiveRuntime(featureName)) {
        throw new Error(`Feature ${featureName} is not enabled`);
      }
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
}

export default runtimeConfig;
