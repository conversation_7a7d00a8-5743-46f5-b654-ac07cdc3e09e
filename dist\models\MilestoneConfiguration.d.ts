import { Document } from 'mongoose';
export interface IMilestoneConfiguration extends Document {
    guildId: string;
    category: 'time_based' | 'participation_diversity' | 'loyalty' | 'engagement';
    milestoneType: string;
    enabled: boolean;
    rewardAmount: number;
    maxRewardsPerWeek: number;
    maxRewardsPerDay: number;
    diminishingReturns: boolean;
    diminishingFactor: number;
    requirements: {
        threshold?: number;
        minMessageLength?: number;
        minVoiceTime?: number;
        cooldownHours?: number;
    };
    createdAt: Date;
    updatedAt: Date;
}
declare const _default: import("mongoose").Model<IMilestoneConfiguration, {}, {}, {}, Document<unknown, {}, IMilestoneConfiguration, {}> & IMilestoneConfiguration & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=MilestoneConfiguration.d.ts.map