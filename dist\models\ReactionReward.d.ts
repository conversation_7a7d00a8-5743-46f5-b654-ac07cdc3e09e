import { Document } from 'mongoose';
export interface IReactionReward extends Document {
    userId: string;
    messageId: string;
    channelId: string;
    coinsAwarded: number;
    timestamp: Date;
}
export declare const ReactionReward: import("mongoose").Model<IReactionReward, {}, {}, {}, Document<unknown, {}, IReactionReward, {}> & IReactionReward & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=ReactionReward.d.ts.map