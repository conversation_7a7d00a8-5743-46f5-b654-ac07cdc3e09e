"use strict";
/**
 * Economy Module Index
 * Exports for the economy service module
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ensureUser = exports.getTransactionHistory = exports.getLeaderboard = exports.adjustBalance = exports.EconomyService = void 0;
var EconomyService_1 = require("./EconomyService");
Object.defineProperty(exports, "EconomyService", { enumerable: true, get: function () { return EconomyService_1.EconomyService; } });
// Re-export legacy functions for backward compatibility
var economyService_1 = require("../economyService");
Object.defineProperty(exports, "adjustBalance", { enumerable: true, get: function () { return economyService_1.adjustBalance; } });
Object.defineProperty(exports, "getLeaderboard", { enumerable: true, get: function () { return economyService_1.getLeaderboard; } });
Object.defineProperty(exports, "getTransactionHistory", { enumerable: true, get: function () { return economyService_1.getTransactionHistory; } });
Object.defineProperty(exports, "ensureUser", { enumerable: true, get: function () { return economyService_1.ensureUser; } });
//# sourceMappingURL=index.js.map