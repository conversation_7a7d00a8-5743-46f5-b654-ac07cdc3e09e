"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const userActivitySchema = new mongoose_1.Schema({
    discordId: {
        type: String,
        required: [true, 'Discord ID is required'],
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true
    },
    // Login/Activity Tracking
    lastSeen: {
        type: Date,
        default: Date.now,
        index: true
    },
    loginStreak: {
        type: Number,
        default: 0,
        min: [0, 'Login streak cannot be negative']
    },
    longestLoginStreak: {
        type: Number,
        default: 0,
        min: [0, 'Longest login streak cannot be negative']
    },
    lastLoginDate: {
        type: Date,
        default: Date.now
    },
    serverJoinDate: {
        type: Date,
        default: Date.now
    },
    totalDaysActive: {
        type: Number,
        default: 0,
        min: [0, 'Total days active cannot be negative']
    },
    // Message Activity
    dailyMessageCount: {
        type: Number,
        default: 0,
        min: [0, 'Daily message count cannot be negative']
    },
    weeklyMessageCount: {
        type: Number,
        default: 0,
        min: [0, 'Weekly message count cannot be negative']
    },
    totalMessages: {
        type: Number,
        default: 0,
        min: [0, 'Total messages cannot be negative']
    },
    lastMessageDate: {
        type: Date
    },
    uniqueChannelsToday: [{
            type: String
        }],
    uniqueChannelsThisWeek: [{
            type: String
        }],
    // Voice Activity
    dailyVoiceMinutes: {
        type: Number,
        default: 0,
        min: [0, 'Daily voice minutes cannot be negative']
    },
    weeklyVoiceMinutes: {
        type: Number,
        default: 0,
        min: [0, 'Weekly voice minutes cannot be negative']
    },
    totalVoiceMinutes: {
        type: Number,
        default: 0,
        min: [0, 'Total voice minutes cannot be negative']
    },
    lastVoiceDate: {
        type: Date
    },
    uniqueVoiceChannelsToday: [{
            type: String
        }],
    uniqueVoiceChannelsThisWeek: [{
            type: String
        }],
    // Reaction Activity
    dailyReactionCount: {
        type: Number,
        default: 0,
        min: [0, 'Daily reaction count cannot be negative']
    },
    weeklyReactionCount: {
        type: Number,
        default: 0,
        min: [0, 'Weekly reaction count cannot be negative']
    },
    totalReactions: {
        type: Number,
        default: 0,
        min: [0, 'Total reactions cannot be negative']
    },
    lastReactionDate: {
        type: Date
    },
    uniqueReactionTypesToday: [{
            type: String
        }],
    uniqueReactionTypesThisWeek: [{
            type: String
        }],
    // Reset tracking
    lastDailyReset: {
        type: Date,
        default: Date.now
    },
    lastWeeklyReset: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});
// Compound indexes for efficient queries
userActivitySchema.index({ discordId: 1, guildId: 1 }, { unique: true });
userActivitySchema.index({ guildId: 1, loginStreak: -1 });
userActivitySchema.index({ guildId: 1, lastSeen: -1 });
userActivitySchema.index({ guildId: 1, totalDaysActive: -1 });
userActivitySchema.index({ lastDailyReset: 1 });
userActivitySchema.index({ lastWeeklyReset: 1 });
exports.default = (0, mongoose_1.model)('UserActivity', userActivitySchema);
//# sourceMappingURL=UserActivity.js.map