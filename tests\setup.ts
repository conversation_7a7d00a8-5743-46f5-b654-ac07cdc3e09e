/**
 * Jest Test Setup
 * Global test configuration and utilities
 */

import { jest } from '@jest/globals';

// Increase timeout for database operations
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
const originalConsole = global.console;

beforeAll(() => {
  global.console = {
    ...originalConsole,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  } as any;
});

afterAll(() => {
  global.console = originalConsole;
});

// Global test utilities
global.testUtils = {
  /**
   * Wait for a specified amount of time
   */
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * Generate a random Discord ID
   */
  generateDiscordId: () => Math.floor(Math.random() * 9000000000000000000) + 1000000000000000000,

  /**
   * Generate a random trade ID
   */
  generateTradeId: () => `test_trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,

  /**
   * Create a mock Discord user
   */
  createMockUser: (id?: string) => ({
    id: id || global.testUtils.generateDiscordId().toString(),
    username: 'testuser',
    discriminator: '0001',
    displayName: 'Test User',
    bot: false
  }),

  /**
   * Create a mock Discord guild
   */
  createMockGuild: (id?: string) => ({
    id: id || global.testUtils.generateDiscordId().toString(),
    name: 'Test Guild',
    memberCount: 100
  }),

  /**
   * Create a mock Discord client
   */
  createMockClient: () => ({
    user: global.testUtils.createMockUser(),
    guilds: {
      fetch: jest.fn().mockResolvedValue(global.testUtils.createMockGuild())
    },
    users: {
      fetch: jest.fn().mockImplementation((id: string) => 
        Promise.resolve(global.testUtils.createMockUser(id))
      )
    }
  })
};

// Declare global types
declare global {
  var testUtils: {
    wait: (ms: number) => Promise<void>;
    generateDiscordId: () => number;
    generateTradeId: () => string;
    createMockUser: (id?: string) => any;
    createMockGuild: (id?: string) => any;
    createMockClient: () => any;
  };
}
