/**
 * Economy Service
 * Refactored economy service with improved architecture
 */
import { Client } from 'discord.js';
import { BaseService } from '../base/BaseService';
import { IEconomyService, IApplicationContext, TransactionType, LeaderboardEntry, TransactionRecord } from '../../core/interfaces';
/**
 * Economy service implementation
 */
export declare class EconomyService extends BaseService implements IEconomyService {
    readonly name = "EconomyService";
    constructor(app: IApplicationContext);
    /**
     * Initialize the economy service
     */
    protected onInitialize(): Promise<void>;
    /**
     * Adjust user balance with transaction logging
     */
    adjustBalance(discordId: string, amount: number, type: TransactionType, details?: string, client?: Client, guildId?: string, dynastyId?: string): Promise<void>;
    /**
     * Get user balance
     */
    getBalance(discordId: string): Promise<number>;
    /**
     * Get leaderboard
     */
    getLeaderboard(guildId?: string, limit?: number): Promise<LeaderboardEntry[]>;
    /**
     * Get transaction history
     */
    getTransactionHistory(discordId: string, limit?: number): Promise<TransactionRecord[]>;
    /**
     * Ensure user exists in database
     */
    ensureUser(discordId: string): Promise<any>;
    /**
     * Validate adjust balance input
     */
    private validateAdjustBalanceInput;
    /**
     * Check and assign role achievements
     */
    private checkRoleAchievements;
}
//# sourceMappingURL=EconomyService.d.ts.map