"use strict";
/**
 * Database Connection Manager
 * Centralized database connection and management
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const config_1 = require("../config");
const constants_1 = require("../config/constants");
/**
 * Database service implementation
 */
class DatabaseService {
    constructor(logger, config) {
        this.name = 'DatabaseService';
        this.connectionPromise = null;
        this.logger = logger;
        this.config = config || (0, config_1.getDatabaseConfig)();
        // Set mongoose options
        mongoose_1.default.set('strictQuery', false);
        // Setup connection event handlers
        this.setupEventHandlers();
    }
    /**
     * Initialize the database service
     */
    async initialize() {
        this.logger.info('[Database] Initializing database service');
        await this.connect();
        await this.setupIndexes();
        await this.performCleanup();
    }
    /**
     * Connect to the database
     */
    async connect() {
        if (this.connectionPromise) {
            return this.connectionPromise;
        }
        this.connectionPromise = this._connect();
        return this.connectionPromise;
    }
    /**
     * Internal connection method
     */
    async _connect() {
        try {
            this.logger.info('[Database] Connecting to MongoDB...');
            this.logger.debug('[Database] Connection URI:', this.config.uri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'));
            this.logger.debug('[Database] Connection options:', this.config.options);
            await mongoose_1.default.connect(this.config.uri, {
                ...this.config.options,
                serverSelectionTimeoutMS: constants_1.DATABASE.CONNECTION_TIMEOUT_MS,
            });
            this.logger.info('[Database] Successfully connected to MongoDB');
        }
        catch (error) {
            // Print full error details for debugging
            console.error('RAW MONGOOSE ERROR:', error);
            this.logger.error('[Database] Failed to connect to MongoDB', {
                error: error?.message,
                stack: error?.stack,
                name: error?.name,
                code: error?.code,
                reason: error?.reason,
                errorObject: error,
                configUri: this.config.uri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'),
                configOptions: this.config.options
            });
            this.connectionPromise = null;
            throw error;
        }
    }
    /**
     * Disconnect from the database
     */
    async disconnect() {
        try {
            this.logger.info('[Database] Disconnecting from MongoDB...');
            await mongoose_1.default.disconnect();
            this.connectionPromise = null;
            this.logger.info('[Database] Disconnected from MongoDB');
        }
        catch (error) {
            this.logger.error('[Database] Error disconnecting from MongoDB', { error });
            throw error;
        }
    }
    /**
     * Check if connected to database
     */
    isConnected() {
        return mongoose_1.default.connection.readyState === 1;
    }
    /**
     * Get connection status string
     */
    getConnectionStatus() {
        const states = ['disconnected', 'connected', 'connecting', 'disconnecting'];
        return states[mongoose_1.default.connection.readyState] || 'unknown';
    }
    /**
     * Shutdown the database service
     */
    async shutdown() {
        this.logger.info('[Database] Shutting down database service');
        await this.disconnect();
    }
    /**
     * Setup database event handlers
     */
    setupEventHandlers() {
        mongoose_1.default.connection.on('connected', () => {
            this.logger.info('[Database] Mongoose connected to MongoDB');
        });
        mongoose_1.default.connection.on('error', (error) => {
            this.logger.error('[Database] Mongoose connection error', {
                error: error?.message,
                stack: error?.stack,
                name: error?.name,
                code: error?.code,
                reason: error?.reason,
                errorObject: error
            });
        });
        mongoose_1.default.connection.on('disconnected', () => {
            this.logger.warn('[Database] Mongoose disconnected from MongoDB');
        });
        // Handle application termination
        process.on('SIGINT', async () => {
            await this.shutdown();
            process.exit(0);
        });
    }
    /**
     * Setup required database indexes
     */
    async setupIndexes() {
        try {
            this.logger.info('[Database] Setting up indexes...');
            const db = mongoose_1.default.connection.db;
            if (!db) {
                throw new Error('Database connection not established');
            }
            // Setup indexes from configuration
            for (const indexConfig of constants_1.DATABASE.REQUIRED_INDEXES) {
                const collection = db.collection(indexConfig.collection);
                try {
                    const indexOptions = { background: true };
                    if ('unique' in indexConfig) {
                        indexOptions.unique = indexConfig.unique;
                    }
                    await collection.createIndex(indexConfig.index, indexOptions);
                    this.logger.debug(`[Database] Created index for ${indexConfig.collection}`, {
                        index: indexConfig.index,
                        unique: 'unique' in indexConfig ? indexConfig.unique : undefined,
                    });
                }
                catch (error) {
                    // Index might already exist
                    if (error.code !== 11000) {
                        this.logger.warn(`[Database] Failed to create index for ${indexConfig.collection}`, {
                            error: error.message,
                            index: indexConfig.index,
                        });
                    }
                }
            }
            this.logger.info('[Database] Index setup completed');
        }
        catch (error) {
            this.logger.error('[Database] Failed to setup indexes', { error });
            throw error;
        }
    }
    /**
     * Perform database cleanup operations
     */
    async performCleanup() {
        try {
            this.logger.info('[Database] Performing cleanup operations...');
            const db = mongoose_1.default.connection.db;
            if (!db) {
                throw new Error('Database connection not established');
            }
            const usersCollection = db.collection('users');
            // Remove old userId index if it exists
            try {
                await usersCollection.dropIndex('userId_1');
                this.logger.info('[Database] Dropped old userId_1 index');
            }
            catch (error) {
                // Index might not exist, which is fine
                this.logger.debug('[Database] userId_1 index not found (expected)');
            }
            // Clean up corrupted records
            const deleteResult = await usersCollection.deleteMany({
                $or: [
                    { discordId: null },
                    { discordId: { $exists: false } },
                    { userId: { $exists: true } } // Remove old schema records
                ]
            });
            if (deleteResult.deletedCount > 0) {
                this.logger.info(`[Database] Cleaned up ${deleteResult.deletedCount} corrupted user records`);
            }
            this.logger.info('[Database] Cleanup operations completed');
        }
        catch (error) {
            this.logger.error('[Database] Failed to perform cleanup', { error });
            // Don't throw here as this is not critical for startup
        }
    }
    /**
     * Get database statistics
     */
    async getStats() {
        try {
            const db = mongoose_1.default.connection.db;
            if (!db) {
                throw new Error('Database connection not established');
            }
            const stats = await db.stats();
            return {
                collections: stats.collections,
                dataSize: stats.dataSize,
                indexSize: stats.indexSize,
                objects: stats.objects,
                avgObjSize: stats.avgObjSize,
            };
        }
        catch (error) {
            this.logger.error('[Database] Failed to get stats', { error });
            throw error;
        }
    }
    /**
     * Health check
     */
    async healthCheck() {
        try {
            if (!this.isConnected()) {
                return false;
            }
            // Perform a simple operation to verify connection
            const db = mongoose_1.default.connection.db;
            await db?.admin().ping();
            return true;
        }
        catch (error) {
            this.logger.error('[Database] Health check failed', { error });
            return false;
        }
    }
}
exports.DatabaseService = DatabaseService;
//# sourceMappingURL=database.js.map