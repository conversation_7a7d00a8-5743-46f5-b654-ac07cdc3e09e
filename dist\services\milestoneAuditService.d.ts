import { Document } from 'mongoose';
import { Client } from 'discord.js';
export interface IMilestoneAuditLog extends Document {
    guildId: string;
    userId?: string;
    adminId?: string;
    action: 'milestone_achieved' | 'config_created' | 'config_updated' | 'config_deleted' | 'system_enabled' | 'system_disabled' | 'suspicious_activity' | 'rate_limit_hit' | 'blacklist_added' | 'blacklist_removed';
    category: 'achievement' | 'admin' | 'system' | 'security';
    details: string;
    metadata: {
        milestoneType?: string;
        rewardAmount?: number;
        oldValue?: any;
        newValue?: any;
        ipAddress?: string;
        userAgent?: string;
        suspiciousReason?: string;
        rateLimitType?: string;
    };
    timestamp: Date;
    severity: 'low' | 'medium' | 'high' | 'critical';
}
export declare const MilestoneAuditLog: import("mongoose").Model<IMilestoneAuditLog, {}, {}, {}, Document<unknown, {}, IMilestoneAuditLog, {}> & IMilestoneAuditLog & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
/**
 * Milestone Audit Service for comprehensive logging and monitoring
 */
export declare class MilestoneAuditService {
    /**
     * Logs a milestone achievement
     */
    static logMilestoneAchievement(guildId: string, userId: string, milestoneType: string, rewardAmount: number, details: string, client?: Client): Promise<void>;
    /**
     * Logs admin configuration changes
     */
    static logAdminAction(guildId: string, adminId: string, action: 'config_created' | 'config_updated' | 'config_deleted' | 'system_enabled' | 'system_disabled', details: string, metadata?: any): Promise<void>;
    /**
     * Logs suspicious activity
     */
    static logSuspiciousActivity(guildId: string, userId: string, reason: string, details: string, metadata?: any): Promise<void>;
    /**
     * Logs rate limit hits
     */
    static logRateLimitHit(guildId: string, userId: string, rateLimitType: string, details: string): Promise<void>;
    /**
     * Logs blacklist actions
     */
    static logBlacklistAction(guildId: string, userId: string, action: 'blacklist_added' | 'blacklist_removed', reason: string, adminId?: string): Promise<void>;
    /**
     * Gets audit logs for a guild with filtering options
     */
    static getAuditLogs(guildId: string, options?: {
        category?: string;
        action?: string;
        userId?: string;
        adminId?: string;
        severity?: string;
        startDate?: Date;
        endDate?: Date;
        limit?: number;
    }): Promise<IMilestoneAuditLog[]>;
    /**
     * Gets security alerts (high and critical severity logs)
     */
    static getSecurityAlerts(guildId: string, hours?: number): Promise<IMilestoneAuditLog[]>;
    /**
     * Gets user activity summary from audit logs
     */
    static getUserActivitySummary(guildId: string, userId: string, days?: number): Promise<{
        totalAchievements: number;
        totalRewards: number;
        suspiciousActivities: number;
        rateLimitHits: number;
        recentActions: IMilestoneAuditLog[];
    }>;
    /**
     * Cleans up old audit logs (older than specified days)
     */
    static cleanupOldLogs(days?: number): Promise<number>;
}
//# sourceMappingURL=milestoneAuditService.d.ts.map