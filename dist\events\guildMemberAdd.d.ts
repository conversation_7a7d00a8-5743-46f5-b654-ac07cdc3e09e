/**
 * Guild Member Add Event Handler
 * Handles Discord guild member join events for automated messages and milestone tracking
 */
import { GuildMember } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';
/**
 * Guild member add event handler
 */
export declare class GuildMemberAddEventHandler extends BaseEventHandler {
    readonly name = "guildMemberAdd";
    constructor(app: IApplicationContext);
    /**
     * Execute guild member add event
     */
    execute(member: GuildMember): Promise<void>;
    /**
     * Process automated join messages
     */
    private processJoinMessages;
    /**
     * Track join activity for milestones
     */
    private trackJoinActivity;
}
//# sourceMappingURL=guildMemberAdd.d.ts.map