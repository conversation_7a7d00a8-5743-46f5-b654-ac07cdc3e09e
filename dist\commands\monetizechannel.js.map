{"version": 3, "file": "monetizechannel.js", "sourceRoot": "", "sources": ["../../src/commands/monetizechannel.ts"], "names": [], "mappings": ";;AAAA,2CAAmG;AACnG,iEAA8D;AAC9D,wDAA0G;AAC1G,wDAA6F;AAE7F,MAAM,CAAC,OAAO,GAAG;IACb,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC1B,OAAO,CAAC,iBAAiB,CAAC;SAC1B,cAAc,CAAC,yEAAyE,CAAC;SACzF,eAAe,CAAC,MAAM,CAAC,EAAE,CACtB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;SACnB,cAAc,CAAC,oCAAoC,CAAC;SACpD,WAAW,CAAC,IAAI,CAAC;SACjB,UAAU,CACP,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,EAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,CACpC,CACR;SACA,2BAA2B,CAAC,gCAAmB,CAAC,aAAa,CAAC;IAEnE,OAAO,EAAE,IAAA,+BAAgB,EAAC,KAAK,EAAE,WAAwC,EAAE,EAAE;QACzE,mBAAmB;QACnB,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,gCAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,8BAAe,EAAE,CAAC;QAChC,CAAC;QAED,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC7D,MAAM,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;QAExC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,MAAM,IAAI,8BAAe,CAAC,yCAAyC,CAAC,CAAC;QACzE,CAAC;QAED,wBAAwB;QACxB,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YACtC,MAAM,IAAI,8BAAe,CAAC,sCAAsC,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,KAAK,IAAI,CAAC;QAEhC,IAAI,CAAC;YACD,4CAA4C;YAC5C,MAAM,gBAAgB,GAAG,MAAM,mCAAgB,CAAC,gBAAgB,CAC5D,EAAE,SAAS,EAAE,EACb;gBACI,SAAS;gBACT,OAAO;aACV,EACD;gBACI,GAAG,EAAE,IAAI;gBACT,MAAM,EAAE,IAAI;gBACZ,aAAa,EAAE,IAAI;aACtB,CACJ,CAAC;YAEF,0BAA0B;YAC1B,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC;YACpD,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,qBAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,qBAAM,CAAC,KAAK,CAAC,OAAO,CAAC;YAC1E,MAAM,cAAc,GAAG,KAAK,SAAS,GAAG,CAAC;YAEzC,MAAM,KAAK,GAAG,IAAA,+BAAgB,EAAC,oBAAoB,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;iBACjF,cAAc,CACX,GAAG,WAAW,uCAAuC;gBACrD,gCAAgC,UAAU,UAAU,cAAc,EAAE,CACvE;iBACA,SAAS,CACN;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,MAAM,UAAU;gBACrC,KAAK,EAAE,cAAc;gBACrB,MAAM,EAAE,IAAI;aACf,EACD;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,KAAK,SAAS;gBACpC,KAAK,EAAE,OAAO,CAAC,CAAC;oBACZ,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,aAAa,CAAC,CAAC;oBACtC,GAAG,qBAAM,CAAC,KAAK,CAAC,OAAO,eAAe;gBAC1C,MAAM,EAAE,IAAI;aACf,EACD;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,GAAG,gBAAgB;gBACzC,KAAK,EAAE,KAAK,WAAW,CAAC,IAAI,CAAC,WAAW,IAAI;gBAC5C,MAAM,EAAE,IAAI;aACf,EACD;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,OAAO,CAAC,KAAK,iBAAiB;gBAC9C,KAAK,EAAE,OAAO,CAAC,CAAC;oBACZ,GAAG,qBAAM,CAAC,OAAO,CAAC,SAAS,oFAAoF;wBAC/G,GAAG,qBAAM,CAAC,OAAO,CAAC,SAAS,sCAAsC;wBACjE,GAAG,qBAAM,CAAC,IAAI,CAAC,KAAK,uCAAuC,CAAC,CAAC;oBAC7D,GAAG,qBAAM,CAAC,KAAK,CAAC,IAAI,2CAA2C;wBAC/D,GAAG,qBAAM,CAAC,IAAI,CAAC,UAAU,iCAAiC;gBAC9D,MAAM,EAAE,KAAK;aAChB,EACD;gBACI,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,KAAK,UAAU;gBACpC,KAAK,EAAE,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK;gBAC/C,MAAM,EAAE,KAAK;aAChB,CACJ;iBACA,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAM,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAM,CAAC,OAAO,CAAC;iBACnD,SAAS,CAAC;gBACP,IAAI,EAAE,wDAAwD;aACjE,CAAC,CAAC;YAEP,MAAM,WAAW,CAAC,KAAK,CAAC;gBACpB,MAAM,EAAE,CAAC,KAAK,CAAC;gBACf,SAAS,EAAE,KAAK;aACnB,CAAC,CAAC;YAEH,iBAAiB;YACjB,OAAO,CAAC,GAAG,CAAC,8BAA8B,SAAS,iBAAiB,UAAU,OAAO,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAEjH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACtB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;gBAC7D,MAAM,IAAI,8BAAe,CAAC,6BAA6B,CAAC,CAAC;YAC7D,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,IAAK,KAAa,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBACtG,MAAM,IAAI,4BAAa,CAAC,sCAAsC,CAAC,CAAC;YACpE,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAChC,MAAM,IAAI,4BAAa,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACrF,CAAC;YACD,MAAM,IAAI,4BAAa,CAAC,kDAAkD,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,CAAC;CACL,CAAC"}