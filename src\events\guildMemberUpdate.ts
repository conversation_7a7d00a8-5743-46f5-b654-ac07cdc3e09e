/**
 * Guild Member Update Event Handler
 * Handles Discord guild member update events for starter balance and role change messages
 */

import { GuildMember, PartialGuildMember } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';

/**
 * Guild member update event handler
 */
export class GuildMemberUpdateEventHandler extends BaseEventHandler {
  public readonly name = 'guildMemberUpdate';

  constructor(app: IApplicationContext) {
    super(app, 'guildMemberUpdate');
  }

  /**
   * Execute guild member update event
   */
  async execute(oldMember: GuildMember | PartialGuildMember, newMember: GuildMember): Promise<void> {
    try {
      // Handle partial members
      const fullOldMember = await this.ensureFullMember(oldMember);
      if (!fullOldMember) {
        return;
      }

      // Check for role changes
      const addedRoles = newMember.roles.cache.filter(role => !fullOldMember.roles.cache.has(role.id));
      const removedRoles = fullOldMember.roles.cache.filter(role => !newMember.roles.cache.has(role.id));

      // Process added roles
      if (addedRoles.size > 0) {
        await this.processAddedRoles(newMember, addedRoles);
      }

      // Process removed roles
      if (removedRoles.size > 0) {
        await this.processRemovedRoles(newMember, removedRoles);
      }

    } catch (error) {
      this.handleError(error, {
        userId: newMember.user.id,
        guildId: newMember.guild.id,
        displayName: newMember.displayName,
      });
    }
  }

  /**
   * Ensure member is fully fetched
   */
  private async ensureFullMember(member: GuildMember | PartialGuildMember): Promise<GuildMember | null> {
    if (member.partial) {
      try {
        return await member.fetch();
      } catch (error) {
        this.logger.error('[GuildMemberUpdate] Failed to fetch old member', { error });
        return null;
      }
    }
    return member as GuildMember;
  }

  /**
   * Process added roles
   */
  private async processAddedRoles(member: GuildMember, addedRoles: any): Promise<void> {
    for (const [roleId, role] of addedRoles) {
      try {
        this.logExecution(`Role added: ${role.name} to ${member.displayName}`, {
          userId: member.user.id,
          guildId: member.guild.id,
          roleId: role.id,
          roleName: role.name,
        });

        // Process starter balance
        if (this.isFeatureEnabled('STARTER_BALANCE')) {
          await this.processStarterBalance(member, role);
        }

        // Process role add messages
        if (this.isFeatureEnabled('AUTO_MESSAGES')) {
          await this.processRoleAddMessage(member, role);
        }

      } catch (error) {
        this.logger.error(`[GuildMemberUpdate] Failed to process role add for ${member.displayName} and role ${role.name}`, {
          error,
          userId: member.user.id,
          roleId: role.id,
        });
      }
    }
  }

  /**
   * Process removed roles
   */
  private async processRemovedRoles(member: GuildMember, removedRoles: any): Promise<void> {
    for (const [roleId, role] of removedRoles) {
      try {
        this.logExecution(`Role removed: ${role.name} from ${member.displayName}`, {
          userId: member.user.id,
          guildId: member.guild.id,
          roleId: role.id,
          roleName: role.name,
        });

        // Process role remove messages
        if (this.isFeatureEnabled('AUTO_MESSAGES')) {
          await this.processRoleRemoveMessage(member, role);
        }

      } catch (error) {
        this.logger.error(`[GuildMemberUpdate] Failed to process role remove for ${member.displayName} and role ${role.name}`, {
          error,
          userId: member.user.id,
          roleId: role.id,
        });
      }
    }
  }

  /**
   * Process starter balance for new role
   */
  private async processStarterBalance(member: GuildMember, role: any): Promise<void> {
    try {
      // Import here to avoid circular dependencies
      const { processStarterBalance } = await import('../services/starterBalanceService');

      const granted = await processStarterBalance(member, role);
      if (granted) {
        this.logger.info(`[GuildMemberUpdate] Granted starter balance to ${member.displayName} for role ${role.name}`);
      }
    } catch (error) {
      this.logger.error('[GuildMemberUpdate] Error processing starter balance', {
        error,
        userId: member.user.id,
        roleId: role.id,
      });
    }
  }

  /**
   * Process role add message
   */
  private async processRoleAddMessage(member: GuildMember, role: any): Promise<void> {
    try {
      // Import here to avoid circular dependencies
      const { processRoleChangeMessage } = await import('../services/automessageService');

      const roleAddResult = await processRoleChangeMessage(member, role, 'role_add');
      if (roleAddResult.sent) {
        this.logger.info(`[GuildMemberUpdate] Sent ${roleAddResult.templatesProcessed} role add message(s) to ${member.displayName} for role ${role.name}`);
      }
      if (roleAddResult.errors.length > 0) {
        this.logger.error(`[GuildMemberUpdate] Errors processing role add messages for ${member.displayName}`, {
          errors: roleAddResult.errors,
        });
      }
    } catch (error) {
      this.logger.error('[GuildMemberUpdate] Error processing role add message', {
        error,
        userId: member.user.id,
        roleId: role.id,
      });
    }
  }

  /**
   * Process role remove message
   */
  private async processRoleRemoveMessage(member: GuildMember, role: any): Promise<void> {
    try {
      // Import here to avoid circular dependencies
      const { processRoleChangeMessage } = await import('../services/automessageService');

      const roleRemoveResult = await processRoleChangeMessage(member, role, 'role_remove');
      if (roleRemoveResult.sent) {
        this.logger.info(`[GuildMemberUpdate] Sent ${roleRemoveResult.templatesProcessed} role remove message(s) to ${member.displayName} for role ${role.name}`);
      }
      if (roleRemoveResult.errors.length > 0) {
        this.logger.error(`[GuildMemberUpdate] Errors processing role remove messages for ${member.displayName}`, {
          errors: roleRemoveResult.errors,
        });
      }
    } catch (error) {
      this.logger.error('[GuildMemberUpdate] Error processing role remove message', {
        error,
        userId: member.user.id,
        roleId: role.id,
      });
    }
  }
}
