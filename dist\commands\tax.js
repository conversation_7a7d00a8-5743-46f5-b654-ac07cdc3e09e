"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const TaxConfiguration_1 = require("../models/TaxConfiguration");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const roleResolver_1 = require("../utils/roleResolver");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('tax')
        .setDescription('Configure the automatic taxation system (admin only)')
        .addStringOption(option => option.setName('status')
        .setDescription('Enable or disable the tax system')
        .setRequired(true)
        .addChoices({ name: 'Enable', value: 'on' }, { name: 'Disable', value: 'off' }))
        .addIntegerOption(option => option.setName('frequency')
        .setDescription('Number of weeks between tax collection (1-52)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(52))
        .addIntegerOption(option => option.setName('amount')
        .setDescription('Amount of PLC to deduct per tax period (1-10000)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(10000))
        .addStringOption(option => option.setName('role')
        .setDescription('Discord role that will be subject to taxation')
        .setRequired(false))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        // Check permissions
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError('You need Administrator permissions to use this command.');
        }
        const status = interaction.options.getString('status', true);
        const frequency = interaction.options.getInteger('frequency');
        const amount = interaction.options.getInteger('amount');
        const roleInput = interaction.options.getString('role');
        try {
            if (status === 'off') {
                // Disable tax system
                await TaxConfiguration_1.TaxConfiguration.findOneAndUpdate({ guildId: interaction.guild.id }, {
                    enabled: false,
                    nextTaxDate: undefined
                }, { upsert: true, new: true });
                const embed = (0, embedBuilder_1.createSuccessEmbed)('Tax System Disabled')
                    .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Tax Collection Disabled**\n\n` +
                    `The automatic taxation system has been disabled for this server.\n\n` +
                    `${embedBuilder_1.EMOJIS.ADMIN.INFO} No further tax collections will occur until re-enabled.`);
                await interaction.reply({ embeds: [embed], ephemeral: false });
                return;
            }
            // Enable tax system - validate required parameters
            if (!frequency) {
                throw new errorHandler_1.ValidationError('Frequency is required when enabling the tax system. Please specify how many weeks between tax collections (1-52).');
            }
            if (!amount) {
                throw new errorHandler_1.ValidationError('Amount is required when enabling the tax system. Please specify how many PLC to deduct per tax period (1-10000).');
            }
            if (!roleInput) {
                throw new errorHandler_1.ValidationError('Role is required when enabling the tax system. Please specify which role will be subject to taxation.');
            }
            // Resolve and validate the role
            const roleResolution = await (0, roleResolver_1.resolveRole)(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;
            // Validate role permissions
            (0, roleResolver_1.validateRolePermissions)(interaction.guild, role);
            // Create or update tax configuration
            const taxConfig = await TaxConfiguration_1.TaxConfiguration.findOneAndUpdate({ guildId: interaction.guild.id }, {
                enabled: true,
                frequency,
                amount,
                roleId: role.id,
                roleName: role.name,
                lastTaxDate: undefined // Reset last tax date when configuration changes
            }, { upsert: true, new: true, runValidators: true });
            // Calculate next tax date (frequency weeks from now)
            const nextTaxDate = new Date(Date.now() + (frequency * 7 * 24 * 60 * 60 * 1000));
            const nextTaxDateString = nextTaxDate.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZoneName: 'short'
            });
            // Create success embed
            const embed = (0, embedBuilder_1.createAdminEmbed)('Tax System Configured')
                .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.HAMMER} **Tax Collection Enabled**\n\n` +
                `The automatic taxation system has been successfully configured!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.SCALES} Tax Configuration`,
                value: `**Frequency:** Every ${frequency} week${frequency > 1 ? 's' : ''}\n` +
                    `**Amount:** ${amount} PLC per collection\n` +
                    `**Taxed Role:** ${role.name}`,
                inline: false
            }, {
                name: `${embedBuilder_1.EMOJIS.ADMIN.CLOCK} Next Collection`,
                value: nextTaxDateString,
                inline: false
            }, {
                name: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Important Notes`,
                value: `• Users who cannot afford the tax will lose **ALL** purchasable roles\n` +
                    `• Users will be notified via DM when roles are removed\n` +
                    `• Tax collection runs automatically in the background`,
                inline: false
            });
            // Add resolution info if role was resolved by fuzzy matching
            if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                embed.setFooter({ text: `Note: Resolved "${roleInput}" to "${role.name}"` });
            }
            await interaction.reply({ embeds: [embed], ephemeral: false });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError('Failed to configure tax system. Please try again.', error);
            }
            throw new errorHandler_1.DatabaseError('An unexpected error occurred while configuring the tax system.');
        }
    })
};
//# sourceMappingURL=tax.js.map