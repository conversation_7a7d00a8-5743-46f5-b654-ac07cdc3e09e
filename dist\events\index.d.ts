/**
 * Events Module Index
 * Centralized event handler registration and management
 */
import { IApplicationContext } from '../core/interfaces';
import { BaseEventHandler } from './base';
/**
 * Event handler factory
 */
export declare class EventHandlerFactory {
    /**
     * Create all event handlers
     */
    static createHandlers(app: IApplicationContext): BaseEventHandler[];
}
/**
 * Event manager for registering and managing event handlers
 */
export declare class EventManager {
    private app;
    private handlers;
    constructor(app: IApplicationContext);
    /**
     * Initialize and register all event handlers
     */
    initialize(): void;
    /**
     * Register a single event handler with Discord client
     */
    private registerHandler;
    /**
     * Unregister all event handlers
     */
    shutdown(): void;
    /**
     * Get registered handlers
     */
    getHandlers(): BaseEventHandler[];
    /**
     * Get handler by name
     */
    getHandler(name: string): BaseEventHandler | undefined;
}
export { BaseEventHandler, eventRegistry } from './base';
export { ReadyEventHandler } from './ready';
export { InteractionCreateEventHandler } from './interactionCreate';
export { MessageCreateEventHandler } from './messageCreate';
export { MessageReactionAddEventHandler } from './messageReactionAdd';
export { GuildMemberAddEventHandler } from './guildMemberAdd';
export { GuildMemberRemoveEventHandler } from './guildMemberRemove';
export { GuildMemberUpdateEventHandler } from './guildMemberUpdate';
export { VoiceStateUpdateEventHandler } from './voiceStateUpdate';
export default EventManager;
//# sourceMappingURL=index.d.ts.map