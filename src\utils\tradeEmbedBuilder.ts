/**
 * Trade Embed Builder
 * Specialized embed builders for trade system UI components
 */

import { <PERSON>bed<PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, User as DiscordUser, ColorResolvable } from 'discord.js';
import { COLORS, EMOJIS, createBaseEmbed, formatCoins } from './embedBuilder';
import { ITrade, IUserTradeStats, IDisputeCase } from '../models';
import { TRADE } from '../config/constants';

/**
 * Create a trade proposal embed
 */
export function createTradeProposalEmbed(trade: ITrade, seller: DiscordUser, buyer: DiscordUser): EmbedBuilder {
  const isSellerInitiated = trade.initiatedBy === 'SELLER';
  const initiator = isSellerInitiated ? seller : buyer;
  const recipient = isSellerInitiated ? buyer : seller;
  
  const embed = createBaseEmbed(
    `${EMOJIS.TRADE.PROPOSAL} Trade Proposal`,
    undefined,
    COLORS.WARNING
  );

  embed.addFields([
    {
      name: `${EMOJIS.ACTIONS.SENDER} Initiated By`,
      value: `${initiator.displayName} (${isSellerInitiated ? 'Seller' : 'Buyer'})`,
      inline: true
    },
    {
      name: `${EMOJIS.MISC.USER} ${isSellerInitiated ? 'Buyer' : 'Seller'}`,
      value: recipient.displayName,
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.TIMER} Status`,
      value: `${EMOJIS.TRADE.PENDING} Pending Acceptance`,
      inline: true
    },
    {
      name: `${EMOJIS.ECONOMY.COINS} Amount`,
      value: formatCoins(trade.amount),
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.PACKAGE} Item`,
      value: trade.itemDescription,
      inline: true
    },
    {
      name: `${EMOJIS.MISC.CLOCK} Expires`,
      value: `<t:${Math.floor(trade.expiresAt.getTime() / 1000)}:R>`,
      inline: true
    }
  ]);

  if (trade.notes) {
    embed.addFields([{
      name: `${EMOJIS.MISC.SCROLL} Notes`,
      value: trade.notes,
      inline: false
    }]);
  }

  embed.addFields([{
    name: `${EMOJIS.MISC.ID} Trade ID`,
    value: `\`${trade.tradeId}\``,
    inline: false
  }]);

  return embed;
}

/**
 * Create trade proposal action buttons
 */
export function createTradeProposalButtons(tradeId: string): ActionRowBuilder<ButtonBuilder> {
  return new ActionRowBuilder<ButtonBuilder>().addComponents(
    new ButtonBuilder()
      .setCustomId(`trade_accept_${tradeId}`)
      .setLabel('Accept Trade')
      .setEmoji(EMOJIS.SUCCESS.CHECK)
      .setStyle(ButtonStyle.Success),
    new ButtonBuilder()
      .setCustomId(`trade_decline_${tradeId}`)
      .setLabel('Decline Trade')
      .setEmoji('❌')
      .setStyle(ButtonStyle.Danger),
    new ButtonBuilder()
      .setCustomId(`trade_details_${tradeId}`)
      .setLabel('View Details')
      .setEmoji(EMOJIS.MISC.MAGNIFYING)
      .setStyle(ButtonStyle.Secondary)
  );
}

/**
 * Create active trade embed
 */
export function createActiveTradeEmbed(trade: ITrade, seller: DiscordUser, buyer: DiscordUser): EmbedBuilder {
  const embed = createBaseEmbed(
    `${EMOJIS.TRADE.ACTIVE} Active Trade`,
    undefined,
    COLORS.SUCCESS
  );

  embed.addFields([
    {
      name: `${EMOJIS.MISC.USER} Seller`,
      value: `${seller.displayName}${trade.sellerConfirmed ? ` ${EMOJIS.TRADE.CONFIRMATION}` : ''}`,
      inline: true
    },
    {
      name: `${EMOJIS.MISC.USER} Buyer`,
      value: `${buyer.displayName}${trade.buyerConfirmed ? ` ${EMOJIS.TRADE.CONFIRMATION}` : ''}`,
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.TIMER} Status`,
      value: `${EMOJIS.TRADE.ACTIVE} Active`,
      inline: true
    },
    {
      name: `${EMOJIS.ECONOMY.COINS} Amount`,
      value: formatCoins(trade.amount),
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.ESCROW} Escrow`,
      value: trade.escrowLocked ? `${EMOJIS.TRADE.ESCROW} Locked` : `${EMOJIS.TRADE.RELEASE} Not Locked`,
      inline: true
    },
    {
      name: `${EMOJIS.MISC.CLOCK} Expires`,
      value: `<t:${Math.floor(trade.expiresAt.getTime() / 1000)}:R>`,
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.PACKAGE} Item`,
      value: trade.itemDescription,
      inline: false
    }
  ]);

  if (trade.notes) {
    embed.addFields([{
      name: `${EMOJIS.MISC.SCROLL} Notes`,
      value: trade.notes,
      inline: false
    }]);
  }

  // Add confirmation status
  const confirmationStatus = [];
  if (trade.sellerConfirmed) {
    confirmationStatus.push(`${EMOJIS.SUCCESS.CHECK} Seller confirmed`);
  } else {
    confirmationStatus.push(`${EMOJIS.TRADE.PENDING} Seller pending`);
  }
  
  if (trade.buyerConfirmed) {
    confirmationStatus.push(`${EMOJIS.SUCCESS.CHECK} Buyer confirmed`);
  } else {
    confirmationStatus.push(`${EMOJIS.TRADE.PENDING} Buyer pending`);
  }

  embed.addFields([{
    name: `${EMOJIS.TRADE.CONFIRMATION} Confirmation Status`,
    value: confirmationStatus.join('\n'),
    inline: false
  }]);

  embed.addFields([{
    name: `${EMOJIS.MISC.ID} Trade ID`,
    value: `\`${trade.tradeId}\``,
    inline: false
  }]);

  return embed;
}

/**
 * Create active trade action buttons
 */
export function createActiveTradeButtons(tradeId: string, userConfirmed: boolean): ActionRowBuilder<ButtonBuilder> {
  const buttons = [];

  if (!userConfirmed) {
    buttons.push(
      new ButtonBuilder()
        .setCustomId(`trade_confirm_${tradeId}`)
        .setLabel('Confirm Completion')
        .setEmoji(EMOJIS.TRADE.CONFIRMATION)
        .setStyle(ButtonStyle.Success)
    );
  }

  buttons.push(
    new ButtonBuilder()
      .setCustomId(`trade_dispute_${tradeId}`)
      .setLabel('Dispute Trade')
      .setEmoji(EMOJIS.TRADE.DISPUTED)
      .setStyle(ButtonStyle.Danger),
    new ButtonBuilder()
      .setCustomId(`trade_cancel_${tradeId}`)
      .setLabel('Cancel Trade')
      .setEmoji(EMOJIS.TRADE.CANCELLED)
      .setStyle(ButtonStyle.Secondary)
  );

  return new ActionRowBuilder<ButtonBuilder>().addComponents(...buttons);
}

/**
 * Create completed trade embed
 */
export function createCompletedTradeEmbed(trade: ITrade, seller: DiscordUser, buyer: DiscordUser): EmbedBuilder {
  const embed = createBaseEmbed(
    `${EMOJIS.TRADE.COMPLETED} Trade Completed`,
    undefined,
    COLORS.SUCCESS
  );

  embed.addFields([
    {
      name: `${EMOJIS.MISC.USER} Seller`,
      value: `${seller.displayName} ${EMOJIS.TRADE.CONFIRMATION}`,
      inline: true
    },
    {
      name: `${EMOJIS.MISC.USER} Buyer`,
      value: `${buyer.displayName} ${EMOJIS.TRADE.CONFIRMATION}`,
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.TIMER} Status`,
      value: `${EMOJIS.TRADE.COMPLETED} Completed`,
      inline: true
    },
    {
      name: `${EMOJIS.ECONOMY.COINS} Amount`,
      value: formatCoins(trade.amount),
      inline: true
    },
    {
      name: `${EMOJIS.MISC.CLOCK} Completed`,
      value: trade.completedAt ? `<t:${Math.floor(trade.completedAt.getTime() / 1000)}:R>` : 'Unknown',
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.RELEASE} Funds`,
      value: `${EMOJIS.TRADE.RELEASE} Released to Seller`,
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.PACKAGE} Item`,
      value: trade.itemDescription,
      inline: false
    }
  ]);

  if (trade.notes) {
    embed.addFields([{
      name: `${EMOJIS.MISC.SCROLL} Notes`,
      value: trade.notes,
      inline: false
    }]);
  }

  embed.addFields([{
    name: `${EMOJIS.MISC.ID} Trade ID`,
    value: `\`${trade.tradeId}\``,
    inline: false
  }]);

  return embed;
}

/**
 * Create cancelled trade embed
 */
export function createCancelledTradeEmbed(trade: ITrade, seller: DiscordUser, buyer: DiscordUser, reason?: string): EmbedBuilder {
  const embed = createBaseEmbed(
    `${EMOJIS.TRADE.CANCELLED} Trade Cancelled`,
    undefined,
    COLORS.ERROR
  );

  embed.addFields([
    {
      name: `${EMOJIS.MISC.USER} Seller`,
      value: seller.displayName,
      inline: true
    },
    {
      name: `${EMOJIS.MISC.USER} Buyer`,
      value: buyer.displayName,
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.TIMER} Status`,
      value: `${EMOJIS.TRADE.CANCELLED} Cancelled`,
      inline: true
    },
    {
      name: `${EMOJIS.ECONOMY.COINS} Amount`,
      value: formatCoins(trade.amount),
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.RELEASE} Funds`,
      value: trade.escrowLocked ? `${EMOJIS.TRADE.RELEASE} Refunded to Buyer` : 'No Escrow',
      inline: true
    },
    {
      name: `${EMOJIS.MISC.CLOCK} Cancelled`,
      value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.PACKAGE} Item`,
      value: trade.itemDescription,
      inline: false
    }
  ]);

  if (reason) {
    embed.addFields([{
      name: `${EMOJIS.MISC.SCROLL} Cancellation Reason`,
      value: reason,
      inline: false
    }]);
  }

  embed.addFields([{
    name: `${EMOJIS.MISC.ID} Trade ID`,
    value: `\`${trade.tradeId}\``,
    inline: false
  }]);

  return embed;
}

/**
 * Create user trade stats embed
 */
export function createUserTradeStatsEmbed(user: DiscordUser, stats: IUserTradeStats): EmbedBuilder {
  const embed = createBaseEmbed(
    `${EMOJIS.SUCCESS.TROPHY} Trade Statistics`,
    `Statistics for ${user.displayName}`,
    COLORS.INFO
  );

  // Calculate derived stats
  const successRate = stats.totalTrades > 0 ? (stats.successfulTrades / stats.totalTrades * 100).toFixed(1) : '0';
  const disputeRate = stats.totalTrades > 0 ? (stats.disputedTrades / stats.totalTrades * 100).toFixed(1) : '0';

  embed.addFields([
    {
      name: `${EMOJIS.ECONOMY.CHART} Overview`,
      value: 
        `**Total Trades:** ${stats.totalTrades}\n` +
        `**Successful:** ${stats.successfulTrades} (${successRate}%)\n` +
        `**Active:** ${stats.activeTrades}\n` +
        `**Reputation:** ${stats.reputationScore}/100`,
      inline: true
    },
    {
      name: `${EMOJIS.ECONOMY.COINS} Financial`,
      value: 
        `**Volume Traded:** ${formatCoins(stats.totalVolumeTraded)}\n` +
        `**Average Trade:** ${formatCoins(Math.round(stats.averageTradeValue))}\n` +
        `**Largest Trade:** ${formatCoins(stats.largestTrade)}`,
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.SCALES} Performance`,
      value: 
        `**Completion Rate:** ${(stats.completionRate * 100).toFixed(1)}%\n` +
        `**Dispute Rate:** ${disputeRate}%\n` +
        `**Avg. Completion:** ${stats.averageCompletionTime.toFixed(1)}h`,
      inline: true
    },
    {
      name: `${EMOJIS.ROLES.MEDAL} Role Distribution`,
      value: 
        `**As Seller:** ${stats.tradesAsSeller}\n` +
        `**As Buyer:** ${stats.tradesAsBuyer}`,
      inline: true
    },
    {
      name: `${EMOJIS.TRADE.TIMER} Status`,
      value: 
        `**Cancelled:** ${stats.cancelledTrades}\n` +
        `**Expired:** ${stats.expiredTrades}\n` +
        `**Disputed:** ${stats.disputedTrades}`,
      inline: true
    },
    {
      name: `${EMOJIS.MISC.CLOCK} Activity`,
      value: 
        `**Daily Trades:** ${stats.dailyTradeCount}/${TRADE.MAX_TRADE_PROPOSALS_PER_DAY}\n` +
        `**Last Trade:** <t:${Math.floor(stats.lastTradeDate.getTime() / 1000)}:R>\n` +
        `**Restricted:** ${stats.isRestricted ? 'Yes' : 'No'}`,
      inline: true
    }
  ]);

  if (stats.isRestricted && stats.restrictedUntil) {
    embed.addFields([{
      name: `${EMOJIS.ADMIN.WARNING} Restriction`,
      value: 
        `**Reason:** ${stats.restrictionReason || 'Not specified'}\n` +
        `**Until:** <t:${Math.floor(stats.restrictedUntil.getTime() / 1000)}:R>`,
      inline: false
    }]);
  }

  return embed;
}

/**
 * Get trade state color
 */
export function getTradeStateColor(state: string): ColorResolvable {
  switch (state) {
    case 'PROPOSED':
      return COLORS.WARNING;
    case 'ACCEPTED':
    case 'ACTIVE':
      return COLORS.INFO;
    case 'COMPLETED':
      return COLORS.SUCCESS;
    case 'CANCELLED':
    case 'EXPIRED':
      return COLORS.ERROR;
    case 'DISPUTED':
      return COLORS.WARNING;
    default:
      return COLORS.PRIMARY;
  }
}

/**
 * Get trade state emoji
 */
export function getTradeStateEmoji(state: string): string {
  switch (state) {
    case 'PROPOSED':
      return EMOJIS.TRADE.PENDING;
    case 'ACCEPTED':
    case 'ACTIVE':
      return EMOJIS.TRADE.ACTIVE;
    case 'COMPLETED':
      return EMOJIS.TRADE.COMPLETED;
    case 'CANCELLED':
      return EMOJIS.TRADE.CANCELLED;
    case 'EXPIRED':
      return EMOJIS.TRADE.EXPIRED;
    case 'DISPUTED':
      return EMOJIS.TRADE.DISPUTED;
    default:
      return EMOJIS.MISC.CLOCK;
  }
}
