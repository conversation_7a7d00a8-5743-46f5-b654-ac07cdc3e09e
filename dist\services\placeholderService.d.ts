import { GuildMember, Guild, Role, User } from 'discord.js';
export interface PlaceholderContext {
    user: User;
    member: GuildMember;
    guild: Guild;
    role?: Role;
    memberCount?: number;
    botCount?: number;
    humanCount?: number;
}
/**
 * Available placeholders for welcome messages
 */
export declare const AVAILABLE_PLACEHOLDERS: {
    '{user}': string;
    '{user.name}': string;
    '{user.displayName}': string;
    '{user.id}': string;
    '{user.tag}': string;
    '{user.avatar}': string;
    '{user.createdAt}': string;
    '{user.accountAge}': string;
    '{server}': string;
    '{server.name}': string;
    '{server.id}': string;
    '{server.memberCount}': string;
    '{server.humanCount}': string;
    '{server.botCount}': string;
    '{server.icon}': string;
    '{server.banner}': string;
    '{role}': string;
    '{role.name}': string;
    '{role.id}': string;
    '{role.color}': string;
    '{role.memberCount}': string;
    '{date}': string;
    '{time}': string;
    '{timestamp}': string;
    '{joinedAt}': string;
    '{newline}': string;
    '{space}': string;
};
/**
 * Processes placeholders in text content
 */
export declare function processPlaceholders(text: string, context: PlaceholderContext): string;
/**
 * Validates that a text contains only valid placeholders
 */
export declare function validatePlaceholders(text: string): {
    valid: boolean;
    invalidPlaceholders: string[];
};
/**
 * Gets a formatted list of available placeholders for help text
 */
export declare function getPlaceholderHelp(): string;
/**
 * Processes placeholders in an entire welcome template
 */
export declare function processTemplatePlaceholders(template: any, context: PlaceholderContext): any;
//# sourceMappingURL=placeholderService.d.ts.map