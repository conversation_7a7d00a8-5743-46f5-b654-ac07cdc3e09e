import { Document } from 'mongoose';
export interface ITaxConfiguration extends Document {
    guildId: string;
    enabled: boolean;
    frequency: number;
    amount: number;
    roleId: string;
    roleName: string;
    lastTaxDate?: Date;
    nextTaxDate?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare const TaxConfiguration: import("mongoose").Model<ITaxConfiguration, {}, {}, {}, Document<unknown, {}, ITaxConfiguration, {}> & ITaxConfiguration & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=TaxConfiguration.d.ts.map