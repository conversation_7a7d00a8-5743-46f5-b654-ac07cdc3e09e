/**
 * Transaction Manager
 * Handles transaction history and record management
 */
import { TransactionRecord, TransactionType, ILogger } from '../../../core/interfaces';
/**
 * Transaction management operations
 */
export declare class TransactionManager {
    private logger;
    constructor(logger: ILogger);
    /**
     * Get transaction history for a user
     */
    getTransactionHistory(discordId: string, limit?: number): Promise<TransactionRecord[]>;
    /**
     * Get transactions by type
     */
    getTransactionsByType(discordId: string, type: TransactionType, limit?: number): Promise<TransactionRecord[]>;
    /**
     * Get transaction statistics for a user
     */
    getTransactionStats(discordId: string): Promise<any>;
    /**
     * Get recent transactions across all users
     */
    getRecentTransactions(limit?: number): Promise<TransactionRecord[]>;
    /**
     * Delete old transactions (cleanup)
     */
    deleteOldTransactions(olderThanDays: number): Promise<number>;
    /**
     * Validate transaction parameters
     */
    private validateTransactionParams;
    /**
     * Log operation
     */
    private logOperation;
    /**
     * Handle errors
     */
    private handleError;
}
//# sourceMappingURL=TransactionManager.d.ts.map