"use strict";
/**
 * Admin Commands Module
 * Exports all admin-related commands
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.incomecredentials = exports.testcleanup = exports.starterbalance = exports.tax = exports.announce = exports.fine = exports.give = exports.GiveCommand = void 0;
var GiveCommand_1 = require("./GiveCommand");
Object.defineProperty(exports, "GiveCommand", { enumerable: true, get: function () { return GiveCommand_1.GiveCommand; } });
// Re-export legacy commands for backward compatibility
exports.give = require('../give');
exports.fine = require('../fine');
exports.announce = require('../announce');
exports.tax = require('../tax');
exports.starterbalance = require('../starterbalance');
exports.testcleanup = require('../testcleanup');
exports.incomecredentials = require('../incomecredentials');
//# sourceMappingURL=index.js.map