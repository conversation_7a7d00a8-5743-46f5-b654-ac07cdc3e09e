"use strict";
/**
 * Economy Service
 * Refactored economy service with improved architecture
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EconomyService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const discord_js_1 = require("discord.js");
const BaseService_1 = require("../base/BaseService");
const errorHandler_1 = require("../../utils/errorHandler");
const features_1 = require("../../config/features");
const User_1 = __importDefault(require("../../models/User"));
const Transaction_1 = __importDefault(require("../../models/Transaction"));
/**
 * Economy service implementation
 */
class EconomyService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'EconomyService';
    }
    /**
     * Initialize the economy service
     */
    async onInitialize() {
        if (!this.isFeatureEnabled('ECONOMY_SYSTEM')) {
            throw new Error('Economy system is not enabled');
        }
        this.logger.info('[EconomyService] Economy system initialized');
    }
    /**
     * Adjust user balance with transaction logging
     */
    async adjustBalance(discordId, amount, type, details, client, guildId, dynastyId) {
        // Input validation
        this.validateAdjustBalanceInput(discordId, amount, type);
        if (mongoose_1.default.connection.readyState !== 1) {
            throw new errorHandler_1.DatabaseError('Database is not connected. Please try again in a moment.');
        }
        const session = await mongoose_1.default.startSession();
        this.logOperation('Starting balance adjustment transaction', { discordId, amount, type, details });
        try {
            await session.withTransaction(async () => {
                // Validate discordId before database operation
                const trimmedDiscordId = discordId.trim();
                if (!trimmedDiscordId) {
                    throw new Error('Discord ID cannot be empty after trimming');
                }
                // Atomic update or insert
                const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId }, {
                    $inc: { balance: amount },
                    $setOnInsert: { discordId: trimmedDiscordId }
                }, {
                    new: true,
                    upsert: true,
                    runValidators: true,
                    session
                });
                this.logOperation('Creating transaction record', {
                    discordId: trimmedDiscordId,
                    type,
                    amount,
                    details,
                    dynastyId
                });
                // Create transaction record in same transaction
                await Transaction_1.default.create([{
                        discordId: trimmedDiscordId,
                        type,
                        amount,
                        details,
                        dynastyId,
                        timestamp: new Date()
                    }], { session });
                this.logOperation('Transaction complete', {
                    userId: user?._id,
                    newBalance: user?.balance
                });
                // Check for role achievements if balance increased and we have client/guild info
                if (amount > 0 && client && guildId && user) {
                    // Schedule role checking after transaction completes
                    setImmediate(async () => {
                        try {
                            await this.checkRoleAchievements(client, trimmedDiscordId, guildId, user.balance);
                        }
                        catch (error) {
                            this.handleError(error, { operation: 'role_achievement_check' });
                        }
                    });
                }
            });
        }
        catch (error) {
            this.handleError(error, { discordId, amount, type });
            throw new errorHandler_1.DatabaseError(`Failed to adjust balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        finally {
            await session.endSession();
        }
    }
    /**
     * Get user balance
     */
    async getBalance(discordId) {
        try {
            const user = await this.ensureUser(discordId);
            return user.balance;
        }
        catch (error) {
            this.handleError(error, { discordId });
            throw new errorHandler_1.DatabaseError(`Failed to get balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get leaderboard
     */
    async getLeaderboard(guildId, limit = 10) {
        try {
            const users = await User_1.default.find({})
                .sort({ balance: -1 })
                .limit(limit)
                .lean();
            return users.map((user, index) => ({
                discordId: user.discordId,
                balance: user.balance,
                rank: index + 1,
            }));
        }
        catch (error) {
            this.handleError(error, { guildId, limit });
            throw new errorHandler_1.DatabaseError(`Failed to get leaderboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get transaction history
     */
    async getTransactionHistory(discordId, limit = 20) {
        try {
            const transactions = await Transaction_1.default.find({ discordId })
                .sort({ timestamp: -1 })
                .limit(limit)
                .lean();
            return transactions.map(tx => ({
                id: tx._id.toString(),
                discordId: tx.discordId,
                type: tx.type,
                amount: tx.amount,
                details: tx.details,
                timestamp: tx.timestamp,
            }));
        }
        catch (error) {
            this.handleError(error, { discordId, limit });
            throw new errorHandler_1.DatabaseError(`Failed to get transaction history: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Ensure user exists in database
     */
    async ensureUser(discordId) {
        try {
            const trimmedDiscordId = discordId.trim();
            if (!trimmedDiscordId) {
                throw new Error('Discord ID cannot be empty');
            }
            const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId }, { $setOnInsert: { discordId: trimmedDiscordId, balance: 0 } }, { new: true, upsert: true, runValidators: true });
            return user;
        }
        catch (error) {
            this.handleError(error, { discordId });
            throw new errorHandler_1.DatabaseError(`Failed to ensure user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Validate adjust balance input
     */
    validateAdjustBalanceInput(discordId, amount, type) {
        if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
            throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
        }
        if (typeof amount !== 'number' || isNaN(amount)) {
            throw new errorHandler_1.DatabaseError('Invalid amount provided');
        }
        if (!type) {
            throw new errorHandler_1.DatabaseError('Transaction type is required');
        }
    }
    /**
     * Check and assign role achievements
     */
    async checkRoleAchievements(client, discordId, guildId, balance) {
        try {
            // Use the service locator to get the RoleService instance at runtime.
            // This correctly avoids the circular dependency issue at the module-import level.
            const roleService = this.getService('RoleService');
            if (!roleService) {
                this.logger.warn('[EconomyService] RoleService not available for achievement check.');
                return;
            }
            const roleResult = await roleService.checkAndAssignRoles(client, discordId, guildId, balance);
            if (roleResult) {
                // Note: `sendRoleAchievementNotifications` should be a public method on RoleService.
                await roleService.sendRoleAchievementNotifications(roleResult, client);
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'role_achievement_check', discordId, guildId, balance });
        }
    }
}
exports.EconomyService = EconomyService;
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, String, String, discord_js_1.Client, String, String]),
    __metadata("design:returntype", Promise)
], EconomyService.prototype, "adjustBalance", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EconomyService.prototype, "getBalance", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], EconomyService.prototype, "getLeaderboard", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], EconomyService.prototype, "getTransactionHistory", null);
//# sourceMappingURL=EconomyService.js.map