{"version": 3, "file": "MessageHandler.js", "sourceRoot": "", "sources": ["../../../src/legacy/events/MessageHandler.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,sEAA4E;AAE5E;;GAEG;AACH,MAAa,oBAAoB;IAG/B,YAAY,MAAc;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,OAAgB;QACxC,IAAI,CAAC;YACH,8BAA8B;YAC9B,IAAI,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;gBAAE,OAAO;YAEjD,sBAAsB;YACtB,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAErC,wCAAwC;YACxC,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAE3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAC<PERSON>,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,OAAgB;QAC7C,IAAI,CAAC;YACH,4BAA4B;YAC5B,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAK,CAAC,CAAC;YAC7D,IAAI,YAAY,EAAE,CAAC;gBACjB,6BAA6B;gBAC7B,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACzB,OAAO,CAAC,GAAG,CAAC,sCAAsC,OAAO,CAAC,KAAK,EAAE,IAAI,0BAA0B,CAAC,CAAC;YACnG,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,OAAgB;QACjD,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAA,4CAAyB,EACtD,IAAI,CAAC,MAAM,EACX,OAAO,CAAC,MAAM,CAAC,EAAE,EACjB,OAAO,CAAC,KAAM,CAAC,EAAE,EACjB,SAAS,EACT;gBACE,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC7B,cAAc,EAAE,OAAO,CAAC,OAAO;gBAC/B,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;gBACrC,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B,CACF,CAAC;YAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,OAAO,CAAC,GAAG,CAAC,oBAAoB,OAAO,CAAC,MAAM,CAAC,GAAG,aAAa,gBAAgB,CAAC,MAAM,qCAAqC,CAAC,CAAC;YAC/H,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO;YACL,WAAW,EAAE,sBAAsB;YACnC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YAClC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;SAC1C,CAAC;IACJ,CAAC;CACF;AA/ED,oDA+EC;AAED,kBAAe,oBAAoB,CAAC"}