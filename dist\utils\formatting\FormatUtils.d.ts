/**
 * Formatting Utilities
 * Centralized formatting functions for consistent display
 */
import { User, GuildMember } from 'discord.js';
/**
 * Currency formatting utilities
 */
export declare class CurrencyFormatter {
    /**
     * Format coins with currency symbol and proper formatting
     */
    static formatCoins(amount: number): string;
    /**
     * Format coins with emoji
     */
    static formatCoinsWithEmoji(amount: number): string;
    /**
     * Format percentage
     */
    static formatPercentage(value: number, decimals?: number): string;
    /**
     * Format large numbers with suffixes (K, M, B)
     */
    static formatLargeNumber(num: number): string;
}
/**
 * Time formatting utilities
 */
export declare class TimeFormatter {
    /**
     * Format duration in milliseconds to human readable
     */
    static formatDuration(ms: number): string;
    /**
     * Format timestamp to relative time
     */
    static formatRelativeTime(date: Date): string;
    /**
     * Format date to Discord timestamp
     */
    static formatDiscordTimestamp(date: Date, style?: 't' | 'T' | 'd' | 'D' | 'f' | 'F' | 'R'): string;
}
/**
 * User formatting utilities
 */
export declare class UserFormatter {
    /**
     * Format user display name without mentions
     */
    static formatDisplayName(user: User | GuildMember): string;
    /**
     * Format user tag (username#discriminator)
     */
    static formatUserTag(user: User): string;
    /**
     * Format user mention (for intentional pings)
     */
    static formatUserMention(user: User): string;
    /**
     * Format user info for embeds
     */
    static formatUserInfo(user: User, includeId?: boolean): string;
}
/**
 * List formatting utilities
 */
export declare class ListFormatter {
    /**
     * Format array as numbered list
     */
    static formatNumberedList(items: string[], startIndex?: number): string;
    /**
     * Format array as bulleted list
     */
    static formatBulletedList(items: string[], bullet?: string): string;
    /**
     * Format leaderboard entries
     */
    static formatLeaderboard(entries: Array<{
        rank: number;
        name: string;
        value: string;
    }>): string;
    /**
     * Get medal emoji for rank
     */
    private static getRankMedal;
    /**
     * Format key-value pairs
     */
    static formatKeyValuePairs(pairs: Record<string, string>, separator?: string): string;
}
/**
 * Text formatting utilities
 */
export declare class TextFormatter {
    /**
     * Truncate text with ellipsis
     */
    static truncate(text: string, maxLength: number, suffix?: string): string;
    /**
     * Capitalize first letter
     */
    static capitalize(text: string): string;
    /**
     * Convert to title case
     */
    static toTitleCase(text: string): string;
    /**
     * Format code block
     */
    static formatCodeBlock(code: string, language?: string): string;
    /**
     * Format inline code
     */
    static formatInlineCode(code: string): string;
    /**
     * Format bold text
     */
    static formatBold(text: string): string;
    /**
     * Format italic text
     */
    static formatItalic(text: string): string;
    /**
     * Format strikethrough text
     */
    static formatStrikethrough(text: string): string;
    /**
     * Format spoiler text
     */
    static formatSpoiler(text: string): string;
}
/**
 * Progress formatting utilities
 */
export declare class ProgressFormatter {
    /**
     * Create progress bar
     */
    static createProgressBar(current: number, max: number, length?: number, filledChar?: string, emptyChar?: string): string;
    /**
     * Format progress with percentage
     */
    static formatProgress(current: number, max: number): string;
}
/**
 * Comprehensive formatting utility
 */
export declare class FormatUtils {
    static currency: typeof CurrencyFormatter;
    static time: typeof TimeFormatter;
    static user: typeof UserFormatter;
    static list: typeof ListFormatter;
    static text: typeof TextFormatter;
    static progress: typeof ProgressFormatter;
    /**
     * Format coins (shorthand)
     */
    static formatCoins: typeof CurrencyFormatter.formatCoins;
    /**
     * Format user display name (shorthand)
     */
    static formatDisplayName: typeof UserFormatter.formatDisplayName;
    /**
     * Format relative time (shorthand)
     */
    static formatRelativeTime: typeof TimeFormatter.formatRelativeTime;
}
export default FormatUtils;
//# sourceMappingURL=FormatUtils.d.ts.map