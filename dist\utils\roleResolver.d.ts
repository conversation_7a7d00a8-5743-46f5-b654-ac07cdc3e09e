import { Guild, Role } from 'discord.js';
export interface RoleResolutionResult {
    role: Role;
    resolvedBy: 'id' | 'exact_name' | 'fuzzy_name';
    confidence: number;
}
/**
 * Resolves a role by ID or name within a guild
 * Supports exact matching, case-insensitive matching, and fuzzy matching
 */
export declare function resolveRole(guild: Guild, input: string): Promise<RoleResolutionResult>;
/**
 * Validates that a role exists in the guild and can be managed by the bot
 */
export declare function validateRolePermissions(guild: Guild, role: Role): void;
//# sourceMappingURL=roleResolver.d.ts.map