/**
 * Legacy Client Manager
 * Extracted Discord client setup from monolithic index.ts
 */

import { Client, GatewayIntentBits, Collection } from 'discord.js';
import fs from 'fs';
import path from 'path';
import MemoryManager from '../../services/memoryManager';

/**
 * Discord client manager for legacy compatibility
 */
export class LegacyClientManager {
  private client: Client;
  private memoryManager: MemoryManager;

  constructor() {
    this.client = this.createClient();
    this.memoryManager = MemoryManager.getInstance();
  }

  /**
   * Create Discord client with proper intents
   */
  private createClient(): Client {
    const client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessageReactions
      ]
    });

    // Add commands collection for legacy compatibility
    (client as any).commands = new Collection();

    return client;
  }

  /**
   * Load commands from the commands directory
   */
  loadCommands(): void {
    const commandsPath = path.join(__dirname, '../../commands');
    
    if (!fs.existsSync(commandsPath)) {
      console.warn(`Commands directory not found: ${commandsPath}`);
      return;
    }

    const commandFiles = fs.readdirSync(commandsPath).filter(file => 
      file.endsWith('.js') || file.endsWith('.ts')
    );

    let loadedCount = 0;

    for (const file of commandFiles) {
      try {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);
        
        if (command.data && command.execute) {
          (this.client as any).commands.set(command.data.name, command);
          loadedCount++;
          console.log(`[Legacy Client] Loaded command: ${command.data.name}`);
        } else {
          console.warn(`[Legacy Client] Invalid command file: ${file}`);
        }
      } catch (error) {
        console.error(`[Legacy Client] Failed to load command file: ${file}`, error);
      }
    }

    console.log(`[Legacy Client] Loaded ${loadedCount} commands`);
  }

  /**
   * Get the Discord client instance
   */
  getClient(): Client {
    return this.client;
  }

  /**
   * Get memory manager instance
   */
  getMemoryManager(): MemoryManager {
    return this.memoryManager;
  }

  /**
   * Login to Discord
   */
  async login(): Promise<void> {
    try {
      const token = process.env.BOT_TOKEN;
      if (!token) {
        throw new Error('BOT_TOKEN environment variable is not set');
      }

      await this.client.login(token);
      console.log(`[Legacy Client] Logged in as ${this.client.user?.tag}`);
    } catch (error) {
      console.error('[Legacy Client] Failed to login:', error);
      throw error;
    }
  }

  /**
   * Destroy the client connection
   */
  destroy(): void {
    this.client.destroy();
    console.log('[Legacy Client] Client destroyed');
  }

  /**
   * Check if client is ready
   */
  isReady(): boolean {
    return this.client.isReady();
  }

  /**
   * Get client uptime in milliseconds
   */
  getUptime(): number | null {
    return this.client.uptime;
  }

  /**
   * Get guild count
   */
  getGuildCount(): number {
    return this.client.guilds.cache.size;
  }

  /**
   * Get user count across all guilds
   */
  getUserCount(): number {
    return this.client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);
  }
}

export default LegacyClientManager;
