import { Client, GuildMember } from 'discord.js';
interface RoleAssignmentResult {
    rolesAssigned: Array<{
        roleId: string;
        roleName: string;
        price: number;
        description?: string;
    }>;
    member: GuildMember;
    newBalance: number;
}
/**
 * Checks if a user qualifies for any new role achievements and assigns them automatically
 * @param client Discord client instance
 * @param discordId User's Discord ID
 * @param guildId Guild ID where roles should be assigned
 * @param newBalance User's new PLC balance
 * @returns Promise<RoleAssignmentResult | null> - Result of role assignments or null if no roles assigned
 */
export declare function checkAndAssignRoles(client: Client, discordId: string, guildId: string, newBalance: number): Promise<RoleAssignmentResult | null>;
/**
 * Sends congratulatory messages to users when they unlock role achievements
 * @param result Role assignment result
 * @param client Discord client instance
 */
export declare function sendRoleAchievementNotifications(result: RoleAssignmentResult, client: Client): Promise<void>;
/**
 * Gets all roles a user currently has that are achievements
 * @param member Guild member
 * @returns Array of role achievement info
 */
export declare function getUserAchievementRoles(member: GuildMember): Promise<Array<{
    roleId: string;
    roleName: string;
    price: number;
    description?: string;
}>>;
export {};
//# sourceMappingURL=roleAssignmentService.d.ts.map