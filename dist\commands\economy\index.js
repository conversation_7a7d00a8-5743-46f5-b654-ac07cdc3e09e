"use strict";
/**
 * Economy Commands Module
 * Exports all economy-related commands
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.leaderboard = exports.history = exports.pay = exports.balance = exports.PayCommand = exports.BalanceCommand = void 0;
var BalanceCommand_1 = require("./BalanceCommand");
Object.defineProperty(exports, "BalanceCommand", { enumerable: true, get: function () { return BalanceCommand_1.BalanceCommand; } });
var PayCommand_1 = require("./PayCommand");
Object.defineProperty(exports, "PayCommand", { enumerable: true, get: function () { return PayCommand_1.PayCommand; } });
// Re-export legacy commands for backward compatibility
exports.balance = require('../balance');
exports.pay = require('../pay');
exports.history = require('../history');
exports.leaderboard = require('../leaderboard');
//# sourceMappingURL=index.js.map