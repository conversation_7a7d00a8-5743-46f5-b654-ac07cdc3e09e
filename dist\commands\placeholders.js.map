{"version": 3, "file": "placeholders.js", "sourceRoot": "", "sources": ["../../src/commands/placeholders.ts"], "names": [], "mappings": ";;AAAA,2CAA8E;AAC9E,wDAA0E;AAC1E,wDAAiE;AAEjE,MAAM,CAAC,OAAO,GAAG;IACb,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC1B,OAAO,CAAC,cAAc,CAAC;SACvB,cAAc,CAAC,oDAAoD,CAAC;IAEzE,OAAO,EAAE,IAAA,+BAAgB,EAAC,KAAK,EAAE,WAAwC,EAAE,EAAE;QACzE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACrB,MAAM,IAAI,8BAAe,CAAC,4CAA4C,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,KAAK,GAAG,IAAA,+BAAgB,EAAC,wBAAwB,CAAC;aACnD,cAAc,CAAC,GAAG,qBAAM,CAAC,KAAK,CAAC,IAAI,kMAAkM,CAAC;aACtO,SAAS,CACN;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,IAAI,oBAAoB;YAC7C,KAAK,EACD,oCAAoC;gBACpC,sEAAsE;gBACtE,yCAAyC;gBACzC,0DAA0D;gBAC1D,qCAAqC;gBACrC,2DAA2D;YAC/D,MAAM,EAAE,KAAK;SAChB,EACD;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,KAAK,sBAAsB;YAChD,KAAK,EACD,8BAA8B;gBAC9B,mCAAmC;gBACnC,+BAA+B;gBAC/B,+CAA+C;YACnD,MAAM,EAAE,KAAK;SAChB,EACD;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,IAAI,oBAAoB;YAC7C,KAAK,EACD,8DAA8D;gBAC9D,+BAA+B;gBAC/B,2BAA2B;gBAC3B,wDAAwD;gBACxD,4EAA4E;YAChF,MAAM,EAAE,KAAK;SAChB,EACD;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,KAAK,yBAAyB;YACnD,KAAK,EACD,0CAA0C;gBAC1C,8CAA8C;gBAC9C,wCAAwC;YAC5C,MAAM,EAAE,KAAK;SAChB,EACD;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,IAAI,CAAC,SAAS,iBAAiB;YAC/C,KAAK,EACD,wBAAwB;gBACxB,mCAAmC;gBACnC,0EAA0E;gBAC1E,mBAAmB;gBACnB,+BAA+B;gBAC/B,6EAA6E;gBAC7E,uBAAuB;gBACvB,2DAA2D;gBAC3D,wBAAwB;gBACxB,4CAA4C;YAChD,MAAM,EAAE,KAAK;SAChB,EACD;YACI,IAAI,EAAE,GAAG,qBAAM,CAAC,KAAK,CAAC,QAAQ,mBAAmB;YACjD,KAAK,EACD,qEAAqE;gBACrE,qEAAqE;gBACrE,kEAAkE;YACtE,MAAM,EAAE,KAAK;SAChB,CACJ,CAAC;QAEN,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;IACnE,CAAC,CAAC;CACL,CAAC"}