import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { withError<PERSON>and<PERSON>, ValidationError, PermissionError } from '../utils/errorHandler';
import { createAdminEmbed, createSuc<PERSON>Embed, EMOJIS, COLORS } from '../utils/embedBuilder';
import { setIncomeGuideText } from '../services/incomeGuideService';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('incomecredentials')
        .setDescription('Customize the income earning guide text displayed in /help command (admin only)')
        .addStringOption(option => 
            option.setName('text')
                .setDescription('Custom text explaining how users can earn Phalanx Loyalty Coins')
                .setRequired(true)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    
    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError();
        }

        const guild = interaction.guild;
        if (!guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        const customText = interaction.options.getString('text', true);

        // Validate text length
        if (customText.length > 1000) {
            throw new ValidationError('Income guide text cannot exceed 1000 characters. Please shorten your text.');
        }

        if (customText.trim().length === 0) {
            throw new ValidationError('Income guide text cannot be empty.');
        }

        // Process the text to handle line breaks (for preview purposes)
        const processedText = customText
            .replace(/\\n/g, '\n')
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            .trim();

        // Show initial response
        const loadingEmbed = createAdminEmbed('Updating Income Guide')
            .setDescription(`${EMOJIS.MISC.CLOCK} Updating the income earning guide...`);

        await interaction.reply({
            embeds: [loadingEmbed],
            ephemeral: true
        });

        try {
            // Update the income guide text
            await setIncomeGuideText(guild.id, customText);

            // Create success embed with preview
            const successEmbed = createSuccessEmbed('Income Guide Updated Successfully')
                .setDescription(
                    `${EMOJIS.SUCCESS.CHECK} **Income earning guide has been updated!**\n\n` +
                    `The new text will now appear in all \`/help\` command responses with proper formatting.`
                )
                .addFields(
                    {
                        name: `${EMOJIS.MISC.SCROLL} Formatted Text Preview`,
                        value: processedText.length > 500 ?
                            `${processedText.substring(0, 500)}...\n\n*Text truncated for display*` :
                            processedText,
                        inline: false
                    },
                    {
                        name: `${EMOJIS.MISC.SCROLL} Text Statistics`,
                        value: `**Length:** ${customText.length}/1000 characters\n**Lines:** ${processedText.split('\n').length}\n**Line breaks:** ${(processedText.match(/\n/g) || []).length}`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.MISC.CLOCK} Updated`,
                        value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                        inline: true
                    }
                )
                .setFooter({
                    text: 'Line breaks (\\n) and empty lines are preserved for better formatting'
                });

            await interaction.editReply({
                embeds: [successEmbed]
            });

            // Log the admin action
            console.log(`[IncomeCredentials] Income guide updated by ${interaction.user.tag} (${interaction.user.id}) in ${guild.name} (${guild.id})`);
            console.log(`[IncomeCredentials] New text length: ${customText.length} characters`);

        } catch (error) {
            console.error(`[IncomeCredentials] Error updating income guide:`, error);
            throw error; // Re-throw to be handled by error handler
        }
    })
};
