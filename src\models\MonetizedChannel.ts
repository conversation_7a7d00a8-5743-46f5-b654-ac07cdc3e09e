import { Schema, model, Document } from 'mongoose';

export interface IMonetizedChannel extends Document {
    channelId: string;
    enabled: boolean;
    createdAt: Date;
    updatedAt: Date;
}

const monetizedChannelSchema = new Schema<IMonetizedChannel>({
    channelId: {
        type: String,
        required: [true, 'Channel ID is required'],
        unique: true,
        validate: {
            validator: function(v: string): boolean {
                return !!(v && v.trim().length > 0);
            },
            message: 'Channel ID cannot be empty'
        }
    },
    enabled: {
        type: Boolean,
        default: true,
        required: true
    }
}, {
    timestamps: true
});

// Index for efficient channel lookups
monetizedChannelSchema.index({ channelId: 1 });

export const MonetizedChannel = model<IMonetizedChannel>('MonetizedChannel', monetizedChannelSchema);
