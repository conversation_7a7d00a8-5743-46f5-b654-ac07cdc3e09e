"use strict";
/**
 * Legacy Message Handler
 * Extracted message handling from monolithic index.ts
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyMessageHandler = void 0;
const milestoneService_1 = require("../../services/milestoneService");
/**
 * Message handler for legacy compatibility
 */
class LegacyMessageHandler {
    constructor(client) {
        this.client = client;
    }
    /**
     * Handle message create events
     */
    async handleMessageCreate(message) {
        try {
            // Ignore bot messages and DMs
            if (message.author.bot || !message.guild)
                return;
            // Handle bot mentions
            await this.handleBotMention(message);
            // Track message activity for milestones
            await this.trackMessageActivity(message);
        }
        catch (error) {
            console.error('[Message Handler] Error in messageCreate handler:', error);
        }
    }
    /**
     * Handle bot mentions with reaction
     */
    async handleBotMention(message) {
        try {
            // Check if bot is mentioned
            const botMentioned = message.mentions.has(this.client.user);
            if (botMentioned) {
                // React with checkmark emoji
                await message.react('✅');
                console.log(`[Message Handler] Bot mentioned in ${message.guild?.name}, reacted with checkmark`);
            }
        }
        catch (error) {
            console.error('[Message Handler] Failed to react to bot mention:', error);
        }
    }
    /**
     * Track message activity for milestones
     */
    async trackMessageActivity(message) {
        try {
            const milestoneResults = await (0, milestoneService_1.checkAndProcessMilestones)(this.client, message.author.id, message.guild.id, 'message', {
                channelId: message.channel.id,
                messageContent: message.content,
                messageLength: message.content.length,
                timestamp: message.createdAt
            });
            if (milestoneResults.length > 0) {
                console.log(`[Milestone] User ${message.author.tag} achieved ${milestoneResults.length} milestone(s) from message activity`);
            }
        }
        catch (error) {
            console.error('[Milestone] Error processing message milestones:', error);
        }
    }
    /**
     * Get message statistics
     */
    getStats() {
        return {
            handlerType: 'LegacyMessageHandler',
            clientReady: this.client.isReady(),
            guildCount: this.client.guilds.cache.size,
        };
    }
}
exports.LegacyMessageHandler = LegacyMessageHandler;
exports.default = LegacyMessageHandler;
//# sourceMappingURL=MessageHandler.js.map