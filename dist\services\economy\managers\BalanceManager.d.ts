/**
 * Balance Manager
 * Handles balance adjustments and transaction processing
 */
import { Client } from 'discord.js';
import { TransactionType, ILogger } from '../../../core/interfaces';
/**
 * Balance management operations
 */
export declare class BalanceManager {
    private logger;
    constructor(logger: ILogger);
    /**
     * Adjust user balance with transaction logging
     */
    adjustBalance(discordId: string, amount: number, type: TransactionType, details?: string, client?: Client, guildId?: string, dynastyId?: string): Promise<void>;
    /**
     * Get user balance
     */
    getBalance(discordId: string): Promise<number>;
    /**
     * Ensure user exists in database
     */
    private ensureUser;
    /**
     * Check and assign role achievements
     */
    private checkRoleAchievements;
    /**
     * Log operation
     */
    private logOperation;
    /**
     * Handle errors
     */
    private handleError;
}
//# sourceMappingURL=BalanceManager.d.ts.map