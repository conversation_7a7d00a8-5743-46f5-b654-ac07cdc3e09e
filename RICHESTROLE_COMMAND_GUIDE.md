# 👑 Richest Role Command Guide

The `/richestrole` command automatically assigns a designated role to the user with the highest Phalanx Loyalty Coin balance in your server.

## 🚀 Quick Start

### Basic Usage
```
/richestrole role:@VIP
```

This command will:
1. Find the user with the highest PLC balance
2. Remove the @VIP role from all current holders
3. Assign the @VIP role to the richest user
4. Display a detailed summary of the changes

## 📋 Command Reference

### `/richestrole` Command

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `role` | Role | ✅ | The role to assign to the richest member | `@VIP`, `@Richest Player`, `@Top Earner` |

**Permissions Required:** Administrator only

## 🎯 How It Works

### 1. User Discovery
- Queries the database for all users with PLC balances
- Sorts users by balance in descending order
- Selects the user with the highest balance

### 2. Server Membership Validation
- Verifies the richest user is still in the server
- If not, finds the next richest user who is in the server
- Continues until a valid member is found

### 3. Role Management
- Removes the specified role from all current holders
- Assigns the role to the richest eligible member
- Uses Discord's role hierarchy validation

### 4. Detailed Reporting
- Shows the new role holder and their balance
- Lists previous role holders (up to 5 displayed)
- Provides role assignment statistics

## 📊 Example Output

```
👑 Richest Role Assigned

🎉 Role Updated!
The "VIP" role has been assigned to the richest member.

🪙 New Role Holder          ⚙️ Role Information
TestUser                    Role: VIP
Balance: 🪙 2,500 PLC      Previous Holders: 2
                           Action: Role reassigned

ℹ️ Previous Holders
OldVIP1, OldVIP2
```

## 🔧 Edge Cases Handled

### No Eligible Users
```
⚠️ No Eligible Users

⚠️ No Users Found
No users with Phalanx Loyalty Coins found in the database.

💡 Suggestion
Users need to have earned at least 1 PLC to be eligible for this role.
```

### Richest User Not in Server
The command automatically finds the next richest user who is currently in the server.

### Tied Balances
When multiple users have the same highest balance, the command selects the first user found in the database query.

### Role Permission Issues
The command validates that the bot can manage the specified role before attempting assignment.

## ⚠️ Important Notes

### Role Hierarchy
- The bot must have a role higher than the target role
- The bot needs "Manage Roles" permission
- The target role cannot be higher than the bot's highest role

### Database Requirements
- Users must have earned at least 1 PLC to be eligible
- Only users currently in the server can receive the role
- The command uses the most up-to-date balance information

### Automatic Updates
- The command does not automatically re-run when balances change
- Run the command manually whenever you want to update the role assignment
- Consider setting up regular role updates if needed

## 🎯 Use Cases

### 1. VIP Status Role
```
/richestrole role:@VIP Member
```
Assign VIP status to your richest member as a prestige reward.

### 2. Economic Leader Role
```
/richestrole role:@Economic Leader
```
Recognize the top earner in your economy system.

### 3. Seasonal Competitions
```
/richestrole role:@Season Champion
```
Award seasonal roles based on accumulated wealth.

### 4. Guild Leadership
```
/richestrole role:@Treasurer
```
Assign leadership roles based on economic contribution.

## 🚨 Troubleshooting

### Common Issues

1. **"Bot does not have permission to manage roles"**
   - Ensure the bot has "Manage Roles" permission
   - Check that the bot's role is higher than the target role

2. **"No eligible users found"**
   - Verify users have earned PLC through the economy system
   - Check that eligible users are still in the server

3. **"Role not found"**
   - Ensure the role exists and is spelled correctly
   - Use the role picker in the command interface

4. **Role assignment fails**
   - Check Discord's role hierarchy rules
   - Verify the target role is not an integrated role (like @everyone)

### Getting Help
- Use `/help` to see all available commands
- Check the bot's error messages for specific guidance
- Ensure all prerequisites are met before running the command

## 🔒 Security & Permissions

- **Administrator Only**: Only users with Administrator permissions can use this command
- **Role Validation**: The bot validates all role operations before execution
- **Safe Operations**: Failed operations don't affect existing role assignments
- **Audit Trail**: All role changes are logged for administrative review

---

The `/richestrole` command provides a powerful way to automatically recognize and reward your server's most economically successful members with special roles and privileges.
