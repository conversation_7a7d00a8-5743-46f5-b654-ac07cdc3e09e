import { Document } from 'mongoose';
export interface IMilestoneAchievement extends Document {
    discordId: string;
    guildId: string;
    milestoneType: string;
    category: 'time_based' | 'participation_diversity' | 'loyalty' | 'engagement';
    achievementValue: number;
    rewardAmount: number;
    diminishingFactor: number;
    achievementCount: number;
    details: string;
    timestamp: Date;
    weekNumber: number;
    dayOfYear: number;
    year: number;
    createdAt: Date;
}
declare const _default: import("mongoose").Model<IMilestoneAchievement, {}, {}, {}, Document<unknown, {}, IMilestoneAchievement, {}> & IMilestoneAchievement & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=MilestoneAchievement.d.ts.map