"use strict";
/**
 * Transaction Manager
 * Handles transaction history and record management
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionManager = void 0;
const errorHandler_1 = require("../../../utils/errorHandler");
const features_1 = require("../../../config/features");
const Transaction_1 = __importDefault(require("../../../models/Transaction"));
/**
 * Transaction management operations
 */
class TransactionManager {
    constructor(logger) {
        this.logger = logger;
    }
    /**
     * Get transaction history for a user
     */
    async getTransactionHistory(discordId, limit = 20) {
        try {
            this.validateTransactionParams(discordId, limit);
            const transactions = await Transaction_1.default.find({ discordId })
                .sort({ timestamp: -1 })
                .limit(limit)
                .lean();
            const history = transactions.map(tx => ({
                id: tx._id.toString(),
                discordId: tx.discordId,
                type: tx.type,
                amount: tx.amount,
                details: tx.details,
                timestamp: tx.timestamp,
            }));
            this.logOperation('Transaction history retrieved', {
                discordId,
                limit,
                transactionsCount: history.length
            });
            return history;
        }
        catch (error) {
            this.handleError(error, { discordId, limit });
            throw new errorHandler_1.DatabaseError(`Failed to get transaction history: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get transactions by type
     */
    async getTransactionsByType(discordId, type, limit = 20) {
        try {
            this.validateTransactionParams(discordId, limit);
            const transactions = await Transaction_1.default.find({
                discordId,
                type
            })
                .sort({ timestamp: -1 })
                .limit(limit)
                .lean();
            const history = transactions.map(tx => ({
                id: tx._id.toString(),
                discordId: tx.discordId,
                type: tx.type,
                amount: tx.amount,
                details: tx.details,
                timestamp: tx.timestamp,
            }));
            this.logOperation('Transactions by type retrieved', {
                discordId,
                type,
                limit,
                transactionsCount: history.length
            });
            return history;
        }
        catch (error) {
            this.handleError(error, { discordId, type, limit });
            throw new errorHandler_1.DatabaseError(`Failed to get transactions by type: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get transaction statistics for a user
     */
    async getTransactionStats(discordId) {
        try {
            if (!discordId || typeof discordId !== 'string') {
                throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
            }
            const trimmedDiscordId = discordId.trim();
            const stats = await Transaction_1.default.aggregate([
                { $match: { discordId: trimmedDiscordId } },
                {
                    $group: {
                        _id: '$type',
                        count: { $sum: 1 },
                        totalAmount: { $sum: '$amount' },
                        averageAmount: { $avg: '$amount' },
                        lastTransaction: { $max: '$timestamp' }
                    }
                }
            ]);
            const result = {
                totalTransactions: stats.reduce((sum, stat) => sum + stat.count, 0),
                byType: stats.reduce((acc, stat) => {
                    acc[stat._id] = {
                        count: stat.count,
                        totalAmount: stat.totalAmount,
                        averageAmount: stat.averageAmount,
                        lastTransaction: stat.lastTransaction
                    };
                    return acc;
                }, {})
            };
            this.logOperation('Transaction stats calculated', {
                discordId: trimmedDiscordId,
                totalTransactions: result.totalTransactions
            });
            return result;
        }
        catch (error) {
            this.handleError(error, { discordId });
            throw new errorHandler_1.DatabaseError(`Failed to get transaction stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get recent transactions across all users
     */
    async getRecentTransactions(limit = 50) {
        try {
            if (typeof limit !== 'number' || limit < 1 || limit > 100) {
                throw new errorHandler_1.DatabaseError('Limit must be a number between 1 and 100');
            }
            const transactions = await Transaction_1.default.find({})
                .sort({ timestamp: -1 })
                .limit(limit)
                .lean();
            const history = transactions.map(tx => ({
                id: tx._id.toString(),
                discordId: tx.discordId,
                type: tx.type,
                amount: tx.amount,
                details: tx.details,
                timestamp: tx.timestamp,
            }));
            this.logOperation('Recent transactions retrieved', {
                limit,
                transactionsCount: history.length
            });
            return history;
        }
        catch (error) {
            this.handleError(error, { limit });
            throw new errorHandler_1.DatabaseError(`Failed to get recent transactions: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Delete old transactions (cleanup)
     */
    async deleteOldTransactions(olderThanDays) {
        try {
            if (typeof olderThanDays !== 'number' || olderThanDays < 1) {
                throw new errorHandler_1.DatabaseError('Days must be a positive number');
            }
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
            const result = await Transaction_1.default.deleteMany({
                timestamp: { $lt: cutoffDate }
            });
            this.logOperation('Old transactions deleted', {
                olderThanDays,
                cutoffDate,
                deletedCount: result.deletedCount
            });
            return result.deletedCount;
        }
        catch (error) {
            this.handleError(error, { olderThanDays });
            throw new errorHandler_1.DatabaseError(`Failed to delete old transactions: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Validate transaction parameters
     */
    validateTransactionParams(discordId, limit) {
        if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
            throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
        }
        if (typeof limit !== 'number' || limit < 1 || limit > 100) {
            throw new errorHandler_1.DatabaseError('Limit must be a number between 1 and 100');
        }
    }
    /**
     * Log operation
     */
    logOperation(operation, details) {
        this.logger.debug(`[TransactionManager] ${operation}`, details);
    }
    /**
     * Handle errors
     */
    handleError(error, context) {
        this.logger.error('[TransactionManager] Error', {
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
            } : error,
            context,
        });
    }
}
exports.TransactionManager = TransactionManager;
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], TransactionManager.prototype, "getTransactionHistory", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number]),
    __metadata("design:returntype", Promise)
], TransactionManager.prototype, "getTransactionsByType", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TransactionManager.prototype, "getTransactionStats", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], TransactionManager.prototype, "getRecentTransactions", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], TransactionManager.prototype, "deleteOldTransactions", null);
//# sourceMappingURL=TransactionManager.js.map