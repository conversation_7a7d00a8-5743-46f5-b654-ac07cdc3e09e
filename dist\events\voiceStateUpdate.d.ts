/**
 * Voice State Update Event Handler
 * Handles Discord voice state update events for milestone tracking
 */
import { VoiceState } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';
/**
 * Voice state update event handler
 */
export declare class VoiceStateUpdateEventHandler extends BaseEventHandler {
    readonly name = "voiceStateUpdate";
    constructor(app: IApplicationContext);
    /**
     * Execute voice state update event
     */
    execute(oldState: VoiceState, newState: VoiceState): Promise<void>;
    /**
     * Track voice activity for milestones
     */
    private trackVoiceActivity;
}
//# sourceMappingURL=voiceStateUpdate.d.ts.map